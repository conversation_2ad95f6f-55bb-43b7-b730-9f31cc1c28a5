/* pages/category/category.wxss */
.container {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

/* 搜索栏样式 */
.search-container {
  padding: 20rpx 30rpx;
  background-color: #fff;
}

.search-box {
  display: flex;
  align-items: center;
  background-color: #f5f5f5;
  border-radius: 30rpx;
  padding: 10rpx 20rpx;
  position: relative;
}

.search-box icon {
  margin-right: 10rpx;
}

.search-box input {
  flex: 1;
  height: 60rpx;
  font-size: 28rpx;
  padding-right: 40rpx; /* 为清除按钮留出空间 */
}

.search-clear {
  position: absolute;
  right: 20rpx;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40rpx;
  height: 40rpx;
  z-index: 10;
}

/* 分类内容样式 */
.category-content {
  flex: 1;
  display: flex;
  overflow: hidden;
}

/* 搜索状态下的分类内容样式 */
.category-content.searching {
  display: block;
}

/* 左侧主分类样式 */
.main-category {
  width: 180rpx;
  height: 100%;
  background-color: #f5f5f5;
}

.category-item {
  height: 100rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 28rpx;
  color: #333;
  position: relative;
}

.category-item.active {
  background-color: #fff;
  color: #8ab6d6;
  font-weight: bold;
}

.category-item.active::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 8rpx;
  height: 30rpx;
  background-color: #8ab6d6;
  border-radius: 4rpx;
}

/* 右侧内容样式 */
.category-detail {
  flex: 1;
  height: 100%;
  background-color: #fff;
  padding: 20rpx;
}

/* 搜索状态下的右侧内容样式 */
.category-detail.full-width {
  width: 100%;
  box-sizing: border-box;
}

/* 子分类样式 */
.sub-category {
  display: flex;
  flex-wrap: wrap;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #eee;
  margin-bottom: 20rpx;
}

.sub-category-item {
  width: 150rpx;
  height: 60rpx;
  background-color: #f5f5f5;
  border-radius: 30rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 24rpx;
  color: #666;
  margin-right: 20rpx;
  margin-bottom: 20rpx;
}

.sub-category-item.active {
  background-color: #8ab6d6;
  color: #fff;
  font-weight: bold;
}

/* 商品列表样式 */
.product-list {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}

.product-item {
  width: 48%;
  background-color: #fff;
  border-radius: 10rpx;
  overflow: hidden;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.product-image {
  width: 100%;
  height: 300rpx;
}

.product-info {
  padding: 16rpx;
}

.product-name {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
}

.product-price-container {
  display: flex;
  align-items: baseline;
  margin-bottom: 10rpx;
}

.product-price {
  font-size: 32rpx;
  color: #ff6b81;
  font-weight: bold;
  margin-right: 10rpx;
}

.product-original-price {
  font-size: 24rpx;
  color: #999;
  text-decoration: line-through;
}

.product-sales {
  font-size: 22rpx;
  color: #999;
}

.product-category {
  font-size: 22rpx;
  color: #8ab6d6;
  margin-top: 6rpx;
  display: block;
}

/* 加载状态样式 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 30rpx 0;
}

.loading-icon {
  width: 40rpx;
  height: 40rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #8ab6d6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 10rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 24rpx;
  color: #999;
}

/* 空状态样式 */
.empty-container,
.empty-sub-category,
.empty-products {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40rpx 0;
}

.empty-text {
  font-size: 26rpx;
  color: #999;
  text-align: center;
}

/* 搜索结果提示样式 */
.search-result-tip {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  margin-bottom: 20rpx;
  border-bottom: 1rpx solid #eee;
}

.search-result-text {
  font-size: 26rpx;
  color: #666;
}

.search-result-clear {
  font-size: 24rpx;
  color: #8ab6d6;
  padding: 6rpx 16rpx;
  border: 1rpx solid #8ab6d6;
  border-radius: 30rpx;
}
