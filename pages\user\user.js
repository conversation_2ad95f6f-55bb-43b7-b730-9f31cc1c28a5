// pages/user/user.js
const auth = require('../../utils/auth.js');
const request = require('../../utils/request.js');
const productApi = require('../../utils/productApi.js');

Page({
  data: {
    userInfo: null,
    hasUserInfo: false,
    canIUseGetUserProfile: false,
    isLoggedIn: false,

    orderCount: {
      unpaid: 0,
      unshipped: 0,
      shipped: 0,
      review: 0,
      aftersale: 0
    },

    recommendProducts: []
  },

  onLoad: function (options) {
    if (wx.getUserProfile) {
      this.setData({
        canIUseGetUserProfile: true
      });
    }

    // 检查是否已登录
    this.checkLoginStatus();
    this.loadUserInfo();

  },

  onShow: function() {
     // 检查是否已登录
     this.checkLoginStatus();
     this.loadUserInfo();
  },

  // 检查登录状态
  checkLoginStatus: function() {
    // 从全局数据获取登录状态
    const app = getApp();
    const isLoggedIn = app.globalData.isLoggedIn;

    if (isLoggedIn) {
      // 从全局数据获取用户信息
      const userInfo = app.globalData.userInfo;

      this.setData({
        isLoggedIn: true,
        userInfo: {
          ...userInfo,
          nickName: userInfo.name, // 映射name到nickName以适配界面显示
          avatarUrl: userInfo.avatar // 映射avatar到avatarUrl以适配界面显示
        },
        hasUserInfo: !!userInfo
      });

    } else {
      this.setData({
        isLoggedIn: false,
        userInfo: null,
        hasUserInfo: false
      });
    }
  },

  // 获取用户信息
  getUserProfile: function() {
    wx.getUserProfile({
      desc: '用于完善会员资料',
      success: (res) => {
        this.setData({
          userInfo: res.userInfo,
          hasUserInfo: true
        });
      }
    });
  },

  // 登录
  login: function() {
    auth.navigateToLogin('/pages/user/user');
  },

  // 退出登录
  logout: function() {
    // 使用全局的登出方法
    const app = getApp();
    app.clearLoginInfo();

    // 更新页面状态
    this.setData({
      isLoggedIn: false,
      userInfo: null,
      hasUserInfo: false
    });

    // 提示用户
    wx.showToast({
      title: '已退出登录',
      icon: 'success'
    });
  },

  // 获取用户信息
  onGetUserInfo: function(e) {
    if (e.detail.userInfo) {
      this.setData({
        userInfo: e.detail.userInfo,
        hasUserInfo: true
      });

      // 跳转到登录页面完成登录流程
      auth.navigateToLogin('/pages/user/user');
    }
  },

  // 跳转到订单列表
  navigateToOrder: function(e) {
    const status = e.currentTarget.dataset.status;
    wx.navigateTo({
      url: '/pages/order/order?status=' + status
    });
  },

  navigateToCart: function() {
    wx.switchTab({
      url: '/pages/cart/cart',
    })
  },

  // 加载用户信息
  loadUserInfo: function() {
    wx.showLoading({
      title: '加载中...',
      mask: true
    });

    request.get('/user/info', {}, {
      success: (res) => {
        wx.hideLoading();
        if (res.data && res.data.code === 1) {
          const userInfo = res.data.data;
          // 更新页面数据，添加界面显示所需的字段映射
          this.setData({
            userInfo: {
              ...userInfo,
              nickName: userInfo.name, // 映射name到nickName
              avatarUrl: userInfo.avatar // 映射avatar到avatarUrl
            },
            isLoggedIn: true
          });
          
          // 更新全局数据
          getApp().globalData.userInfo = userInfo;
          getApp().globalData.isLoggedIn = true;
        }
      },
      fail: (err) => {
        wx.hideLoading();
        console.error('获取用户信息失败:', err);
      }
    });
  }
})
