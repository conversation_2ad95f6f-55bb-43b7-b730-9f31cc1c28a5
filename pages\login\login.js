// pages/login/login.js
const request = require('../../utils/request');
Page({
  data: {
    isAgree: true
  },

  onLoad: function (options) {
    // 检查是否已经登录
    const token = wx.getStorageSync('token');
    if (token) {
      // 已登录，跳转到首页或来源页面
      const redirect = options.redirect || '/pages/index/index';
      wx.switchTab({
        url: redirect
      });
    }
  },

  // 微信一键登录
  wechatLogin: function () {
    if (!this.data.isAgree) {
      wx.showToast({
        title: '请先同意用户协议和隐私政策',
        icon: 'none'
      });
      return;
    }

    wx.showLoading({
      title: '登录中...',
    });

    // 直接调用微信登录接口
    wx.login({
      success: (res) => {
        console.log(res.code)
        if (res.code) {
          // 发送 code 到后台换取 openId, sessionKey, unionId
          this.getWxUserInfo(res.code);
        } else {
          wx.hideLoading();
          wx.showToast({
            title: '登录失败，请重试',
            icon: 'none'
          });
        }
      },
      fail: () => {
        wx.hideLoading();
        wx.showToast({
          title: '登录失败，请重试',
          icon: 'none'
        });
      }
    });
  },

  // 发送 code 到后台
  getWxUserInfo: function (code) {
    // 使用封装的请求工具
    request.post('/user/login', {
      code: code
    }, {
      needToken: false, // 登录请求不需要token
      success: (res) => {
        wx.hideLoading();

        // 处理新的返回数据格式
        if (res.data.code === 1) {
          const userData = res.data.data;

          // 保存登录信息到本地存储
          this.saveUserData(userData);

          // 登录成功，跳转到首页或来源页面
          wx.showToast({
            title: '登录成功',
            icon: 'success',
            duration: 1500,
            success: () => {
              setTimeout(() => {
                wx.switchTab({
                  url: '/pages/index/index'
                });
              }, 1500);
            }
          });
        } else {
          wx.showToast({
            title: res.data.msg || '登录失败，请重试',
            icon: 'none'
          });
        }
      },
      fail: () => {
        wx.hideLoading();
        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        });
      }
    });
  },

  // 保存用户数据到本地存储
  saveUserData: function(userData) {
    // 保存token
    wx.setStorageSync('token', userData.token);

    // 保存用户ID
    wx.setStorageSync('userId', userData.userId);

    // 保存openid
    wx.setStorageSync('openid', userData.openid);

    // 保存用户信息
    const userInfo = {
      nickName: userData.name,
      avatarUrl: userData.avatar || '/images/default-avatar.png',
      membershipLevel: userData.membershipLevel || '普通'
    };
    wx.setStorageSync('userInfo', userInfo);

    // 保存是否是新用户
    wx.setStorageSync('isNewUser', userData.isNewUser);

    // 设置全局数据（如果使用了全局数据）
    const app = getApp();
    if (app.globalData) {
      app.globalData.userInfo = userInfo;
      app.globalData.token = userData.token;
      app.globalData.userId = userData.userId;
      app.globalData.isLoggedIn = true;
    }
  },

  // 切换协议同意状态
  toggleAgreement: function () {
    this.setData({
      isAgree: !this.data.isAgree
    });
  },

  // 显示用户协议
  showUserAgreement: function () {
    wx.navigateTo({
      url: '/pages/agreement/user-agreement'
    });
  },

  // 显示隐私政策
  showPrivacyPolicy: function () {
    wx.navigateTo({
      url: '/pages/agreement/privacy-policy'
    });
  }
});
