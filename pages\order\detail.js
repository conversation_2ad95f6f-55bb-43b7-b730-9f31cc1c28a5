// pages/order/detail.js
const orderApi = require('../../utils/orderApi.js');
const reviewApi = require('../../utils/reviewApi.js');
const auth = require('../../utils/auth.js');

Page({
  data: {
    orderId: null, // 订单ID
    orderDetail: null, // 订单详情
    loading: false, // 加载状态
    error: null, // 错误信息
    expressInfo: null, // 快递信息
    expressLoading: false, // 快递查询加载状态
    showExpressDetail: false, // 是否显示快递详情
    // 评价相关
    showReviewPopup: false, // 是否显示评价弹窗
    currentProductId: null, // 当前评价的商品ID
    reviewContent: '', // 评价内容
    reviewRating: 5, // 评价星级，默认5星
    reviewImages: [], // 评价图片列表
    maxImageCount: 9, // 最大图片数量
  },

  onLoad: function (options) {
    // 检查登录状态
    if (!auth.checkLogin()) {
      auth.navigateToLogin('/pages/order/detail');
      return;
    }

    // 获取订单ID
    if (options.id) {
      this.setData({
        orderId: options.id
      });
      this.loadOrderDetail();
    } else {
      this.setData({
        error: '订单ID不能为空'
      });
    }
  },

  onShow: function () {
    // 检查登录状态
    if (!auth.checkLogin()) {
      auth.navigateToLogin('/pages/order/detail');
      return;
    }
  },

  onPullDownRefresh: function () {
    // 下拉刷新
    this.loadOrderDetail();
  },

  // 加载订单详情
  loadOrderDetail: function () {
    if (!this.data.orderId) {
      this.setData({
        error: '订单ID不能为空'
      });
      return;
    }

    this.setData({
      loading: true,
      error: null
    });

    console.log('加载订单详情 - 订单ID:', this.data.orderId);

    orderApi.getOrderDetail(this.data.orderId)
      .then(orderDetail => {
        console.log('获取订单详情成功:', orderDetail);

        // 格式化订单数据
        const formattedOrder = this.formatOrderDetail(orderDetail);

        this.setData({
          orderDetail: formattedOrder,
          loading: false,
          error: null
        });

        // 如果有物流单号，自动查询快递信息
        if (formattedOrder.shipment && formattedOrder.shipment.trackingNumber) {
          this.loadExpressInfo(formattedOrder.shipment.trackingNumber);
        }

        // 停止下拉刷新
        wx.stopPullDownRefresh();
      })
      .catch(err => {
        console.error('获取订单详情失败:', err);
        this.setData({
          loading: false,
          error: err.message || '获取订单详情失败'
        });

        wx.showToast({
          title: err.message || '获取订单详情失败',
          icon: 'none'
        });

        // 停止下拉刷新
        wx.stopPullDownRefresh();
      });
  },

  // 格式化订单详情数据
  formatOrderDetail: function (orderDetail) {
    if (!orderDetail) return null;

    return {
      ...orderDetail,
      // 格式化创建时间
      createdAt: this.formatDate(orderDetail.createdAt),
      // 格式化更新时间
      updatedAt: orderDetail.updatedAt ? this.formatDate(orderDetail.updatedAt) : null,
      // 确保订单项是数组
      orderItems: Array.isArray(orderDetail.orderItems) ? orderDetail.orderItems : [],
      // 格式化物流信息
      shipment: orderDetail.shipment ? {
        ...orderDetail.shipment,
        shippingDate: orderDetail.shipment.shippingDate ? this.formatDate(orderDetail.shipment.shippingDate) : null,
        estimatedDeliveryDate: orderDetail.shipment.estimatedDeliveryDate ? this.formatDate(orderDetail.shipment.estimatedDeliveryDate) : null,
        actualDeliveryDate: orderDetail.shipment.actualDeliveryDate ? this.formatDate(orderDetail.shipment.actualDeliveryDate) : null
      } : null
    };
  },

  // 格式化日期
  formatDate: function (dateString) {
    if (!dateString) return '';

    const date = new Date(dateString);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');

    return `${year}-${month}-${day} ${hours}:${minutes}`;
  },

  // 取消订单
  onCancelOrder: function () {
    wx.showModal({
      title: '确认取消',
      content: '确定要取消这个订单吗？',
      success: (res) => {
        if (res.confirm) {
          this.cancelOrder();
        }
      }
    });
  },

  // 执行取消订单
  cancelOrder: function () {
    wx.showLoading({ title: '取消中...' });

    orderApi.cancelOrder(this.data.orderId)
      .then(() => {
        wx.hideLoading();
        wx.showToast({
          title: '订单已取消',
          icon: 'success'
        });

        // 重新加载订单详情
        this.loadOrderDetail();
      })
      .catch(err => {
        wx.hideLoading();
        wx.showToast({
          title: err.message || '取消失败',
          icon: 'none'
        });
      });
  },

  // 支付订单
  onPayOrder: function () {
    if (!this.data.orderId) {
      wx.showToast({
        title: '订单ID不能为空',
        icon: 'none'
      });
      return;
    }

    wx.showLoading({ title: '获取支付参数...' });

    // 调用支付API
    this.getPaymentParams(this.data.orderId)
      .then(paymentParams => {
        if (paymentParams) {
          return this.requestPayment(paymentParams);
        }
      })
      .then(() => {
        wx.hideLoading();
        wx.showToast({
          title: '支付成功',
          icon: 'success'
        });

        // 重新加载订单详情
        this.loadOrderDetail();
      })
      .catch(err => {
        wx.hideLoading();
        console.error('支付失败:', err);
        wx.showToast({
          title: err.message || '支付失败',
          icon: 'none'
        });
      });
  },

  // 获取支付参数
  getPaymentParams: function(orderId) {
    return new Promise((resolve, reject) => {
      const request = require('../../utils/request.js');

      const paymentData = {
        orderId: orderId,
        paymentMethod: "" // 根据API文档，paymentMethod可以为空字符串
      };

      console.log('调用支付API - 请求参数:', paymentData);

      request.post('/user/orders/pay', paymentData, {
        success: (res) => {
          console.log('支付API响应:', res);

          if (res.data && (res.data.code === 0 || res.data.code === 1) && res.data.data) {
            console.log('获取支付参数成功:', res.data.data);
            resolve(res.data.data);
          } else {
            console.error('获取支付参数失败:', res.data);
            reject(new Error(res.data.msg || '获取支付参数失败'));
          }
        },
        fail: (err) => {
          console.error('支付API请求失败:', err);
          reject(err);
        }
      });
    });
  },

  // 发起微信支付
  requestPayment: function(paymentParams) {
    return new Promise((resolve, reject) => {
      wx.requestPayment({
        timeStamp: paymentParams.timeStamp,
        nonceStr: paymentParams.nonceStr,
        package: paymentParams.package,
        signType: paymentParams.signType || 'MD5',
        paySign: paymentParams.paySign,
        success: resolve,
        fail: reject
      });
    });
  },

  // 确认收货
  onConfirmReceive: function () {
    // 检查订单状态
    if (!this.data.orderDetail) {
      wx.showToast({
        title: '订单信息不完整',
        icon: 'none'
      });
      return;
    }

    const orderStatus = this.data.orderDetail.status;
    if (orderStatus !== '已发货' && orderStatus !== '待收货') {
      wx.showToast({
        title: '当前订单状态不支持确认收货',
        icon: 'none'
      });
      return;
    }

    // 显示确认对话框
    wx.showModal({
      title: '确认收货',
      content: '请确认您已收到商品并检查无误。确认收货后订单将完成，无法撤销。',
      confirmText: '确认收货',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          this.confirmReceive();
        }
      }
    });
  },

  // 执行确认收货
  confirmReceive: function () {
    if (!this.data.orderId) {
      wx.showToast({
        title: '订单ID不能为空',
        icon: 'none'
      });
      return;
    }

    console.log('确认收货 - 订单ID:', this.data.orderId);
    wx.showLoading({ title: '确认收货中...' });

    orderApi.confirmReceive(this.data.orderId)
      .then((result) => {
        wx.hideLoading();
        console.log('确认收货成功:', result);

        wx.showToast({
          title: '确认收货成功',
          icon: 'success',
          duration: 2000
        });

        // 延迟重新加载订单详情，确保后端状态已更新
        setTimeout(() => {
          this.loadOrderDetail();
        }, 1000);
      })
      .catch(err => {
        wx.hideLoading();
        console.error('确认收货失败:', err);

        wx.showModal({
          title: '确认收货失败',
          content: err.message || '网络异常，请稍后重试',
          showCancel: true,
          confirmText: '重试',
          cancelText: '取消',
          success: (res) => {
            if (res.confirm) {
              // 用户选择重试
              this.confirmReceive();
            }
          }
        });
      });
  },

  // 申请退款
  onRequestRefund: function () {
    // 检查订单状态
    if (!this.data.orderDetail) {
      wx.showToast({
        title: '订单信息不完整',
        icon: 'none'
      });
      return;
    }

    const orderStatus = this.data.orderDetail.status;
    if (orderStatus !== '待发货') {
      wx.showToast({
        title: '只有待发货状态的订单才能申请退款',
        icon: 'none',
        duration: 3000
      });
      return;
    }

    // 显示退款确认对话框
    wx.showModal({
      title: '申请退款',
      content: '确定要申请退款吗？申请后商家将停止发货并处理退款。',
      confirmText: '申请退款',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          this.requestRefund();
        }
      }
    });
  },

  // 执行申请退款
  requestRefund: function () {
    if (!this.data.orderId) {
      wx.showToast({
        title: '订单ID不能为空',
        icon: 'none'
      });
      return;
    }

    if (!this.data.orderDetail) {
      wx.showToast({
        title: '订单信息不完整',
        icon: 'none'
      });
      return;
    }

    console.log('申请退款 - 订单ID:', this.data.orderId);
    wx.showLoading({ title: '提交退款申请...' });

    const refundData = {
      orderId: this.data.orderId,
      reason: '用户申请退款',
      refundAmount: this.data.orderDetail.totalAmount || 0
    };

    orderApi.requestRefund(refundData)
      .then((result) => {
        wx.hideLoading();
        console.log('退款申请成功:', result);

        wx.showToast({
          title: '退款申请已提交',
          icon: 'success',
          duration: 2000
        });

        // 延迟重新加载订单详情，确保后端状态已更新
        setTimeout(() => {
          this.loadOrderDetail();
        }, 1000);
      })
      .catch(err => {
        wx.hideLoading();
        console.error('申请退款失败:', err);

        wx.showModal({
          title: '退款申请失败',
          content: err.message || '网络异常，请稍后重试',
          showCancel: true,
          confirmText: '重试',
          cancelText: '取消',
          success: (res) => {
            if (res.confirm) {
              // 用户选择重试
              this.requestRefund();
            }
          }
        });
      });
  },

  // 返回订单列表
  onGoBack: function () {
    wx.navigateBack({
      delta: 1
    });
  },

  // 加载快递信息
  loadExpressInfo: function (trackingNumber) {
    if (!trackingNumber) {
      console.log('物流单号为空，跳过快递查询');
      return;
    }

    console.log('查询快递信息 - 单号:', trackingNumber);

    this.setData({ expressLoading: true });

    const request = require('../../utils/request.js');

    // 使用你提供的快递查询API
    const apiUrl = `https://api.tanshuapi.com/api/exp/v1/index?key=c844cf198dc2decd1e46c05abd2d04b6&no=${trackingNumber}`;

    wx.request({
      url: apiUrl,
      method: 'GET',
      success: (res) => {
        console.log('快递查询响应:', res);

        if (res.statusCode === 200 && res.data && res.data.code === 1) {
          const expressData = this.formatExpressInfo(res.data.data);
          this.setData({
            expressInfo: expressData,
            expressLoading: false
          });
          console.log('快递信息查询成功:', expressData);
        } else {
          console.error('快递查询失败:', res.data);
          this.setData({
            expressInfo: null,
            expressLoading: false
          });
        }
      },
      fail: (err) => {
        console.error('快递查询请求失败:', err);
        this.setData({
          expressInfo: null,
          expressLoading: false
        });
      }
    });
  },

  // 格式化快递信息
  formatExpressInfo: function (expressData) {
    if (!expressData) return null;

    return {
      company: expressData.company || '未知快递公司',
      com: expressData.com || '',
      no: expressData.no || '',
      comPhone: expressData.com_phone || '',
      comUrl: expressData.com_url || '',
      takeTime: expressData.take_time || '',
      courierPhone: expressData.courier_phone || '',
      statusDesc: expressData.status_desc || '',
      statusDetail: expressData.status_detail || 0,
      list: Array.isArray(expressData.list) ? expressData.list.map(item => ({
        datetime: item.datetime || '',
        remark: item.remark || ''
      })) : []
    };
  },

  // 切换快递详情显示
  toggleExpressDetail: function () {
    this.setData({
      showExpressDetail: !this.data.showExpressDetail
    });
  },

  // 刷新快递信息
  refreshExpressInfo: function () {
    if (this.data.orderDetail && this.data.orderDetail.shipment && this.data.orderDetail.shipment.trackingNumber) {
      this.loadExpressInfo(this.data.orderDetail.shipment.trackingNumber);
    }
  },

  // 复制物流单号
  copyTrackingNumber: function () {
    if (this.data.orderDetail && this.data.orderDetail.shipment && this.data.orderDetail.shipment.trackingNumber) {
      wx.setClipboardData({
        data: this.data.orderDetail.shipment.trackingNumber,
        success: () => {
          wx.showToast({
            title: '物流单号已复制',
            icon: 'success'
          });
        }
      });
    }
  },

  // 拨打快递公司电话
  callExpressCompany: function () {
    if (this.data.expressInfo && this.data.expressInfo.comPhone) {
      wx.makePhoneCall({
        phoneNumber: this.data.expressInfo.comPhone
      });
    }
  },

  // 拨打快递员电话
  callCourier: function () {
    if (this.data.expressInfo && this.data.expressInfo.courierPhone) {
      wx.makePhoneCall({
        phoneNumber: this.data.expressInfo.courierPhone
      });
    }
  },

  // 显示评价弹窗
  showReviewPopup: function(e) {
    const productId = e.currentTarget.dataset.productId;
    this.setData({
      showReviewPopup: true,
      currentProductId: productId,
      reviewContent: '',
      reviewRating: 5,
      reviewImages: []
    });
  },

  // 隐藏评价弹窗
  hideReviewPopup: function() {
    this.setData({
      showReviewPopup: false,
      currentProductId: null,
      reviewContent: '',
      reviewRating: 5,
      reviewImages: []
    });
  },

  // 评价内容输入
  onReviewContentInput: function(e) {
    this.setData({
      reviewContent: e.detail.value
    });
  },

  // 评分改变
  onRatingChange: function(e) {
    const value = parseInt(e.currentTarget.dataset.value);
    this.setData({
      reviewRating: value
    });
  },

  // 选择评价图片
  chooseReviewImages: function() {
    const remainCount = this.data.maxImageCount - this.data.reviewImages.length;
    if (remainCount <= 0) {
      wx.showToast({
        title: `最多只能上传${this.data.maxImageCount}张图片`,
        icon: 'none'
      });
      return;
    }

    wx.chooseImage({
      count: remainCount,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        console.log('选择图片成功:', res.tempFilePaths);

        // 验证图片大小
        const validImages = [];
        const maxSize = 5 * 1024 * 1024; // 5MB

        res.tempFilePaths.forEach((filePath, index) => {
          wx.getFileInfo({
            filePath: filePath,
            success: (fileInfo) => {
              if (fileInfo.size > maxSize) {
                wx.showToast({
                  title: `第${index + 1}张图片超过5MB，已跳过`,
                  icon: 'none'
                });
              } else {
                validImages.push(filePath);
              }

              // 当所有图片都检查完毕时更新数据
              if (validImages.length + index === res.tempFilePaths.length) {
                this.setData({
                  reviewImages: [...this.data.reviewImages, ...validImages]
                });
              }
            },
            fail: () => {
              // 如果获取文件信息失败，仍然添加图片
              validImages.push(filePath);
              if (validImages.length + index === res.tempFilePaths.length) {
                this.setData({
                  reviewImages: [...this.data.reviewImages, ...validImages]
                });
              }
            }
          });
        });
      },
      fail: (err) => {
        console.error('选择图片失败:', err);
        wx.showToast({
          title: '选择图片失败',
          icon: 'none'
        });
      }
    });
  },

  // 删除评价图片
  deleteReviewImage: function(e) {
    const index = e.currentTarget.dataset.index;
    const images = [...this.data.reviewImages];
    images.splice(index, 1);
    this.setData({
      reviewImages: images
    });
  },

  // 预览评价图片
  previewReviewImage: function(e) {
    const current = e.currentTarget.dataset.src;
    wx.previewImage({
      current,
      urls: this.data.reviewImages
    });
  },

  // 提交评价
  submitReview: function() {
    if (!this.data.reviewContent.trim()) {
      wx.showToast({
        title: '请输入评价内容',
        icon: 'none'
      });
      return;
    }

    if (!this.data.currentProductId) {
      wx.showToast({
        title: '商品ID不能为空',
        icon: 'none'
      });
      return;
    }

    if (!this.data.orderId) {
      wx.showToast({
        title: '订单ID不能为空',
        icon: 'none'
      });
      return;
    }

    console.log('开始提交评价 - 订单ID:', this.data.orderId, '商品ID:', this.data.currentProductId);

    wx.showLoading({
      title: this.data.reviewImages.length > 0 ? '上传图片中...' : '提交中...',
      mask: true
    });

    // 准备评价数据
    const reviewData = {
      content: this.data.reviewContent.trim(),
      orderId: this.data.orderId,
      productId: this.data.currentProductId,
      rating: this.data.reviewRating
    };

    // 使用reviewApi提交评价（包含图片上传）
    reviewApi.submitReviewWithImages(reviewData, this.data.reviewImages)
      .then((result) => {
        console.log('评价提交成功:', result);
        this.handleSubmitSuccess();
      })
      .catch(err => {
        console.error('评价提交失败:', err);
        this.handleSubmitError(err);
      });
  },

  // 处理提交成功
  handleSubmitSuccess: function() {
    wx.hideLoading();
    wx.showToast({
      title: '评价成功',
      icon: 'success',
      duration: 2000
    });

    this.hideReviewPopup();

    // 延迟重新加载订单详情，确保后端状态已更新
    setTimeout(() => {
      this.loadOrderDetail();
    }, 1000);
  },

  // 处理提交错误
  handleSubmitError: function(err) {
    wx.hideLoading();
    console.error('评价提交失败:', err);

    wx.showModal({
      title: '评价失败',
      content: err.message || '网络异常，请稍后重试',
      showCancel: true,
      confirmText: '重试',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          // 用户选择重试
          this.submitReview();
        }
      }
    });
  }
});
