/* pages/cart/cart.wxss */
.container {
  min-height: 100vh;
  background-color: #f8f8f8;
  padding-bottom: 100rpx;
}

/* 加载中样式 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300rpx;
  width: 100%;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 6rpx solid #f3f3f3;
  border-top: 6rpx solid #8ab6d6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #999;
}

/* 空购物车样式 */
.empty-cart {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-top: 100rpx;
  padding-bottom: 40rpx;
}

.empty-cart-image {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 30rpx;
  opacity: 0.8;
}

.empty-cart-text {
  font-size: 32rpx;
  color: #333;
  margin-bottom: 20rpx;
}

.empty-cart-tip {
  font-size: 28rpx;
  color: #999;
  margin-bottom: 40rpx;
}

.empty-cart-btn {
  width: 240rpx;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  border: 1px solid #ddd;
  border-radius: 40rpx;
  color: #333;
  font-size: 28rpx;
  margin-bottom: 60rpx;
}

/* 推荐商品样式 */
.recommend-section {
  width: 100%;
  padding: 0 30rpx;
}

.recommend-title {
  display: block;
  font-size: 30rpx;
  color: #333;
  text-align: center;
  margin-bottom: 30rpx;
  position: relative;
}

.recommend-title::before,
.recommend-title::after {
  content: '';
  position: absolute;
  top: 50%;
  width: 60rpx;
  height: 1px;
  background-color: #ddd;
}

.recommend-title::before {
  left: 30%;
}

.recommend-title::after {
  right: 30%;
}

.recommend-list {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
}

.recommend-item {
  width: 48%;
  background-color: #fff;
  border-radius: 10rpx;
  overflow: hidden;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  display: flex;
  flex-direction: column;
}

.recommend-image {
  width: 100%;
  height: 300rpx;
}

.recommend-name {
  font-size: 26rpx;
  color: #333;
  padding: 10rpx 16rpx;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
}

.recommend-tip {
  font-size: 22rpx;
  color: #999;
  padding: 0 16rpx 10rpx;
}

.recommend-price {
  font-size: 30rpx;
  color: #ff6b81;
  font-weight: bold;
  padding: 0 16rpx 16rpx;
}

/* 购物车列表样式 */
.cart-list {
  padding: 20rpx;
}

.cart-item {
  display: flex;
  align-items: center;
  background-color: #fff;
  border-radius: 10rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
  position: relative;
}

.item-select {
  margin-right: 20rpx;
}

.item-image {
  width: 160rpx;
  height: 160rpx;
  border-radius: 8rpx;
  margin-right: 20rpx;
}

.item-info {
  flex: 1;
  height: 160rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.item-name {
  font-size: 28rpx;
  color: #333;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-bottom: 10rpx;
}

/* SKU属性样式 */
.item-sku {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 5rpx;
}

.sku-attr {
  font-size: 22rpx;
  border-radius: 4rpx;
  margin-right: 6rpx;
  margin-bottom: 10rpx;
}

/* 商品图片点击效果 */
.item-image {
  position: relative;
}

.item-image:active {
  opacity: 0.8;
}

.item-image::after {
  content: '查看详情';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: rgba(0,0,0,0.5);
  color: white;
  font-size: 24rpx;
  text-align: center;
  padding: 4rpx 0;
  opacity: 0;
  transition: opacity 0.3s;
}

.item-image:active::after {
  opacity: 1;
}

.item-bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.item-price {
  font-size: 32rpx;
  color: #ff6b81;
  font-weight: bold;
}

.item-count {
  display: flex;
  align-items: center;
}

.count-btn {
  width: 50rpx;
  height: 50rpx;
  border: 1rpx solid #ddd;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 28rpx;
  color: #333;
}

.count-number {
  width: 70rpx;
  height: 50rpx;
  border-top: 1rpx solid #ddd;
  border-bottom: 1rpx solid #ddd;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 28rpx;
  color: #333;
}

.item-delete {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
}

/* 底部结算栏样式 */
.cart-footer {
  position: fixed;
  left: 0;
  bottom: 0;
  width: 100%;
  height: 100rpx;
  background-color: #fff;
  display: flex;
  align-items: center;
  padding: 0 20rpx;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.select-all {
  display: flex;
  align-items: center;
  margin-right: 30rpx;
}

.select-all text {
  font-size: 28rpx;
  color: #333;
  margin-left: 10rpx;
}

.total-info {
  flex: 1;
  display: flex;
  align-items: baseline;
}

.total-price-text {
  font-size: 28rpx;
  color: #333;
}

.total-price {
  font-size: 36rpx;
  color: #ff6b81;
  font-weight: bold;
}

.checkout-btn {
  width: 200rpx;
  height: 70rpx;
  background-color: #8ab6d6;
  border-radius: 35rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #fff;
  font-size: 28rpx;
  margin-right: 30rpx;
}