<template>
  <div class="dashboard-container">
    <!-- 数据概览 -->
    <el-row :gutter="20">
      <el-col :span="6" v-for="(item, index) in statsList" :key="index">
        <el-card class="stats-card" shadow="hover" v-loading="statsLoading">
          <div class="stats-icon" :style="{ backgroundColor: item.color }">
            <el-icon>
              <component :is="item.icon"></component>
            </el-icon>
          </div>
          <div class="stats-info">
            <div class="stats-value">{{ item.value }}</div>
            <div class="stats-title">{{ item.title }}</div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 图表区域 -->
    <el-row :gutter="20" class="chart-row">
      <el-col :span="12">
        <el-card class="chart-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <span>订单统计</span>
              <el-radio-group v-model="orderChartType" size="small">
                <el-radio-button label="week">本周</el-radio-button>
                <el-radio-button label="month">本月</el-radio-button>
                <el-radio-button label="year">本年</el-radio-button>
              </el-radio-group>
            </div>
          </template>
          <div class="chart-container" v-loading="chartLoading">
            <div class="simple-chart">
              <div class="chart-title">订单数量统计</div>
              <div v-if="orderChartData.length > 0" class="chart-bars">
                <div
                  v-for="(item, index) in orderChartData.slice(0, 7)"
                  :key="index"
                  class="chart-bar"
                >
                  <div
                    class="bar-fill"
                    :style="{
                      height: `${(item.value / Math.max(...orderChartData.map(d => d.value))) * 100}%`,
                      backgroundColor: '#409EFF'
                    }"
                  ></div>
                  <div class="bar-label">{{ item.name }}</div>
                  <div class="bar-value">{{ item.value }}</div>
                </div>
              </div>
              <div v-else class="chart-empty">
                <span>暂无订单数据</span>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card class="chart-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <span>销售额统计</span>
              <el-radio-group v-model="salesChartType" size="small">
                <el-radio-button label="week">本周</el-radio-button>
                <el-radio-button label="month">本月</el-radio-button>
                <el-radio-button label="year">本年</el-radio-button>
              </el-radio-group>
            </div>
          </template>
          <div class="chart-container" v-loading="chartLoading">
            <div class="simple-chart">
              <div class="chart-title">销售金额统计</div>
              <div v-if="salesChartData.length > 0" class="chart-bars">
                <div
                  v-for="(item, index) in salesChartData.slice(0, 7)"
                  :key="index"
                  class="chart-bar"
                >
                  <div
                    class="bar-fill"
                    :style="{
                      height: `${(item.value / Math.max(...salesChartData.map(d => d.value))) * 100}%`,
                      backgroundColor: '#67C23A'
                    }"
                  ></div>
                  <div class="bar-label">{{ item.name }}</div>
                  <div class="bar-value">{{ formatCurrency(item.value) }}</div>
                </div>
              </div>
              <div v-else class="chart-empty">
                <span>暂无销售数据</span>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 最新订单 -->
    <el-card class="latest-orders" shadow="hover">
      <template #header>
        <div class="card-header">
          <span>最新订单</span>
          <div>
            <el-button type="text" @click="refreshData" :loading="loading || statsLoading">
              刷新
            </el-button>
            <el-button type="text" @click="goToOrderList">查看更多</el-button>
          </div>
        </div>
      </template>
      <el-table :data="latestOrders" style="width: 100%" v-loading="loading">
        <el-table-column prop="orderNumber" label="订单号" width="180" />
        <el-table-column prop="userName" label="客户" width="120" />
        <el-table-column prop="totalAmount" label="金额" width="120">
          <template #default="scope">
            ¥{{ (scope.row.totalAmount || 0).toFixed(2) }}
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="120">
          <template #default="scope">
            <el-tag :type="getOrderStatusType(scope.row.status)">
              {{ getOrderStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createdAt" label="创建时间" />
        <el-table-column label="操作" width="120">
          <template #default="scope">
            <el-button type="text" size="small" @click="viewOrderDetail(scope.row.id)">
              查看
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import {
  ShoppingCart,
  Goods,
  User,
  Money
} from '@element-plus/icons-vue'
import {
  getOrderStats,
  getSalesStats,
  getLatestOrders,
  getTotalCompletedAmount,
  getTotalOrdersCount,
  getTotalUsersCount
} from '@/api/dashboard'
import { getProductList } from '@/api/product'

const router = useRouter()
const loading = ref(false)
const orderChartType = ref('week')
const salesChartType = ref('month')

// 统计数据
const statsList = ref([
  { title: '总订单数', value: 0, icon: 'ShoppingCart', color: '#409EFF' },
  { title: '总用户数', value: 0, icon: 'User', color: '#67C23A' },
  { title: '总交易金额', value: '¥0.00', icon: 'Money', color: '#E6A23C' },
  { title: '商品总数', value: 0, icon: 'Goods', color: '#F56C6C' }
])

// 加载状态
const statsLoading = ref(false)

// 最新订单
const latestOrders = ref([])

// 图表数据
const orderChartData = ref([])
const salesChartData = ref([])
const chartLoading = ref(false)

// 格式化数字（添加千分位分隔符）
const formatNumber = (num) => {
  if (num === null || num === undefined) return '0'
  return Number(num).toLocaleString()
}

// 格式化货币
const formatCurrency = (amount) => {
  if (amount === null || amount === undefined) return '¥0.00'
  return `¥${Number(amount).toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`
}

// 获取时间范围
const getTimeRange = (type) => {
  const now = new Date()
  let beginTime, endTime

  switch (type) {
    case 'week':
      // 本周一00:00:00到当前时间
      const monday = new Date(now)
      const dayOfWeek = now.getDay() === 0 ? 7 : now.getDay() // 将周日(0)转换为7
      monday.setDate(now.getDate() - dayOfWeek + 1)
      monday.setHours(0, 0, 0, 0)
      beginTime = monday.toISOString().split('T')[0]
      endTime = now.toISOString().split('T')[0]
      break

    case 'month':
      // 本月1日00:00:00到当前时间
      const firstDayOfMonth = new Date(now.getFullYear(), now.getMonth(), 1)
      beginTime = firstDayOfMonth.toISOString().split('T')[0]
      endTime = now.toISOString().split('T')[0]
      break

    case 'year':
      // 本年1月1日00:00:00到当前时间
      const firstDayOfYear = new Date(now.getFullYear(), 0, 1)
      beginTime = firstDayOfYear.toISOString().split('T')[0]
      endTime = now.toISOString().split('T')[0]
      break

    default:
      // 默认为本周
      const defaultMonday = new Date(now)
      const defaultDayOfWeek = now.getDay() === 0 ? 7 : now.getDay()
      defaultMonday.setDate(now.getDate() - defaultDayOfWeek + 1)
      defaultMonday.setHours(0, 0, 0, 0)
      beginTime = defaultMonday.toISOString().split('T')[0]
      endTime = now.toISOString().split('T')[0]
  }

  return { beginTime, endTime }
}

// 获取统计数据
const getStatsData = async () => {
  statsLoading.value = true
  try {
    // 并行获取所有统计数据
    const [
      totalOrdersResponse,
      totalUsersResponse,
      totalAmountResponse,
      productsResponse
    ] = await Promise.all([
      getTotalOrdersCount(),
      getTotalUsersCount(),
      getTotalCompletedAmount(),
      getProductList({ page: 1, pageSize: 1 }) // 只获取第一页来获取总数
    ])

    // 更新统计数据
    const totalOrders = totalOrdersResponse.code === 1 ? totalOrdersResponse.data : 0
    const totalUsers = totalUsersResponse.code === 1 ? totalUsersResponse.data : 0
    const totalAmount = totalAmountResponse.code === 1 ? totalAmountResponse.data : 0
    const totalProducts = productsResponse.code === 1 ? productsResponse.data?.total || 0 : 0

    statsList.value = [
      {
        title: '总订单数',
        value: formatNumber(totalOrders),
        icon: 'ShoppingCart',
        color: '#409EFF'
      },
      {
        title: '总用户数',
        value: formatNumber(totalUsers),
        icon: 'User',
        color: '#67C23A'
      },
      {
        title: '总交易金额',
        value: formatCurrency(totalAmount),
        icon: 'Money',
        color: '#E6A23C'
      },
      {
        title: '商品总数',
        value: formatNumber(totalProducts),
        icon: 'Goods',
        color: '#F56C6C'
      }
    ]
  } catch (error) {
    console.error('获取统计数据失败:', error)
    ElMessage.error('获取统计数据失败')
  } finally {
    statsLoading.value = false
  }
}

// 获取最新订单数据
const getLatestOrdersData = async () => {
  loading.value = true
  try {
    const response = await getLatestOrders(8) // 获取最新8条订单
    if (response.code === 1) {
      latestOrders.value = response.data || []
    } else {
      console.warn('获取最新订单失败:', response.msg)
      latestOrders.value = []
    }
  } catch (error) {
    console.error('获取最新订单失败:', error)
    ElMessage.error('获取最新订单失败')
    latestOrders.value = []
  } finally {
    loading.value = false
  }
}

// 获取图表数据
const getChartData = async () => {
  chartLoading.value = true
  try {
    // 获取订单统计数据
    await getOrderChartData()

    // 获取销售额统计数据
    await getSalesChartData()
  } catch (error) {
    console.error('获取图表数据失败:', error)
    // 使用模拟数据
    generateMockChartData()
  } finally {
    chartLoading.value = false
  }
}

// 获取订单图表数据
const getOrderChartData = async () => {
  const timeRange = getTimeRange(orderChartType.value)

  try {
    const response = await getOrderStats(timeRange)
    if (response.code === 1) {
      // 处理订单数据，按时间维度分组
      const chartData = processOrderDataByTime(response.data.records || [], orderChartType.value)
      orderChartData.value = chartData
    } else {
      console.warn('获取订单统计失败:', response.msg)
      orderChartData.value = []
    }
  } catch (error) {
    console.error('获取订单图表数据失败:', error)
    orderChartData.value = []
  }
}

// 获取销售额图表数据
const getSalesChartData = async () => {
  const timeRange = getTimeRange(salesChartType.value)

  try {
    const response = await getSalesStats(timeRange)
    if (response.code === 1) {
      // 处理销售额数据，按时间维度分组并累加金额
      const chartData = processSalesDataByTime(response.data.records || [], salesChartType.value)
      salesChartData.value = chartData
    } else {
      console.warn('获取销售额统计失败:', response.msg)
      salesChartData.value = []
    }
  } catch (error) {
    console.error('获取销售额图表数据失败:', error)
    salesChartData.value = []
  }
}

// 处理订单数据按时间维度分组
const processOrderDataByTime = (orders, timeType) => {
  const groupedData = {}

  // 初始化时间轴
  const initializeTimeAxis = (type) => {
    switch (type) {
      case 'week':
        ['周一', '周二', '周三', '周四', '周五', '周六', '周日'].forEach(day => {
          groupedData[day] = 0
        })
        break
      case 'month':
        // 获取当前月份的天数
        const now = new Date()
        const daysInMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0).getDate()
        for (let i = 1; i <= Math.min(daysInMonth, 31); i++) {
          groupedData[`${i}日`] = 0
        }
        break
      case 'year':
        for (let i = 1; i <= 12; i++) {
          groupedData[`${i}月`] = 0
        }
        break
    }
  }

  initializeTimeAxis(timeType)

  orders.forEach(order => {
    const orderDate = new Date(order.createdAt)
    let key

    switch (timeType) {
      case 'week':
        // 按星期几分组
        const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']
        key = weekdays[orderDate.getDay()]
        break
      case 'month':
        // 按日期分组
        key = `${orderDate.getDate()}日`
        break
      case 'year':
        // 按月份分组
        key = `${orderDate.getMonth() + 1}月`
        break
      default:
        key = '未知'
    }

    if (groupedData.hasOwnProperty(key)) {
      groupedData[key]++
    }
  })

  // 转换为图表数据格式并排序
  return Object.entries(groupedData).map(([name, value]) => ({
    name,
    value
  })).sort((a, b) => {
    if (timeType === 'week') {
      const weekOrder = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
      return weekOrder.indexOf(a.name) - weekOrder.indexOf(b.name)
    }
    return a.name.localeCompare(b.name, 'zh-CN', { numeric: true })
  })
}

// 处理销售额数据按时间维度分组
const processSalesDataByTime = (orders, timeType) => {
  const groupedData = {}

  // 初始化时间轴
  const initializeTimeAxis = (type) => {
    switch (type) {
      case 'week':
        ['周一', '周二', '周三', '周四', '周五', '周六', '周日'].forEach(day => {
          groupedData[day] = 0
        })
        break
      case 'month':
        // 获取当前月份的天数
        const now = new Date()
        const daysInMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0).getDate()
        for (let i = 1; i <= Math.min(daysInMonth, 31); i++) {
          groupedData[`${i}日`] = 0
        }
        break
      case 'year':
        for (let i = 1; i <= 12; i++) {
          groupedData[`${i}月`] = 0
        }
        break
    }
  }

  initializeTimeAxis(timeType)

  orders.forEach(order => {
    // 只统计已完成的订单
    if (order.status !== '已完成') return

    const orderDate = new Date(order.createdAt)
    let key

    switch (timeType) {
      case 'week':
        // 按星期几分组
        const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']
        key = weekdays[orderDate.getDay()]
        break
      case 'month':
        // 按日期分组
        key = `${orderDate.getDate()}日`
        break
      case 'year':
        // 按月份分组
        key = `${orderDate.getMonth() + 1}月`
        break
      default:
        key = '未知'
    }

    if (groupedData.hasOwnProperty(key)) {
      groupedData[key] += Number(order.totalAmount || 0)
    }
  })

  // 转换为图表数据格式并排序
  return Object.entries(groupedData).map(([name, value]) => ({
    name,
    value
  })).sort((a, b) => {
    if (timeType === 'week') {
      const weekOrder = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
      return weekOrder.indexOf(a.name) - weekOrder.indexOf(b.name)
    }
    return a.name.localeCompare(b.name, 'zh-CN', { numeric: true })
  })
}

// 生成模拟图表数据（备用）
const generateMockChartData = () => {
  const days = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
  const months = ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月']

  if (orderChartType.value === 'week') {
    orderChartData.value = days.map(day => ({
      name: day,
      value: Math.floor(Math.random() * 50) + 10
    }))
  } else if (orderChartType.value === 'month') {
    orderChartData.value = Array.from({ length: 30 }, (_, i) => ({
      name: `${i + 1}日`,
      value: Math.floor(Math.random() * 100) + 20
    }))
  } else {
    orderChartData.value = months.map(month => ({
      name: month,
      value: Math.floor(Math.random() * 500) + 100
    }))
  }

  if (salesChartType.value === 'week') {
    salesChartData.value = days.map(day => ({
      name: day,
      value: Math.floor(Math.random() * 5000) + 1000
    }))
  } else if (salesChartType.value === 'month') {
    salesChartData.value = Array.from({ length: 30 }, (_, i) => ({
      name: `${i + 1}日`,
      value: Math.floor(Math.random() * 10000) + 2000
    }))
  } else {
    salesChartData.value = months.map(month => ({
      name: month,
      value: Math.floor(Math.random() * 50000) + 10000
    }))
  }
}

// 获取仪表盘数据
const getDashboardData = async () => {
  // 并行获取统计数据、最新订单和图表数据
  await Promise.all([
    getStatsData(),
    getLatestOrdersData(),
    getChartData()
  ])
}

// 获取订单状态类型
const getOrderStatusType = (status) => {
  switch (status) {
    case '待付款':
      return 'info'
    case '已支付':
    case '待发货':
      return 'warning'
    case '已发货':
      return 'primary'
    case '已完成':
      return 'success'
    case '已取消':
      return 'danger'
    case '退款中':
      return 'warning'
    case '已退款':
      return 'info'
    default:
      return 'info'
  }
}

// 获取订单状态文本
const getOrderStatusText = (status) => {
  return status || '未知'
}

// 查看订单详情
const viewOrderDetail = (id) => {
  router.push(`/order/detail/${id}`)
}

// 跳转到订单列表
const goToOrderList = () => {
  router.push('/order')
}

// 刷新数据
const refreshData = () => {
  getDashboardData()
}

// 监听图表类型变化
watch(orderChartType, () => {
  getOrderChartData()
})

watch(salesChartType, () => {
  getSalesChartData()
})

onMounted(() => {
  getDashboardData()
})
</script>

<style scoped>
.dashboard-container {
  padding: 20px;
}

.stats-card {
  display: flex;
  align-items: center;
  height: 108px;
  margin-bottom: 20px;
}

.stats-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 15px;
}

.stats-icon .el-icon {
  font-size: 30px;
  color: #fff;
}

.stats-info {
  flex: 1;
}

.stats-value {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 5px;
}

.stats-title {
  font-size: 14px;
  color: #909399;
}

.chart-row {
  margin-bottom: 20px;
}

.chart-card {
  height: 350px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chart-container {
  height: 280px;
  padding: 20px;
}

.simple-chart {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.chart-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 20px;
  text-align: center;
}

.chart-bars {
  flex: 1;
  display: flex;
  align-items: flex-end;
  justify-content: space-around;
  padding: 0 10px;
}

.chart-bar {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  max-width: 60px;
  height: 100%;
  position: relative;
}

.bar-fill {
  width: 30px;
  min-height: 5px;
  border-radius: 4px 4px 0 0;
  transition: all 0.3s ease;
  margin-bottom: 8px;
}

.bar-label {
  font-size: 12px;
  color: #606266;
  margin-bottom: 4px;
  text-align: center;
}

.bar-value {
  font-size: 11px;
  color: #909399;
  font-weight: 600;
  text-align: center;
}

.chart-empty {
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #909399;
  font-size: 14px;
}

.latest-orders {
  margin-bottom: 20px;
}
</style>
