<template>
  <div class="order-list-container">
    <!-- 搜索和操作区域 -->
    <el-card class="search-card" shadow="never">
      <div class="search-operations">
        <el-form :inline="true" :model="searchForm" class="search-form">
          <el-form-item label="订单号">
            <el-input v-model="searchForm.orderNo" placeholder="请输入订单号" clearable />
          </el-form-item>
          <el-form-item label="用户名">
            <el-input v-model="searchForm.username" placeholder="请输入用户名" clearable />
          </el-form-item>
          <el-form-item label="订单状态">
            <el-select
              v-model="searchForm.status"
              placeholder="请选择订单状态"
              clearable
              style="width: 160px"
            >
              <el-option label="待付款" value="待付款" />
              <el-option label="已支付" value="已支付" />
              <el-option label="待发货" value="待发货" />
              <el-option label="已发货" value="已发货" />
              <el-option label="已完成" value="已完成" />
              <el-option label="已取消" value="已取消" />
              <el-option label="退款中" value="退款中" />
              <el-option label="已退款" value="已退款" />
            </el-select>
          </el-form-item>
          <el-form-item label="下单时间">
            <el-date-picker
              v-model="searchForm.dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="YYYY-MM-DD"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">搜索</el-button>
            <el-button @click="resetSearch">重置</el-button>
          </el-form-item>
        </el-form>
        <div class="operation-buttons">
          <el-button type="primary" @click="handleExport">导出订单</el-button>
        </div>
      </div>
    </el-card>

    <!-- 订单列表 -->
    <el-card class="order-table-card" shadow="never">
      <el-table
        v-loading="loading"
        :data="orderList"
        border
        style="width: 100%"
      >
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="orderNumber" label="订单号" width="180" />
        <el-table-column prop="userId" label="用户ID" width="100" />
        <el-table-column prop="totalAmount" label="订单金额" width="120">
          <template #default="scope">
            ¥{{ scope.row.totalAmount.toFixed(2) }}
          </template>
        </el-table-column>
        <el-table-column prop="status" label="订单状态" width="120">
          <template #default="scope">
            <el-tag :type="getOrderStatusType(scope.row.status)" effect="plain">
              {{ getOrderStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="paymentStatus" label="支付状态" width="120">
          <template #default="scope">
            <el-tag :type="getPaymentStatusType(scope.row.paymentStatus)" effect="plain">
              {{ getPaymentStatusText(scope.row.paymentStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createdAt" label="创建时间" width="180" />
        <el-table-column prop="updatedAt" label="更新时间" width="180" />
        <el-table-column label="操作" width="280" fixed="right">
          <template #default="scope">
            <el-button type="primary" link @click="handleDetail(scope.row)">详情</el-button>
            <el-button
              v-if="scope.row.status === '待发货'"
              type="success"
              link
              @click="handleShip(scope.row)"
            >
              发货
            </el-button>
            <el-button
              v-if="scope.row.paymentStatus === '未支付'"
              type="danger"
              link
              @click="handleCancel(scope.row)"
            >
              取消
            </el-button>
            <el-button
              v-if="scope.row.status === '退款中'"
              type="warning"
              link
              @click="handleViewRefund(scope.row)"
            >
              查看退款申请
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 发货对话框 -->
    <el-dialog
      v-model="shipDialogVisible"
      title="订单发货"
      width="500px"
    >
      <el-form :model="shipForm" label-width="100px">
        <el-form-item label="订单号">
          <el-input v-model="shipForm.orderNo" disabled />
        </el-form-item>
        <el-form-item label="物流公司">
          <el-select v-model="shipForm.shippingCompany" placeholder="请选择物流公司" style="width: 100%">
            <el-option label="顺丰速运" value="SF" />
            <el-option label="中通快递" value="ZTO" />
            <el-option label="圆通速递" value="YTO" />
            <el-option label="韵达快递" value="YD" />
            <el-option label="申通快递" value="STO" />
            <el-option label="京东物流" value="JD" />
          </el-select>
        </el-form-item>
        <el-form-item label="物流单号">
          <el-input v-model="shipForm.trackingNumber" placeholder="请输入物流单号" />
        </el-form-item>
        <el-form-item label="备注">
          <el-input
            v-model="shipForm.remark"
            type="textarea"
            :rows="3"
            placeholder="请输入备注信息"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="shipDialogVisible = false">取消</el-button>
          <el-button type="primary" :loading="submitting" @click="confirmShip">确认发货</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 退款申请详情对话框 -->
    <el-dialog
      v-model="refundDialogVisible"
      title="退款申请详情"
      width="800px"
    >
      <div v-loading="refundLoading" class="refund-detail">
        <div v-if="refundDetail" class="refund-info">
          <!-- 基本信息 -->
          <el-card class="info-card" shadow="never">
            <template #header>
              <span class="card-title">退款申请信息</span>
            </template>
            <el-descriptions :column="2" border>
              <el-descriptions-item label="退款单号">{{ refundDetail.refundNumber }}</el-descriptions-item>
              <el-descriptions-item label="订单号">{{ refundDetail.orderNumber }}</el-descriptions-item>
              <el-descriptions-item label="申请时间">{{ refundDetail.createdAt }}</el-descriptions-item>
              <el-descriptions-item label="退款状态">
                <el-tag :type="getRefundStatusType(refundDetail.status)">{{ refundDetail.status }}</el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="用户名">{{ refundDetail.userName }}</el-descriptions-item>
              <el-descriptions-item label="用户电话">{{ refundDetail.userPhone }}</el-descriptions-item>
              <el-descriptions-item label="订单金额">¥{{ refundDetail.orderAmount.toFixed(2) }}</el-descriptions-item>
              <el-descriptions-item label="退款金额">¥{{ refundDetail.refundAmount.toFixed(2) }}</el-descriptions-item>
              <el-descriptions-item label="退款原因" :span="2">{{ refundDetail.reason }}</el-descriptions-item>
              <el-descriptions-item v-if="refundDetail.adminRemark" label="管理员备注" :span="2">{{ refundDetail.adminRemark }}</el-descriptions-item>
            </el-descriptions>
          </el-card>

          <!-- 商品信息 -->
          <el-card class="info-card" shadow="never">
            <template #header>
              <span class="card-title">商品信息</span>
            </template>
            <div class="product-info">
              <el-image
                :src="refundDetail.firstOrderItem.imageUrl"
                fit="cover"
                class="product-image"
              />
              <div class="product-details">
                <div class="product-name">{{ refundDetail.firstOrderItem.productName }}</div>
                <div class="product-specs">
                  <span v-if="refundDetail.firstOrderItem.color">颜色：{{ refundDetail.firstOrderItem.color }}</span>
                  <span v-if="refundDetail.firstOrderItem.size">尺寸：{{ refundDetail.firstOrderItem.size }}</span>
                </div>
                <div class="product-price">
                  单价：¥{{ refundDetail.firstOrderItem.price.toFixed(2) }} × {{ refundDetail.firstOrderItem.quantity }}
                </div>
                <div class="product-total">
                  小计：¥{{ refundDetail.firstOrderItem.subtotal.toFixed(2) }}
                </div>
                <div v-if="refundDetail.totalItems > 1" class="more-items">
                  还有 {{ refundDetail.totalItems - 1 }} 件其他商品
                </div>
              </div>
            </div>
          </el-card>

          <!-- 处理操作 -->
          <el-card v-if="refundDetail.status === '待审核'" class="info-card" shadow="never">
            <template #header>
              <span class="card-title">审核退款申请</span>
            </template>
            <el-form :model="refundHandleForm" label-width="120px">
              <el-form-item label="审核结果">
                <el-radio-group v-model="refundHandleForm.approved">
                  <el-radio :value="true">同意退款</el-radio>
                  <el-radio :value="false">拒绝退款</el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item v-if="refundHandleForm.approved" label="实际退款金额" required>
                <el-input-number
                  v-model="refundHandleForm.actualRefundAmount"
                  :min="0.01"
                  :max="refundDetail.orderAmount"
                  :precision="2"
                  :step="0.01"
                  style="width: 200px"
                  placeholder="请输入实际退款金额"
                />
                <span class="amount-hint">（最大可退款：¥{{ refundDetail.orderAmount.toFixed(2) }}）</span>
              </el-form-item>
              <el-form-item label="管理员备注" required>
                <el-input
                  v-model="refundHandleForm.adminRemark"
                  type="textarea"
                  :rows="3"
                  placeholder="请输入审核备注"
                />
              </el-form-item>
            </el-form>
          </el-card>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="refundDialogVisible = false">关闭</el-button>
          <el-button
            v-if="refundDetail && refundDetail.status === '待审核'"
            type="primary"
            :loading="refundSubmitting"
            @click="handleRefund"
          >
            提交审核
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getOrderList, shipOrder, cancelOrder, exportOrders, getRefundRequests, reviewRefundRequest } from '@/api/order'

const router = useRouter()
const loading = ref(false)
const submitting = ref(false)
const orderList = ref([])
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(10)
const shipDialogVisible = ref(false)

// 退款相关
const refundDialogVisible = ref(false)
const refundLoading = ref(false)
const refundSubmitting = ref(false)
const refundDetail = ref(null)

// 搜索表单
const searchForm = reactive({
  orderNo: '',
  username: '',
  status: '',
  dateRange: []
})

// 发货表单
const shipForm = reactive({
  orderId: '',
  orderNo: '',
  shippingCompany: '',
  trackingNumber: '',
  remark: ''
})

// 退款处理表单
const refundHandleForm = reactive({
  approved: true,
  actualRefundAmount: 0,
  adminRemark: ''
})

// 获取订单列表
const fetchOrderList = async () => {
  loading.value = true
  try {
    // 构建查询参数
    const params = {
      page: currentPage.value,
      pageSize: pageSize.value,
      orderNumber: searchForm.orderNo || undefined,
      userId: searchForm.username || undefined,
      status: searchForm.status || undefined,
      beginTime: searchForm.dateRange?.[0] || undefined,
      endTime: searchForm.dateRange?.[1] || undefined
    }

    const response = await getOrderList(params)
    if (response.code === 1) {
      orderList.value = response.data.records || []
      total.value = response.data.total || 0
    } else {
      ElMessage.error(response.msg || '获取订单列表失败')
    }
  } catch (error) {
    console.error('获取订单列表失败:', error)
    ElMessage.error('获取订单列表失败')
  } finally {
    loading.value = false
  }
}

// 获取订单状态类型
const getOrderStatusType = (status) => {
  switch (status) {
    case '待付款':
      return 'info'
    case '已支付':
    case '待发货':
      return 'warning'
    case '已发货':
      return 'primary'
    case '已完成':
      return 'success'
    case '已取消':
      return 'danger'
    case '退款中':
      return 'warning'
    case '已退款':
      return 'info'
    default:
      return 'info'
  }
}

// 获取订单状态文本
const getOrderStatusText = (status) => {
  return status || '未知'
}

// 获取支付状态类型
const getPaymentStatusType = (status) => {
  switch (status) {
    case '已支付':
      return 'success'
    case '已退款':
      return 'warning'
    default:
      return 'info'
  }
}

// 获取支付状态文本
const getPaymentStatusText = (status) => {
  return status || '未支付'
}



// 获取退款状态类型
const getRefundStatusType = (status) => {
  switch (status) {
    case '待审核':
      return 'warning'
    case '已同意':
      return 'success'
    case '已拒绝':
      return 'danger'
    default:
      return 'info'
  }
}

// 搜索
const handleSearch = () => {
  currentPage.value = 1
  fetchOrderList()
}

// 重置搜索
const resetSearch = () => {
  Object.keys(searchForm).forEach(key => {
    searchForm[key] = key === 'dateRange' ? [] : ''
  })
  currentPage.value = 1
  fetchOrderList()
}

// 导出订单
const handleExport = async () => {
  try {
    const params = {
      orderNumber: searchForm.orderNo || undefined,
      userId: searchForm.username || undefined,
      status: searchForm.status || undefined,
      beginTime: searchForm.dateRange?.[0] || undefined,
      endTime: searchForm.dateRange?.[1] || undefined
    }

    const response = await exportOrders(params)
    const blob = new Blob([response], { type: 'application/vnd.ms-excel' })
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.setAttribute('download', '订单列表.xlsx')
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)

    ElMessage.success('订单导出成功')
  } catch (error) {
    console.error('导出订单失败:', error)
    ElMessage.error('导出订单失败')
  }
}

// 查看订单详情
const handleDetail = (row) => {
  router.push(`/order/detail/${row.id}`)
}

// 发货
const handleShip = (row) => {
  shipForm.orderId = row.id
  shipForm.orderNo = row.orderNumber
  shipForm.shippingCompany = ''
  shipForm.trackingNumber = ''
  shipForm.remark = ''
  shipDialogVisible.value = true
}

// 确认发货
const confirmShip = async () => {
  if (!shipForm.shippingCompany) {
    ElMessage.warning('请选择物流公司')
    return
  }
  if (!shipForm.trackingNumber) {
    ElMessage.warning('请输入物流单号')
    return
  }

  submitting.value = true
  try {
    const response = await shipOrder({
      orderId: shipForm.orderId,
      shippingCompany: shipForm.shippingCompany,
      trackingNumber: shipForm.trackingNumber,
      remark: shipForm.remark
    })

    if (response.code === 1) {
      ElMessage.success('发货成功')
      shipDialogVisible.value = false
      fetchOrderList()
    } else {
      ElMessage.error(response.msg || '发货失败')
    }
  } catch (error) {
    console.error('发货失败:', error)
    ElMessage.error('发货失败')
  } finally {
    submitting.value = false
  }
}

// 取消订单
const handleCancel = (row) => {
  ElMessageBox.confirm('确定要取消该订单吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      const response = await cancelOrder(row.id)
      if (response.code === 1) {
        ElMessage.success('订单已取消')
        fetchOrderList()
      } else {
        ElMessage.error(response.msg || '取消订单失败')
      }
    } catch (error) {
      console.error('取消订单失败:', error)
      ElMessage.error('取消订单失败')
    }
  }).catch(() => {})
}

// 查看退款申请
const handleViewRefund = async (row) => {
  refundLoading.value = true
  refundDialogVisible.value = true
  refundDetail.value = null

  try {
    const response = await getRefundRequests({ orderNumber: row.orderNumber })
    if (response.code === 1 && response.data.records.length > 0) {
      refundDetail.value = response.data.records[0]
      // 重置处理表单
      refundHandleForm.approved = true
      refundHandleForm.actualRefundAmount = refundDetail.value.refundAmount
      refundHandleForm.adminRemark = ''
    } else {
      ElMessage.error('未找到退款申请信息')
      refundDialogVisible.value = false
    }
  } catch (error) {
    console.error('获取退款申请失败:', error)
    ElMessage.error('获取退款申请失败')
    refundDialogVisible.value = false
  } finally {
    refundLoading.value = false
  }
}

// 处理退款申请
const handleRefund = async () => {
  if (!refundHandleForm.adminRemark.trim()) {
    ElMessage.warning('请输入管理员备注')
    return
  }

  // 如果同意退款，检查实际退款金额
  if (refundHandleForm.approved) {
    if (!refundHandleForm.actualRefundAmount || refundHandleForm.actualRefundAmount <= 0) {
      ElMessage.warning('请输入有效的实际退款金额')
      return
    }
    if (refundHandleForm.actualRefundAmount > refundDetail.value.orderAmount) {
      ElMessage.warning('实际退款金额不能大于订单金额')
      return
    }
  }

  refundSubmitting.value = true
  try {
    const requestData = {
      refundRequestId: refundDetail.value.id,
      approved: refundHandleForm.approved,
      adminRemark: refundHandleForm.adminRemark
    }

    // 只有在同意退款时才传递实际退款金额
    if (refundHandleForm.approved) {
      requestData.actualRefundAmount = refundHandleForm.actualRefundAmount
    }

    const response = await reviewRefundRequest(requestData)

    if (response.code === 1) {
      ElMessage.success('退款申请审核成功')
      refundDialogVisible.value = false
      fetchOrderList() // 刷新订单列表
    } else {
      ElMessage.error(response.msg || '审核退款申请失败')
    }
  } catch (error) {
    console.error('处理退款申请失败:', error)
    ElMessage.error('处理退款申请失败')
  } finally {
    refundSubmitting.value = false
  }
}

// 页码变化
const handleCurrentChange = (page) => {
  currentPage.value = page
  fetchOrderList()
}

// 每页条数变化
const handleSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1
  fetchOrderList()
}

onMounted(() => {
  fetchOrderList()
})
</script>

<style scoped>
.order-list-container {
  padding: 20px;
}

.search-card {
  margin-bottom: 20px;
}

.search-operations {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
}

.search-form {
  flex: 1;
  min-width: 600px;
}

.operation-buttons {
  display: flex;
  align-items: flex-start;
  margin-left: 20px;
}

.order-table-card {
  margin-bottom: 20px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

/* 退款申请详情样式 */
.refund-detail {
  max-height: 600px;
  overflow-y: auto;
}

.info-card {
  margin-bottom: 20px;
}

.info-card:last-child {
  margin-bottom: 0;
}

.card-title {
  font-weight: 600;
  color: #303133;
}

.product-info {
  display: flex;
  align-items: flex-start;
  gap: 16px;
}

.product-image {
  width: 80px;
  height: 80px;
  border-radius: 8px;
  flex-shrink: 0;
}

.product-details {
  flex: 1;
}

.product-name {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 8px;
}

.product-specs {
  font-size: 14px;
  color: #909399;
  margin-bottom: 4px;
}

.product-specs span {
  margin-right: 16px;
}

.product-price,
.product-total {
  font-size: 14px;
  color: #606266;
  margin-bottom: 4px;
}

.more-items {
  font-size: 12px;
  color: #909399;
  margin-top: 8px;
}

.amount-hint {
  margin-left: 10px;
  font-size: 12px;
  color: #909399;
}
</style>
