<template>
  <div class="user-detail-container" v-loading="loading">
    <el-card class="detail-card" shadow="never">
      <template #header>
        <div class="card-header">
          <span>用户详情</span>
          <el-button link type="primary" @click="goBack">返回</el-button>
        </div>
      </template>

      <div v-if="userDetail">
        <el-descriptions
          :column="2"
          border
          direction="vertical"
        >
          <el-descriptions-item label="ID">{{ userDetail.id }}</el-descriptions-item>
          <el-descriptions-item label="头像">
            <el-avatar :src="userDetail.avatar" :size="60">
               {{ userDetail.name ? userDetail.name.substring(0, 1).toUpperCase() : '-' }}
            </el-avatar>
          </el-descriptions-item>
          <el-descriptions-item label="用户名/昵称">{{ userDetail.name }}</el-descriptions-item>
          <el-descriptions-item label="手机号">{{ userDetail.phone }}</el-descriptions-item>
          <!-- 隐藏余额、积分、会员等级 -->
          <!-- <el-descriptions-item label="余额">¥{{ userDetail.balance ? userDetail.balance.toFixed(2) : '0.00' }}</el-descriptions-item> -->
          <!-- <el-descriptions-item label="积分">{{ userDetail.points }}</el-descriptions-item> -->
          <el-descriptions-item label="性别">{{ userDetail.gender }}</el-descriptions-item>
          <el-descriptions-item label="生日">{{ userDetail.birthday }}</el-descriptions-item>
          <el-descriptions-item label="地区">{{ userDetail.region }}</el-descriptions-item>
           <!-- <el-descriptions-item label="会员等级">{{ userDetail.membershipLevel }}</el-descriptions-item> -->
          <el-descriptions-item label="注册时间">{{ userDetail.createdAt }}</el-descriptions-item>
           <el-descriptions-item label="推荐状态">{{ userDetail.enableRecommendation ? '已启用' : '未启用' }}</el-descriptions-item>
           <!-- 您可能还需要其他字段 -->
           <el-descriptions-item label="openid">{{ userDetail.openid }}</el-descriptions-item>
            <!-- 如果有删除时间等字段，也可以加上 -->
             <el-descriptions-item label="删除时间">{{ userDetail.deletedAt || '未删除' }}</el-descriptions-item>

        </el-descriptions>
      </div>
       <el-empty v-else description="用户数据加载失败或不存在" />
    </el-card>

    <!-- 用户地址信息 -->
    <el-card class="detail-card" shadow="never" style="margin-top: 20px;">
      <template #header>
        <div class="card-header">
          <span>用户地址</span>
        </div>
      </template>

      <div v-loading="addressLoading">
        <el-table
          v-if="userAddresses.length > 0"
          :data="userAddresses"
          border
          style="width: 100%"
        >
          <el-table-column prop="id" label="地址ID" width="80" />
          <el-table-column prop="consignee" label="收货人" width="120" />
          <el-table-column prop="phone" label="联系电话" width="130" />
          <el-table-column prop="address" label="详细地址" min-width="200" show-overflow-tooltip />
          <el-table-column label="是否默认" width="100">
            <template #default="scope">
              <el-tag v-if="scope.row.isDefault" type="success" size="small">默认地址</el-tag>
              <span v-else class="text-muted">普通地址</span>
            </template>
          </el-table-column>
          <el-table-column prop="createdAt" label="创建时间" width="160" />
          <el-table-column prop="updatedAt" label="更新时间" width="160" />
        </el-table>

        <el-empty v-else description="该用户暂无收货地址" />
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { getUserDetail, getUserAddresses } from '@/api/user'

const route = useRoute()
const router = useRouter()
const loading = ref(true)
const userDetail = ref(null)
const addressLoading = ref(false)
const userAddresses = ref([])

// 获取用户详情
const fetchUserDetail = async (id) => {
  loading.value = true
  try {
    const res = await getUserDetail(id)
    if (res.code === 1 && res.data) {
      userDetail.value = res.data
      // 获取用户详情成功后，同时获取用户地址
      fetchUserAddresses(id)
    } else {
      ElMessage.error(res.msg || '获取用户详情失败')
      userDetail.value = null // 加载失败时清空数据
    }
  } catch (error) {
    console.error('获取用户详情失败:', error)
    ElMessage.error('获取用户详情失败')
    userDetail.value = null // 加载失败时清空数据
  } finally {
    loading.value = false
  }
}

// 获取用户地址列表
const fetchUserAddresses = async (userId) => {
  addressLoading.value = true
  try {
    const res = await getUserAddresses(userId)
    if (res.code === 1 && res.data) {
      userAddresses.value = res.data || []
    } else {
      console.warn('获取用户地址失败:', res.msg)
      userAddresses.value = []
    }
  } catch (error) {
    console.error('获取用户地址失败:', error)
    userAddresses.value = []
  } finally {
    addressLoading.value = false
  }
}

// 返回上一页
const goBack = () => {
  router.back()
}

// 监听路由参数变化
watch(
  () => route.params.id,
  (newId) => {
    if (newId) {
      fetchUserDetail(newId)
    } else {
    
      router.push('/user')
    }
  },
  { immediate: true }
)


</script>

<style scoped>
.user-detail-container {
  padding: 20px;
}

.detail-card {
  max-width: 800px;
  margin: 0 auto;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.text-muted {
  color: #909399;
  font-size: 12px;
}
</style>
