import { createRouter, createWebHistory } from 'vue-router'

// 导入页面组件
const Login = () => import('../views/Login.vue')
const Layout = () => import('../views/Layout.vue')
const Dashboard = () => import('../views/Dashboard.vue')
const ProductList = () => import('../views/product/ProductList.vue')
const ProductAdd = () => import('../views/product/ProductAdd.vue')
const CategoryList = () => import('../views/category/CategoryList.vue')
const OrderList = () => import('../views/order/OrderList.vue')
const UserList = () => import('../views/user/UserList.vue')
const BannerList = () => import('../views/banner/BannerList.vue')
const BannerAdd = () => import('../views/banner/BannerAdd.vue')
const UserDetail = () => import('../views/user/UserDetail.vue')
const Settings = () => import('../views/Settings.vue')
const ProductEdit = () => import('../views/product/ProductEdit.vue')
const CategoryAdd = () => import('../views/category/CategoryAdd.vue')
const OrderDetail = () => import('../views/order/OrderDetail.vue')

// 定义路由
const routes = [
  {
    path: '/login',
    name: 'Login',
    component: Login,
    meta: { requiresAuth: false }
  },
  {
    path: '/',
    component: Layout,
    redirect: '/dashboard',
    meta: { requiresAuth: true },
    children: [
      {
        path: 'dashboard',
        name: 'Dashboard',
        component: Dashboard,
        meta: { title: '仪表盘', icon: 'Odometer' }
      },
      {
        path: 'category',
        name: 'Category',
        component: CategoryList,
        meta: { title: '分类管理', icon: 'menu' }
      },
      {
        path: 'product',
        name: 'Product',
        component: ProductList,
        meta: { title: '商品管理', icon: 'Goods' }
      },
      {
        path: 'order',
        name: 'Order',
        component: OrderList,
        meta: { title: '订单管理', icon: 'document' }
      },
      {
        path: 'user',
        name: 'User',
        component: UserList,
        meta: { title: '用户管理', icon: 'user' }
      },
      {
        path: 'banner',
        name: 'Banner',
        component: BannerList,
        meta: { title: 'Banner图管理', icon: 'picture' }
      }
    ]
  },
  // 独立的功能页面路由（不在侧边栏显示）
  {
    path: '/product/add',
    component: Layout,
    meta: { requiresAuth: true },
    children: [
      {
        path: '',
        name: 'ProductAdd',
        component: ProductAdd,
        meta: { title: '添加商品', activeMenu: '/product' }
      }
    ]
  },
  {
    path: '/product/edit/:id',
    component: Layout,
    meta: { requiresAuth: true },
    children: [
      {
        path: '',
        name: 'ProductEdit',
        component: ProductEdit,
        meta: { title: '编辑商品', activeMenu: '/product' }
      }
    ]
  },
  {
    path: '/category/add',
    component: Layout,
    meta: { requiresAuth: true },
    children: [
      {
        path: '',
        name: 'CategoryAdd',
        component: CategoryAdd,
        meta: { title: '添加分类', activeMenu: '/category' }
      }
    ]
  },
  {
    path: '/order/detail/:id',
    component: Layout,
    meta: { requiresAuth: true },
    children: [
      {
        path: '',
        name: 'OrderDetail',
        component: OrderDetail,
        meta: { title: '订单详情', activeMenu: '/order' }
      }
    ]
  },
  {
    path: '/user/detail/:id',
    component: Layout,
    meta: { requiresAuth: true },
    children: [
      {
        path: '',
        name: 'UserDetail',
        component: UserDetail,
        meta: { title: '用户详情', activeMenu: '/user' }
      }
    ]
  },
  {
    path: '/banner/add',
    component: Layout,
    meta: { requiresAuth: true },
    children: [
      {
        path: '',
        name: 'BannerAdd',
        component: BannerAdd,
        meta: { title: '添加Banner图', activeMenu: '/banner' }
      }
    ]
  },
  {
    path: '/banner/edit/:id',
    component: Layout,
    meta: { requiresAuth: true },
    children: [
      {
        path: '',
        name: 'BannerEdit',
        component: BannerAdd,
        meta: { title: '编辑Banner图', activeMenu: '/banner' }
      }
    ]
  },
  {
    path: '/settings',
    component: Layout,
    meta: { requiresAuth: true },
    children: [
      {
        path: '',
        name: 'Settings',
        component: Settings,
        meta: { title: '系统设置', activeMenu: '/settings' }
      }
    ]
  },
  // 404页面
  {
    path: '/:pathMatch(.*)*',
    redirect: '/404'
  },
  {
    path: '/404',
    name: '404',
    component: () => import('../views/404.vue'),
    meta: { requiresAuth: false }
  }
]

// 创建路由实例
const router = createRouter({
  history: createWebHistory(),
  routes
})

// 全局前置守卫
router.beforeEach((to, from, next) => {
  const token = localStorage.getItem('token')

  if (to.meta.requiresAuth && !token) {
    // 需要登录但未登录，重定向到登录页
    next({ name: 'Login' })
  } else {
    next()
  }
})

export default router
