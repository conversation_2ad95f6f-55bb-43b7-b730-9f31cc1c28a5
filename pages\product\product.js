// pages/product/product.js
const productApi = require('../../utils/productApi.js');
const addressApi = require('../../utils/addressApi.js');
const cartApi = require('../../utils/cartApi.js');
const util = require('../../utils/util.js');

Page({
  data: {
    product: null,
    current: 1,
    total: 3,
    indicatorDots: true,
    autoplay: true,
    interval: 3000,
    duration: 500,
    currentTab: 0,
    selectedSize: '',
    selectedColor: '',
    quantity: 1,
    showSku: false,
    skuType: 'cart', // cart or buy
    // 尺码数据
    sizes: [

    ],
    selectedSizeIndex: 2, // 默认选中精灵20
    colors: ['粉色', '蓝色', '黄色', '绿色'],
    isVip: false,
    // 地址相关
    showAddressPopup: false,
    addresses: [],
    selectedAddressIndex: 0,
    currentAddress: null,
    // 订单相关
    orderRemark: '',
    showRemarkInput: false,
    // 配送方式
    deliveryType: 'express', // express: 邮寄, self: 自提
    // 配送相关
    deliveryInfo: {
      shop: 'CloudsPuppy 云朵小狗',
      distance: '',
      location: '重庆市南岸区福民路39号附76号'
    },
    // 提货人相关
    receiver: {
      name: '',
      phone: ''
    },
    showReceiverSelector: false,
    // 提货时间相关
    deliveryTime: '',
    showTimeSelector: false,
    // 支付方式
    paymentMethod: '微信支付',
    // 运费
    expressFee: 0 // 默认免运费
  },

  onLoad: function (options) {
    // 获取商品ID
    const id = options.id || '';
    if (!id) {
      wx.showToast({
        title: '商品ID不能为空',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
      return;
    }

    // 设置页面标题为加载中
    wx.setNavigationBarTitle({
      title: '商品详情加载中...'
    });

    // 显示加载中
    wx.showLoading({
      title: '加载中...',
      mask: true
    });

    // 获取商品数据
    this.getProductDetail(id);

    // 获取收货地址
    this.getAddresses();

    // 初始化订单相关数据
    this.setData({
      showSku: false,
      showOrderConfirm: false,
      'receiver.name': '',
      'receiver.phone': '',
      deliveryTime: '请选择提货时间'
    });
  },

  // 获取商品详情
  getProductDetail: function (id) {
    productApi.getProductDetail(id)
      .then(productData => {
        // 处理商品数据
        if (!productData) {
          throw new Error('商品不存在');
        }

        // 调试输出API返回的数据
        console.log('API返回的商品数据:', productData);
        console.log('SKU数据:', productData.skus);
        console.log('可用尺码:', productData.availableSizes);
        console.log('可用颜色:', productData.availableColors);

        // 设置页面标题
        wx.setNavigationBarTitle({
          title: productData.name || '商品详情'
        });

        // 创建一个新的商品对象，映射API返回的数据
        const formattedProduct = {
          id: productData.id,
          name: productData.name || '日常休闲套装',
          price: util.formatPrice(productData.currentPrice || 0),
          originalPrice: util.formatPrice(productData.originalPrice || 0),
          // 计算会员折扣金额 (原价 - 折扣价)
          memberDiscount: ((productData.originalPrice - productData.currentPrice) || 0).toFixed(1),
          stock: productData.totalStock || 0,
          sales: productData.salesCount || 0,
          description: productData.description || '',
          detail: productData.description || '', // 可能需要更详细的描述字段
          status: productData.status || '',
          categoryId: productData.categoryId || 0,
          categoryName: productData.categoryName || '',
          subcategoryId: productData.subcategoryId || 0,
          subcategoryName: productData.subcategoryName || '',
          comments: this.formatReviews(productData.reviews || []),
          averageRating: productData.averageRating || 0,
          reviewCount: productData.reviewCount || 0,
          shopName: '屁桃 偷糖',
          stockStatus: productData.status || '现货'
        };

        // 处理图片数组
        formattedProduct.images = [];
        formattedProduct.detailImages = [];

        // 添加主图到轮播图
        if (productData.imageUrl) {
          formattedProduct.images.push(productData.imageUrl);
        }

        // 处理详情图片 - 用于详情页面展示
        if (productData.detailImages && Array.isArray(productData.detailImages) && productData.detailImages.length > 0) {
          // 详情图片单独存储，用于详情页面无缝展示
          formattedProduct.detailImages = productData.detailImages.filter(img => img && img.trim() !== '');
          console.log('处理详情图片:', formattedProduct.detailImages.length, '张');
        }

        // 如果没有主图，使用第一张详情图作为主图
        if (formattedProduct.images.length === 0 && formattedProduct.detailImages.length > 0) {
          formattedProduct.images.push(formattedProduct.detailImages[0]);
        }

        // 如果完全没有图片，添加默认图片
        if (formattedProduct.images.length === 0) {
          formattedProduct.images = ['/images/default-product.jpg'];
        }

        // 设置轮播图数量
        this.setData({
          product: formattedProduct,
          total: formattedProduct.images.length,
          current: 1
        });

        // 处理商品规格
        this.processProductSizes(productData);

        // 获取推荐商品
        this.getRecommendProducts();
      })
      .catch(err => {
        console.error('获取商品详情失败:', err);
        wx.showToast({
          title: err.message || '获取商品详情失败',
          icon: 'none'
        });
      })
      .finally(() => {
        wx.hideLoading();
      });
  },

  // 格式化评论数据
  formatReviews: function (reviews) {
    console.log('格式化评价数据:', reviews);

    if (!reviews || !Array.isArray(reviews)) {
      console.log('评价数据为空或不是数组，返回空数组');
      return [];
    }

    const formattedReviews = reviews.map(review => ({
      id: review.id || Math.random().toString(36).substr(2, 9),
      user: review.userName || review.user || '匿名用户',
      avatar: review.userAvatar || review.avatar || '/images/default-avatar.png',
      content: review.content || review.comment || '用户未留下评价内容',
      images: Array.isArray(review.reviewImages) ? review.reviewImages :
              Array.isArray(review.images) ? review.images : [],
      date: review.createdAt ? this.formatDate(review.createdAt) :
            review.date || new Date().toISOString().split('T')[0],
      star: review.rating || review.star || 5,
      // 添加评分数组用于星星显示
      starArray: new Array(review.rating || review.star || 5).fill(1)
    }));

    console.log('格式化后的评价数据:', formattedReviews);
    return formattedReviews;
  },

  // 格式化日期
  formatDate: function(dateString) {
    if (!dateString) return '';

    try {
      const date = new Date(dateString);
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    } catch (error) {
      console.error('日期格式化失败:', error);
      return dateString.split('T')[0] || '';
    }
  },

  /**
   * 处理商品规格 - 仅使用SKU数据
   * 修改说明：
   * 1. 现在只处理productData.skus数组中的数据
   * 2. 移除对availableSizes和availableColors的处理
   * 3. 无SKU数据时显示错误提示并清空规格选择
   * 4. 尺码和颜色现在从sku.attributes中获取
   * 5. 颜色提取逻辑也改为使用attributes
   */
  processProductSizes: function (productData) {
    console.log('开始处理商品规格数据');

    // 检查是否有SKU数据
    if (!productData.skus || !Array.isArray(productData.skus) || productData.skus.length === 0) {
      console.error('商品没有有效的SKU数据');
      wx.showToast({
        title: '该商品暂无库存',
        icon: 'none'
      });
      this.setData({
        sizes: [],
        colors: [],
        selectedSizeIndex: -1,
        selectedSize: '',
        selectedColor: ''
      });
      return;
    }

    console.log('使用SKU数据处理规格');

    // 转换SKU数据为规格数据
    const sizes = productData.skus.map(sku => ({
      id: sku.id,
      name: sku.attributes.尺码 + sku.attributes.颜色 || '默认规格',
      color: sku.color || '',
      price: sku.discountedPrice || sku.price || productData.currentPrice,
      stock: sku.stock || 0,
      skuCode: sku.skuCode || ''
    }));

    console.log('转换后的规格数据:', sizes);

    // 从SKU中提取唯一的颜色
    const colorSet = new Set();
    sizes.forEach(sku => {
      if (sku.color) {
        colorSet.add(sku.color);
      }
    });
    const availableColors = Array.from(colorSet);
    console.log('从SKU中提取的可用颜色:', availableColors);

    // 更新商品价格范围
    let minPrice = Number.MAX_VALUE;
    let maxPrice = 0;

    sizes.forEach(sku => {
      const price = parseFloat(sku.price);
      if (price < minPrice) minPrice = price;
      if (price > maxPrice) maxPrice = price;
    });

    console.log('价格范围:', minPrice, '-', maxPrice);

    // 如果有价格范围，更新商品价格显示
    if (minPrice !== maxPrice && minPrice !== Number.MAX_VALUE) {
      this.setData({
        'product.minPrice': util.formatPrice(minPrice),
        'product.maxPrice': util.formatPrice(maxPrice)
      });
    }

    // 设置默认选中的尺码和颜色
    const selectedSizeIndex = 0;
    const selectedSize = sizes[selectedSizeIndex].name;

    this.setData({
      sizes: sizes,
      selectedSizeIndex: selectedSizeIndex,
      selectedSize: selectedSize,
      colors: availableColors,
      selectedColor: availableColors.length > 0 ? availableColors[0] : ''
    });

    console.log('设置的规格数据:', {
      sizes: sizes,
      selectedSize: selectedSize,
      colors: availableColors
    });

    // 处理库存统计
    if (productData.skuStockStats && typeof productData.skuStockStats === 'object') {
      console.log('处理SKU库存统计:', productData.skuStockStats);

      // 如果需要，可以使用skuStockStats来更新特定SKU组合的库存
      // 例如: skuStockStats['粉色_M'] = 40 表示粉色M码的库存为40

      // 更新已设置的尺码库存
      const sizes = this.data.sizes;
      const updatedSizes = sizes.map(size => {
        const key = `${size.color || ''}_${size.name}`;
        if (productData.skuStockStats[key] !== undefined) {
          size.stock = productData.skuStockStats[key];
          console.log(`更新尺码 ${size.name} 颜色 ${size.color || '无'} 的库存为 ${size.stock}`);
        }
        return size;
      });

      this.setData({
        sizes: updatedSizes
      });
    }
  },

  // 获取推荐商品
  getRecommendProducts: function () {
    productApi.getRecommendProducts(6)
      .then(recommendProducts => {
        if (recommendProducts && Array.isArray(recommendProducts)) {
          // 处理推荐商品数据
          const recommends = recommendProducts.map(item => ({
            id: item.id,
            name: item.name,
            price: util.formatPrice(item.currentPrice || item.price),
            originalPrice: util.formatPrice(item.originalPrice),
            image: item.imageUrl || (item.images && item.images.length > 0 ? item.images[0] : '')
          }));

          // 更新推荐商品
          this.setData({
            'product.recommends': recommends
          });
        }
      })
      .catch(err => {
        console.error('获取推荐商品失败:', err);
      });
  },

  // 获取购物车数量
  getCartCount: function () {
    cartApi.getCartCount()
      .then(count => {
        // 可以在这里更新购物车图标上的数字
        console.log('购物车数量:', count);
        // 如果需要，可以设置到全局数据中
        getApp().globalData.cartCount = count;
      })
      .catch(err => {
        console.error('获取购物车数量失败:', err);
      });
  },

  // 切换标签页
  switchTab: function (e) {
    this.setData({
      currentTab: e.currentTarget.dataset.index
    });
  },

  // 显示SKU选择或直接显示订单确认
  showSkuPanel: function (e) {
    const type = e.currentTarget.dataset.type;

    if (type === 'buy') {
      // 如果是立即购买，先隐藏SKU选择面板，然后显示订单确认面板
      this.setData({
        showSku: false
      });

      // 创建订单项
      const product = this.data.product;
      const size = this.data.sizes[this.data.selectedSizeIndex];
      const selectedColor = this.data.selectedColor;

      // 查找匹配的SKU
      let skuId = null;
      if (size.id) {
        skuId = size.id;
      } else {
        // 否则构建SKU ID
        skuId = `${product.id}_${this.data.selectedSize}${selectedColor ? '_' + selectedColor : ''}`;
      }

      const orderItem = {
        id: product.id,
        skuId: skuId,
        name: product.name,
        price: size.price,
        imageUrl: product.images[0],
        size: this.data.selectedSize,
        color: selectedColor || '',
        count: this.data.quantity,
        totalPrice: size.price * this.data.quantity
      };

      // 存储临时订单
      wx.setStorageSync('tempOrder', [orderItem]);

      // 确保已加载地址
      if (this.data.addresses.length === 0) {
        this.getAddresses();
      }

      // 显示订单确认面板，并确保尺码、颜色和数量信息正确
      this.setData({
        showOrderConfirm: true,
        selectedSize: this.data.selectedSize,
        selectedColor: selectedColor || '',
        quantity: this.data.quantity
      });
    } else {
      // 如果是加入购物车，显示SKU选择面板
      this.setData({
        showSku: true,
        skuType: type
      });
    }
  },

  // 隐藏SKU选择
  hideSkuPanel: function () {
    this.setData({
      showSku: false
    });
  },

  // 隐藏订单确认面板
  hideOrderConfirm: function () {
    this.setData({
      showOrderConfirm: false
    });
  },

  // 选择尺码
  selectSize: function (e) {
    const index = e.currentTarget.dataset.index;
    const size = this.data.sizes[index];

    // 检查库存，如果无货则不允许选择
    if (size.stock <= 0) {
      wx.showToast({
        title: '该尺码暂无库存',
        icon: 'none'
      });
      return;
    }

    this.setData({
      selectedSize: size.name,
      selectedSizeIndex: index
    });
  },

  // 选择颜色
  selectColor: function (e) {
    this.setData({
      selectedColor: e.currentTarget.dataset.color
    });
  },

  // 减少数量
  minusCount: function () {
    if (this.data.quantity > 1) {
      this.setData({
        quantity: this.data.quantity - 1
      });
    }
  },

  // 增加数量
  addCount: function () {
    this.setData({
      quantity: this.data.quantity + 1
    });
  },

  // 添加到购物车
  addToCart: function () {
    if (!this.data.selectedSize) {
      wx.showToast({
        title: '请选择尺码',
        icon: 'none'
      });
      return;
    }

    if (this.data.colors && this.data.colors.length > 0 && !this.data.selectedColor) {
      wx.showToast({
        title: '请选择颜色',
        icon: 'none'
      });
      return;
    }

    const product = this.data.product;
    const selectedSize = this.data.selectedSize;
    const selectedColor = this.data.selectedColor;

    // 查找匹配的SKU
    let skuId = null;
    let selectedSku = null;

    // 如果有sizes数据，并且包含id字段，说明是从SKU转换来的
    if (this.data.sizes && this.data.sizes.length > 0 && this.data.sizes[this.data.selectedSizeIndex].id) {
      selectedSku = this.data.sizes[this.data.selectedSizeIndex];
      skuId = selectedSku.id;
    } else {
      // 否则构建SKU ID
      skuId = `${product.id}_${selectedSize}${selectedColor ? '_' + selectedColor : ''}`;
    }

    // 调用API添加到购物车
    wx.showLoading({
      title: '添加中...'
    });

    // 构建请求数据
    const cartData = {
      productId: parseInt(product.id),
      quantity: parseInt(this.data.quantity),
      skuId: parseInt(skuId) || 0
    };

    console.log('添加购物车请求数据:', cartData);

    // 调用购物车API
    cartApi.addToCart(cartData)
      .then(result => {
        console.log('添加到购物车成功:', result);

        // 隐藏SKU选择面板
        this.setData({
          showSku: false
        });

        wx.showToast({
          title: '已加入购物车',
          icon: 'success'
        });
      })
      .catch(err => {
        console.error('添加到购物车失败:', err);
        wx.showToast({
          title: '添加失败: ' + (err.message || '未知错误'),
          icon: 'none'
        });
      })
      .finally(() => {
        wx.hideLoading();
      });
  },

  // 立即购买 - 从SKU面板确认后调用
  buyNow: function () {
    if (!this.data.selectedSize) {
      wx.showToast({
        title: '请选择尺码',
        icon: 'none'
      });
      return;
    }

    if (this.data.colors && this.data.colors.length > 0 && !this.data.selectedColor) {
      wx.showToast({
        title: '请选择颜色',
        icon: 'none'
      });
      return;
    }

    // 创建订单项
    const product = this.data.product;
    const size = this.data.sizes[this.data.selectedSizeIndex];
    const selectedColor = this.data.selectedColor;

    // 查找匹配的SKU
    let skuId = null;

    // 如果有sizes数据，并且包含id字段，说明是从SKU转换来的
    if (size.id) {
      skuId = size.id;
    } else {
      // 否则构建SKU ID
      skuId = `${product.id}_${this.data.selectedSize}${selectedColor ? '_' + selectedColor : ''}`;
    }

    const orderItem = {
      id: product.id,
      skuId: skuId,
      name: product.name,
      price: size.price,
      imageUrl: product.images[0],
      size: this.data.selectedSize,
      color: selectedColor || '',
      count: this.data.quantity,
      totalPrice: size.price * this.data.quantity
    };

    // 存储临时订单
    wx.setStorageSync('tempOrder', [orderItem]);

    // 确保已加载地址
    if (this.data.addresses.length === 0) {
      this.getAddresses();
    }

    // 关闭SKU选择面板，显示订单确认面板
    this.setData({
      showSku: false,
      showOrderConfirm: true,
      selectedSize: this.data.selectedSize,
      selectedColor: selectedColor || '',
      quantity: this.data.quantity
    });
  },

  // 图片预览
  previewImage: function (e) {
    const current = e.currentTarget.dataset.src;
    wx.previewImage({
      current: current,
      urls: this.data.product.images
    });
  },

  // 轮播图切换
  swiperChange: function (e) {
    this.setData({
      current: e.detail.current + 1
    });
  },

  // 分享
  onShareAppMessage: function () {
    const product = this.data.product;
    return {
      title: product.name,
      path: '/pages/product/product?id=' + product.id,
      imageUrl: product.images[0]
    };
  },

  // 分享按钮点击
  onShareTap: function () {
    wx.showShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage', 'shareTimeline']
    });
  },

  // 联系客服
  contactService: function () {
    wx.showToast({
      title: '正在连接客服...',
      icon: 'loading',
      duration: 1000
    });
  },

  // 前往店铺
  goToShop: function () {
    wx.navigateTo({
      url: '/pages/shop/shop'
    });
  },

  // 前往购物车
  goToCart: function () {
    wx.switchTab({
      url: '/pages/cart/cart'
    });
  },

  // 获取收货地址
  getAddresses: function () {
    console.log("开始加载地址")
    // 显示加载中
    wx.showLoading({
      title: '加载地址...',
      mask: true
    });

    // 从API获取地址列表
    addressApi.getAddressList()
      .then(addresses => {
        if (addresses && Array.isArray(addresses) && addresses.length > 0) {
          // 设置默认选中地址
          let defaultIndex = 0;
          for (let i = 0; i < addresses.length; i++) {
            if (addresses[i].isDefault) {
              defaultIndex = i;
              break;
            }
          }

          // 格式化地址数据
          const formattedAddresses = addresses.map(item => ({
            id: item.id,
            name: item.consignee,
            phone: item.phone,
            address: item.address,
            isDefault: item.isDefault
          }));

          this.setData({
            addresses: formattedAddresses,
            selectedAddressIndex: defaultIndex,
            currentAddress: formattedAddresses[defaultIndex]
          });

          console.log('已加载地址:', formattedAddresses[defaultIndex]);
        } else {
          // 如果没有地址，设置空数组
          this.setData({
            addresses: [],
            selectedAddressIndex: -1,
            currentAddress: null
          });

          // 如果当前是邮寄模式且没有地址，提示用户添加地址
          if (this.data.deliveryType === 'express') {
            wx.showToast({
              title: '请添加收货地址',
              icon: 'none',
              duration: 2000
            });
          }
        }
      })
      .catch(err => {
        console.error('获取地址列表失败:', err);
        // 设置空数组
        this.setData({
          addresses: [],
          selectedAddressIndex: -1,
          currentAddress: null
        });

        // 如果是未登录错误，跳转到登录页面
        if (err.message === '用户未登录') {
          wx.showToast({
            title: '请先登录',
            icon: 'none',
            duration: 2000,
            success: () => {
              setTimeout(() => {
                wx.navigateTo({
                  url: '/pages/login/login'
                });
              }, 2000);
            }
          });
        } else {
          wx.showToast({
            title: err.message || '获取地址失败',
            icon: 'none',
            duration: 2000
          });
        }
      })
      .finally(() => {
        wx.hideLoading();
      });
  },

  // 显示地址选择弹窗
  showAddressSelector: function () {
    // 隐藏订单确认面板，显示地址选择弹窗
    this.setData({
      showOrderConfirm: false,
      showAddressPopup: true
    });
  },

  // 隐藏地址选择弹窗
  hideAddressSelector: function () {
    this.setData({
      showAddressPopup: false
    });
  },

  // 选择地址
  selectAddress: function (e) {
    const index = e.currentTarget.dataset.index;
    const selectedAddress = this.data.addresses[index];

    // 更新选中的地址
    this.setData({
      selectedAddressIndex: index,
      currentAddress: selectedAddress,
      showAddressPopup: false // 隐藏地址选择弹窗
    });

    console.log('已选择地址:', selectedAddress);
  },

  // 跳转到地址管理页面
  goToAddressManage: function () {
    wx.navigateTo({
      url: '/pages/address/address?from=order'
    });
  },

  // 显示提货人选择
  showReceiverInput: function () {
    this.setData({
      showReceiverSelector: true
    });
  },

  // 隐藏提货人选择
  hideReceiverInput: function () {
    this.setData({
      showReceiverSelector: false
    });
  },

  // 设置提货人信息
  setReceiver: function (e) {
    const {
      name,
      phone
    } = e.detail.value;
    this.setData({
      'receiver.name': name,
      'receiver.phone': phone,
      showReceiverSelector: false
    });
  },

  // 显示提货时间选择
  showTimeSelector: function () {
    this.setData({
      showTimeSelector: true
    });
  },

  // 隐藏提货时间选择
  hideTimeSelector: function () {
    this.setData({
      showTimeSelector: false
    });
  },

  // 选择提货时间
  selectDeliveryTime: function (e) {
    const time = e.currentTarget.dataset.time;
    this.setData({
      deliveryTime: time,
      showTimeSelector: false
    });
  },

  // 显示备注输入
  showRemarkInput: function () {
    this.setData({
      showRemarkInput: true
    });
  },

  // 隐藏备注输入
  hideRemarkInput: function () {
    this.setData({
      showRemarkInput: false
    });
  },

  // 设置订单备注
  setOrderRemark: function (e) {
    this.setData({
      orderRemark: e.detail.value
    });
  },

  // 选择配送方式
  selectDeliveryType: function (e) {
    const type = e.currentTarget.dataset.type;
    this.setData({
      deliveryType: type
    });

    // 如果选择邮寄，检查地址
    if (type === 'express') {
      // 如果没有地址列表，先获取地址列表
      if (this.data.addresses.length === 0) {
        this.getAddresses();
      }

      // 如果没有选择地址，则显示地址选择器
      if (!this.data.currentAddress) {
        this.showAddressSelector();
      }
    }
  },

  // 提交订单
  submitOrder: function () {
    // 验证必填信息
    if (this.data.deliveryType === 'express' && !this.data.currentAddress) {
      wx.showToast({
        title: '请选择收货地址',
        icon: 'none'
      });
      return;
    }
    if (this.data.deliveryType === 'self' && (!this.data.receiver.name || !this.data.receiver.phone)) {
      wx.showToast({
        title: '请填写提货人信息',
        icon: 'none'
      });
      return;
    }
    if (this.data.deliveryType === 'self' && !this.data.deliveryTime) {
      wx.showToast({
        title: '请选择提货时间',
        icon: 'none'
      });
      return;
    }

    // 构建订单项
    const currentProduct = this.data.product;
    const selectedSizeInfo = this.data.sizes[this.data.selectedSizeIndex];
    let skuIdToUse = 0; // Default to 0 as per API example if specific SKU ID is not an integer
    if (selectedSizeInfo && selectedSizeInfo.id && !isNaN(parseInt(selectedSizeInfo.id))) {
      skuIdToUse = parseInt(selectedSizeInfo.id);
    } else {
      // If selectedSizeInfo.id is not a parseable integer, backend might infer SKU from productId and attributes,
      // or you might have a different way to get the integer SKU ID.
      // For now, using 0 or null if the backend handles it.
      console.warn('Could not determine a valid integer SKU ID. Sending 0 or null based on backend.');
      // skuIdToUse = null; // if your backend prefers null for unspecified optional integers
    }

    const orderItem = {
      productId: parseInt(currentProduct.id), // Ensure productId is an integer
      quantity: this.data.quantity,
      skuId: skuIdToUse
    };

    // 构建直接下单DTO
    const directOrderCreateDTO = {
      orderItems: [orderItem],
      remark: this.data.orderRemark || ''
    };

    if (this.data.deliveryType === 'express' && this.data.currentAddress && this.data.currentAddress.id) {
      directOrderCreateDTO.addressId = parseInt(this.data.currentAddress.id);
    } else {
      directOrderCreateDTO.addressId = null; // Or omit if backend prefers that for optional fields
    }

    console.log('提交直接下单数据 (DirectOrderCreateDTO):', directOrderCreateDTO);

    wx.showLoading({
      title: '提交订单中...',
      mask: true
    });

    // 调用新的创建订单API并进行支付
    this.callCreateOrderAPIAndPay(directOrderCreateDTO);
  },

  // 修改: 调用创建订单API并获取支付参数进行支付
  callCreateOrderAPIAndPay: function (directOrderCreateDTO) {
    const app = getApp();
    const request = require('../../utils/request.js'); // Assuming request.js handles base URL and auth tokens

    console.log('调用后端创建订单API: /user/orders/direct-create');

    request.post('/user/orders/direct-create', directOrderCreateDTO, {
      header: {
        'content-type': 'application/json'
      },
      success: (res) => {
        console.log('创建订单API响应:', res);
        if (res.data && (res.data.code === 1 || res.statusCode === 201 || res.statusCode === 200) && res.data.data) {
          const orderId = res.data.data; // Assuming res.data.data contains the orderId (integer)
          console.log('订单创建成功，订单ID:', orderId);

          // 调用支付接口获取支付参数
          const paymentData = {
            orderId: orderId,
            paymentMethod: "" // 根据API文档，paymentMethod可以为空字符串
          };

          console.log('调用支付API - 请求参数:', paymentData);

          request.post('/user/orders/pay', paymentData, {
            success: (payRes) => {
              console.log('获取支付参数成功:', payRes);

              if (payRes.data && payRes.data.code === 1 && payRes.data.data) {
                const paymentParams = payRes.data.data;

                // 发起支付
                wx.requestPayment({
                  timeStamp: paymentParams.timeStamp,
                  nonceStr: paymentParams.nonceStr,
                  package: paymentParams.package,
                  signType: paymentParams.signType,
                  paySign: paymentParams.paySign,
                  success: (paymentSuccessRes) => {
                    console.log('微信支付成功:', paymentSuccessRes);
                    wx.hideLoading();
                    wx.showToast({
                      title: '支付成功',
                      icon: 'success',
                      duration: 2000,
                      success: () => {
                        setTimeout(() => {
                          wx.navigateTo({
                            url: `/pages/order/order`
                          });
                        }, 2000);
                      }
                    });
                  },
                  fail: (paymentFailErr) => {
                    console.error('微信支付失败:', paymentFailErr);
                    wx.hideLoading();
                    wx.showToast({
                      title: '支付失败',
                      icon: 'none',
                      duration: 2000
                    });
                  }
                });
              } else {
                wx.hideLoading();
                console.error('获取支付参数失败:', payRes.data.msg || '未知错误');
                wx.showToast({
                  title: payRes.data.msg || '获取支付参数失败',
                  icon: 'none'
                });
              }
            },
            fail: (payErr) => {
              wx.hideLoading();
              console.error('支付接口请求失败:', payErr);
              wx.showToast({
                title: '支付请求失败',
                icon: 'none'
              });
            }
          });
        } else {
          wx.hideLoading();
          console.error('创建订单失败:', res.data.msg || '未知错误');
          wx.showToast({
            title: res.data.msg || '创建订单失败',
            icon: 'none'
          });
        }
      },
      fail: (err) => {
        wx.hideLoading();
        console.error('创建订单API请求失败:', err);
        wx.showToast({
          title: '订单请求失败',
          icon: 'none'
        });
      }
    });
  },

  // 处理购买弹窗内收货地址点击
  handleAddressClickInPopup: function () {
    // 隐藏订单确认面板，显示地址选择弹窗
    this.setData({
      showOrderConfirm: false,
      showAddressPopup: true
    });
  },

  // 详情图片加载成功
  onDetailImageLoad: function(e) {
    console.log('详情图片加载成功:', e.detail);
  },

  // 详情图片加载失败
  onDetailImageError: function(e) {
    console.error('详情图片加载失败:', e.detail);
    // 可以在这里设置默认图片或显示错误提示
  },

  // 预览详情图片
  previewDetailImage: function(e) {
    const src = e.currentTarget.dataset.src;
    const urls = this.data.product.detailImages || [];

    wx.previewImage({
      current: src,
      urls: urls
    });
  },

  // 头像加载失败处理
  onAvatarError: function(e) {
    console.log('头像加载失败，使用默认头像');
    // 可以设置默认头像
  },

  // 预览评价图片
  previewCommentImage: function(e) {
    const src = e.currentTarget.dataset.src;
    const images = e.currentTarget.dataset.images || [];

    wx.previewImage({
      current: src,
      urls: images
    });
  },

  // 预览商品轮播图
  previewImage: function(e) {
    const src = e.currentTarget.dataset.src;
    const urls = this.data.product.images || [];

    wx.previewImage({
      current: src,
      urls: urls
    });
  }
})