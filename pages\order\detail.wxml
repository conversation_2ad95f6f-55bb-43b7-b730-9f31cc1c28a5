<!--pages/order/detail.wxml-->
<view class="container">
  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading-container">
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 错误状态 -->
  <view wx:elif="{{error}}" class="error-container">
    <image src="/images/empty-cart.png" class="error-image" mode="aspectFit"></image>
    <text class="error-text">{{error}}</text>
    <button class="retry-btn" bindtap="loadOrderDetail">重新加载</button>
  </view>

  <!-- 订单详情内容 -->
  <view wx:else class="detail-content">
    <!-- 订单状态卡片 -->
    <view class="status-card">
      <view class="status-header">
        <view class="status-icon {{orderDetail.status === '待发货' ? 'pending' : orderDetail.status === '已取消' ? 'cancelled' : 'normal'}}">
          <text class="status-text">{{orderDetail.status}}</text>
        </view>
      </view>
      <view class="order-basic-info">
        <text class="order-number">订单号：{{orderDetail.orderNumber}}</text>
        <text class="order-time">下单时间：{{orderDetail.createdAt}}</text>
        <text class="update-time" wx:if="{{orderDetail.updatedAt}}">更新时间：{{orderDetail.updatedAt}}</text>
      </view>
    </view>

    <!-- 物流信息 -->
    <view class="shipping-card" wx:if="{{orderDetail.shipment && orderDetail.shipment.trackingNumber}}">
      <view class="card-header">
        <text class="card-title">物流信息</text>
      </view>

      <!-- 基础物流信息 -->
      <view class="shipping-content">
        <view class="shipping-item">
          <text class="shipping-label">物流公司：</text>
          <text class="shipping-value">{{expressInfo.company || orderDetail.shipment.shippingCompany || '暂无'}}</text>
        </view>
        <view class="shipping-item">
          <text class="shipping-label">运单号：</text>
          <text class="shipping-value">{{orderDetail.shipment.trackingNumber}}</text>
          <text class="copy-btn" bindtap="copyTrackingNumber">复制</text>
        </view>
        <view class="shipping-item" wx:if="{{orderDetail.shipment.shippingDate}}">
          <text class="shipping-label">发货时间：</text>
          <text class="shipping-value">{{orderDetail.shipment.shippingDate}}</text>
        </view>
        <view class="shipping-item" wx:if="{{expressInfo.statusDesc}}">
          <text class="shipping-label">物流状态：</text>
          <text class="shipping-value status-{{expressInfo.statusDetail}}">{{expressInfo.statusDesc}}</text>
        </view>
        <view class="shipping-item" wx:if="{{expressInfo.takeTime}}">
          <text class="shipping-label">运输耗时：</text>
          <text class="shipping-value">{{expressInfo.takeTime}}</text>
        </view>
        <view class="shipping-item" wx:if="{{expressInfo.courierPhone}}">
          <text class="shipping-label">快递员：</text>
          <text class="shipping-value">{{expressInfo.courierPhone}}</text>
          <text class="call-btn" bindtap="callCourier">联系</text>
        </view>
      </view>

      <!-- 物流轨迹 -->
      <view class="express-timeline" wx:if="{{expressInfo && expressInfo.list && expressInfo.list.length > 0}}">
        <view class="timeline-header" bindtap="toggleExpressDetail">
          <text class="timeline-title">物流轨迹</text>
          <text class="toggle-btn">{{showExpressDetail ? '收起' : '展开'}}</text>
        </view>

        <view class="timeline-content" wx:if="{{showExpressDetail}}">
          <view class="timeline-item" wx:for="{{expressInfo.list}}" wx:key="datetime">
            <view class="timeline-dot {{index === 0 ? 'active' : ''}}"></view>
            <view class="timeline-info">
              <text class="timeline-time">{{item.datetime}}</text>
              <text class="timeline-desc">{{item.remark}}</text>
            </view>
          </view>
        </view>

        <!-- 显示最新一条物流信息 -->
        <view class="latest-express" wx:else>
          <view class="latest-item" wx:if="{{expressInfo.list[0]}}">
            <text class="latest-time">{{expressInfo.list[0].datetime}}</text>
            <text class="latest-desc">{{expressInfo.list[0].remark}}</text>
          </view>
        </view>
      </view>

      <!-- 快递查询失败提示 -->
      <view class="express-error" wx:if="{{!expressLoading && !expressInfo && orderDetail.shipment.trackingNumber}}">
        <text class="error-text">暂无物流信息</text>
        <text class="retry-btn" bindtap="refreshExpressInfo">重新查询</text>
      </view>
    </view>

    <!-- 收货地址 -->
    <view class="address-card" wx:if="{{orderDetail.consignee}}">
      <view class="card-header">
        <text class="card-title">收货地址</text>
      </view>
      <view class="address-content">
        <view class="address-info">
          <text class="consignee-name">{{orderDetail.consignee}}</text>
          <text class="consignee-phone">{{orderDetail.phone}}</text>
        </view>
        <text class="address-detail">{{orderDetail.address}}</text>
      </view>
    </view>

    <!-- 商品信息 -->
    <view class="products-card">
      <view class="card-header">
        <text class="card-title">商品信息</text>
      </view>
      <view class="products-content">
        <view class="product-item" wx:for="{{orderDetail.orderItems}}" wx:key="skuId">
          <image src="{{item.imageUrl || '/images/product-default.jpg'}}"
                 class="product-image" mode="aspectFill"></image>
          <view class="product-details">
            <text class="product-name">{{item.productName}}</text>
            <view class="product-specs">
              <text class="spec-text">{{item.color}} {{item.size}}</text>
            </view>
            <view class="product-price-qty">
              <text class="product-price">¥{{item.price}}</text>
              <text class="product-quantity">x{{item.quantity}}</text>
            </view>
            <text class="product-subtotal">小计：¥{{item.subtotal}}</text>

            <!-- 评价按钮 - 仅在订单状态为已完成时显示 -->
            <view class="item-actions" wx:if="{{orderDetail.status === '已完成'}}">
              <button class="review-btn"
                      bindtap="showReviewPopup"
                      data-product-id="{{item.productId}}">评价</button>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 费用明细 -->
    <view class="payment-card">
      <view class="card-header">
        <text class="card-title">费用明细</text>
      </view>
      <view class="payment-content">
        <view class="payment-item">
          <text class="payment-label">商品总价：</text>
          <text class="payment-value">¥{{orderDetail.totalAmount}}</text>
        </view>
        <view class="payment-item">
          <text class="payment-label">运费：</text>
          <text class="payment-value">¥0.00</text>
        </view>
        <view class="payment-divider"></view>
        <view class="payment-item total">
          <text class="payment-label">实付款：</text>
          <text class="payment-value total-amount">¥{{orderDetail.totalAmount}}</text>
        </view>
        <view class="payment-item" wx:if="{{orderDetail.paymentMethod}}">
          <text class="payment-label">支付方式：</text>
          <text class="payment-value">{{orderDetail.paymentMethod}}</text>
        </view>
        <view class="payment-item" wx:if="{{orderDetail.paymentStatus}}">
          <text class="payment-label">支付状态：</text>
          <text class="payment-value">{{orderDetail.paymentStatus}}</text>
        </view>
      </view>
    </view>

    <!-- 订单备注 -->
    <view class="remark-card" wx:if="{{orderDetail.remark}}">
      <view class="card-header">
        <text class="card-title">订单备注</text>
      </view>
      <view class="remark-content">
        <text class="remark-text">{{orderDetail.remark}}</text>
      </view>
    </view>

    <!-- 已发货状态提示 -->
    <view class="shipping-status-tip" wx:if="{{orderDetail.status === '已发货'}}">
      <view class="tip-icon">📦</view>
      <view class="tip-content">
        <text class="tip-title">商品已发货</text>
        <text class="tip-desc">请确认收到商品后点击下方"确认收货"按钮</text>
      </view>
    </view>

    <!-- 待发货状态提示 -->
    <view class="pending-shipment-tip" wx:if="{{orderDetail.status === '待发货'}}">
      <view class="tip-icon">⏳</view>
      <view class="tip-content">
        <text class="tip-title">等待发货</text>
        <text class="tip-desc">商家正在准备您的商品，如需取消订单可点击下方"申请退款"</text>
      </view>
    </view>
  </view>

  <!-- 底部操作栏 -->
  <view class="bottom-actions" wx:if="{{!loading && !error}}">
    <!-- 待支付状态 -->
    <block wx:if="{{orderDetail.status === '待支付'}}">
      <button class="action-btn cancel-btn" bindtap="onCancelOrder">
        取消订单
      </button>
      <button class="action-btn primary-btn" bindtap="onPayOrder">
        立即支付
      </button>
    </block>

    <!-- 待收货状态 或 已发货状态 -->
    <block wx:elif="{{orderDetail.status === '待收货' || orderDetail.status === '已发货'}}">
      <button class="action-btn confirm-receive-btn" bindtap="onConfirmReceive">
        <text class="btn-icon">📦</text>
        <text class="btn-text">确认收货</text>
      </button>
    </block>

    <!-- 待发货状态 - 可申请退款 -->
    <block wx:elif="{{orderDetail.status === '待发货'}}">
      <button class="action-btn secondary-btn" bindtap="onRequestRefund">
        申请退款
      </button>
    </block>

    <!-- 已完成状态 - 不显示任何操作按钮 -->
    <block wx:elif="{{orderDetail.status === '已完成'}}">
      <!-- 已完成订单不显示操作按钮 -->
    </block>

    <!-- 返回按钮（所有状态都有） -->
    <button class="action-btn back-btn" bindtap="onGoBack">
      返回订单列表
    </button>
  </view>

  <!-- 评价弹窗 -->
  <view class="review-popup" wx:if="{{showReviewPopup}}">
    <view class="review-popup-mask" bindtap="hideReviewPopup"></view>
    <view class="review-popup-content">
      <view class="review-popup-header">
        <text class="review-popup-title">商品评价</text>
        <view class="review-popup-close" bindtap="hideReviewPopup">×</view>
      </view>

      <view class="review-popup-body">
        <!-- 评分 -->
        <view class="review-rating">
          <text class="review-label">评分</text>
          <view class="rating-stars">
            <view class="star {{index + 1 <= reviewRating ? 'active' : ''}}"
                  wx:for="{{5}}"
                  wx:key="index"
                  bindtap="onRatingChange"
                  data-value="{{index + 1}}">★</view>
          </view>
        </view>

        <!-- 评价内容 -->
        <view class="review-content">
          <text class="review-label">评价内容</text>
          <textarea class="review-textarea"
                    placeholder="请输入您的评价内容"
                    value="{{reviewContent}}"
                    bindinput="onReviewContentInput"
                    maxlength="500"></textarea>
        </view>

        <!-- 上传图片 -->
        <view class="review-images">
          <text class="review-label">上传图片（最多{{maxImageCount}}张）</text>
          <view class="image-list">
            <view class="image-item"
                  wx:for="{{reviewImages}}"
                  wx:key="index">
              <image src="{{item}}"
                     mode="aspectFill"
                     bindtap="previewReviewImage"
                     data-src="{{item}}"/>
              <view class="delete-btn"
                    catchtap="deleteReviewImage"
                    data-index="{{index}}">×</view>
            </view>
            <view class="upload-btn"
                  bindtap="chooseReviewImages"
                  wx:if="{{reviewImages.length < maxImageCount}}">
              <text class="upload-icon">+</text>
              <text class="upload-text">添加图片</text>
            </view>
          </view>
          <view class="upload-tip">支持JPG、PNG格式，单张图片不超过5MB</view>
        </view>
      </view>

      <view class="review-popup-footer">
        <button class="cancel-btn" bindtap="hideReviewPopup">取消</button>
        <button class="submit-btn" bindtap="submitReview">提交评价</button>
      </view>
    </view>
  </view>


</view>
