import request from '@/utils/request'

// 获取商品列表（分页）
export function getProductList(params) {
  return request({
    url: '/admin/products/page',
    method: 'get',
    params
  })
}

// 获取所有商品列表（不分页，可能用于下拉选择等）
export function getAllProducts() {
  return request({
    url: '/admin/products/all',
    method: 'get'
  })
}

// 根据ID获取商品详情
export function getProductDetail(id) {
  return request({
    url: `/admin/products/${id}`,
    method: 'get'
  })
}

// 添加商品
export function addProduct(data) {
  return request({
    url: '/admin/products/simple',
    method: 'post',
    headers: {
      'Content-Type': 'application/json'
    },
    data: {
      name: data.name,
      description: data.description,
      detailImages: data.detailImages || [],
      discountRate: data.discountRate || 0,
      imageUrl: data.imageUrl,
      isHot: data.isHot,
      isRecommended: data.isRecommended,
      originalPrice: data.originalPrice,
      status: data.status,
      subcategoryId: data.subcategoryId
    }
  })
}

// 更新商品
export function updateProduct(data) {
  return request({
    url: '/admin/products/simple',
    method: 'put',
    data
  })
}

// 删除商品
export function deleteProduct(id) {
  return request({
    url: `/admin/products/${id}`,
    method: 'delete'
  })
}

// 批量删除商品 (假设接口接收ID列表)
export function batchDeleteProducts(ids) {
  return request({
    url: '/admin/products/batchDelete',
    method: 'delete',
    data: ids // 假设后端接收一个ID数组在请求体中
  })
}

// 上传商品图片
export function uploadProductImage(data) {
  return request({
    url: '/admin/upload/image',
    method: 'post',
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    data
  })
}

// 更新商品状态
export function updateProductStatus(id, status) {
  // 将数字状态转换为字符串状态
  const statusString = status === 1 ? '上架' : '下架';
  return request({
    url: `/admin/products/status/${statusString}`,
    method: 'post',
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
    data: new URLSearchParams({ id: id, status: statusString }).toString()
  })
}

// 更新商品是否推荐状态
export function updateProductRecommended(id, status) {
  return request({
    url: `/admin/products/recommended/${status}`,
    method: 'post',
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
    data: new URLSearchParams({ id: id, status: status }).toString()
  })
}

// 更新商品热门状态
export function updateProductHot(id, status) {
  return request({
    url: `/admin/products/hot/${status}`,
    method: 'post',
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
    data: new URLSearchParams({ id }).toString()
  })
}

// 添加商品SKU
export function addProductSku(data) {
  return request({
    url: '/admin/product-skus/add',
    method: 'post',
    headers: {
      'Content-Type': 'application/json'
    },
    data: {
      attributes: data.attributes || {},
      price: data.price,
      productId: data.productId,
      stock: data.stock
    }
  })
}

// 批量添加SKU
export function batchAddSkus(data) {
  return request({
    url: '/admin/product-skus/batch-add',
    method: 'post',
    headers: {
      'Content-Type': 'application/json'
    },
    data
  })
}

// 更新单个SKU
export function updateProductSku(data) {
  return request({
    url: '/admin/product-skus/update',
    method: 'put',
    headers: {
      'Content-Type': 'application/json'
    },
    data
  })
}
