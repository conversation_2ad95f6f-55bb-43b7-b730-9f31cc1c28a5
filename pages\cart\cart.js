// pages/cart/cart.js
const cartApi = require('../../utils/cartApi.js');
const productApi = require('../../utils/productApi.js');

Page({
  data: {
    cartList: [], // 初始购物车为空
    allSelected: false,
    totalPrice: 0,
    totalCount: 0,
    loading: false,
    recommendList: []
  },

  onLoad: function (options) {
    this.loadCartData();
    this.loadRecommendProducts();
  },

  onShow: function () {
    // 页面显示时重新加载购物车数据
    this.loadCartData();
  },

  // 加载推荐商品
  loadRecommendProducts: function() {
    productApi.getRecommendProducts(10)
      .then(products => {
        if (products && Array.isArray(products)) {
          // 处理推荐商品数据
          const formattedProducts = products.map(item => ({
            id: item.id,
            name: item.name,
            price: item.currentPrice || item.price,
            imageUrl: item.imageUrl || (item.images && item.images.length > 0 ? item.images[0] : '/images/product-default.jpg'),
            tip: item.salesCount > 100 ? `本店销量榜第${Math.floor(Math.random() * 10) + 1}` : ''
          }));

          // 更新推荐商品列表
          this.setData({
            recommendList: formattedProducts
          });
        }
      })
      .catch(err => {
        console.error('获取推荐商品失败:', err);
      });
  },

  // 加载购物车数据
  loadCartData: function() {
    this.setData({ loading: true });

    cartApi.getCartList()
      .then(data => {
        // 处理返回的购物车数据
        const cartList = data.map(item => ({
          id: item.id,
          productId: item.productId,
          name: item.productName,
          price: item.price,
          imageUrl: item.imageUrl,
          count: item.quantity,
          skuId: item.skuId,
          selected: true, // 默认选中
          attributes: Object.entries(item.attributes || {}).map(([key, value]) => ({
            key,
            value
          }))
        }));

        this.setData({
          cartList: cartList,
          allSelected: cartList.length > 0 // 如果有商品，默认全选
        });

        this.calculateTotal();
      })
      .catch(err => {
        console.error('获取购物车失败:', err);
        wx.showToast({
          title: '获取购物车失败',
          icon: 'none'
        });
      })
      .finally(() => {
        this.setData({ loading: false });
      });
  },

  // 选择/取消选择单个商品
  selectItem: function(e) {
    const index = e.currentTarget.dataset.index;
    let cartList = this.data.cartList;
    cartList[index].selected = !cartList[index].selected;

    // 判断是否全选
    const allSelected = cartList.every(item => item.selected);

    this.setData({
      cartList: cartList,
      allSelected: allSelected
    });

    this.calculateTotal();
  },

  // 全选/取消全选
  selectAll: function() {
    const allSelected = !this.data.allSelected;
    let cartList = this.data.cartList;

    cartList.forEach(item => {
      item.selected = allSelected;
    });

    this.setData({
      cartList: cartList,
      allSelected: allSelected
    });

    this.calculateTotal();
  },

  // 增加商品数量
  addCount: function(e) {
    const index = e.currentTarget.dataset.index;
    let cartList = this.data.cartList;
    const item = cartList[index];
    const newCount = item.count + 1;

    // 先在UI上更新数量，提高响应速度
    cartList[index].count = newCount;
    this.setData({
      cartList: cartList
    });
    this.calculateTotal();

    // 调用API更新购物车
    wx.showLoading({ title: '更新中...' });
    cartApi.updateCartItem(item.id, newCount)
      .then(() => {
        console.log('购物车数量更新成功');
      })
      .catch(err => {
        console.error('更新购物车失败:', err);
        // 更新失败，恢复原来的数量
        cartList[index].count -= 1;
        this.setData({
          cartList: cartList
        });
        this.calculateTotal();

        wx.showToast({
          title: '更新失败',
          icon: 'none'
        });
      })
      .finally(() => {
        wx.hideLoading();
      });
  },

  // 减少商品数量
  minusCount: function(e) {
    const index = e.currentTarget.dataset.index;
    let cartList = this.data.cartList;
    const item = cartList[index];

    if (item.count > 1) {
      const newCount = item.count - 1;

      // 先在UI上更新数量，提高响应速度
      cartList[index].count = newCount;
      this.setData({
        cartList: cartList
      });
      this.calculateTotal();

      // 调用API更新购物车
      wx.showLoading({ title: '更新中...' });
      cartApi.updateCartItem(item.id, newCount)
        .then(() => {
          console.log('购物车数量更新成功');
        })
        .catch(err => {
          console.error('更新购物车失败:', err);
          // 更新失败，恢复原来的数量
          cartList[index].count += 1;
          this.setData({
            cartList: cartList
          });
          this.calculateTotal();

          wx.showToast({
            title: '更新失败',
            icon: 'none'
          });
        })
        .finally(() => {
          wx.hideLoading();
        });
    }
  },

  // 删除商品
  deleteItem: function(e) {
    const index = e.currentTarget.dataset.index;
    let cartList = this.data.cartList;
    const itemId = cartList[index].id;

    wx.showModal({
      title: '提示',
      content: '确定要删除这个商品吗？',
      success: (res) => {
        if (res.confirm) {
          // 先在UI上删除，提高响应速度
          const removedItem = cartList.splice(index, 1)[0];

          // 判断是否全选
          const allSelected = cartList.length > 0 ? cartList.every(item => item.selected) : false;

          this.setData({
            cartList: cartList,
            allSelected: allSelected
          });
          this.calculateTotal();

          // 调用API删除购物车项
          wx.showLoading({ title: '删除中...' });
          cartApi.removeFromCart(itemId)
            .then(() => {
              console.log('购物车商品删除成功');
              wx.showToast({
                title: '删除成功',
                icon: 'success'
              });
            })
            .catch(err => {
              console.error('删除购物车商品失败:', err);
              // 删除失败，恢复商品
              cartList.splice(index, 0, removedItem);
              this.setData({
                cartList: cartList,
                allSelected: cartList.every(item => item.selected)
              });
              this.calculateTotal();

              wx.showToast({
                title: '删除失败',
                icon: 'none'
              });
            })
            .finally(() => {
              wx.hideLoading();
            });
        }
      }
    });
  },

  // 计算总价和总数量
  calculateTotal: function() {
    let totalPrice = 0;
    let totalCount = 0;

    this.data.cartList.forEach(item => {
      if (item.selected) {
        totalPrice += item.price * item.count;
        totalCount += item.count;
      }
    });

    this.setData({
      totalPrice: totalPrice.toFixed(2),
      totalCount: totalCount
    });
  },

  // 去结算
  goToCheckout: function() {
    if (this.data.totalCount <= 0) {
      wx.showToast({
        title: '请选择要结算的商品',
        icon: 'none'
      });
      return;
    }

    // 获取选中的商品
    const selectedItems = this.data.cartList.filter(item => item.selected);

    // 构建订单创建DTO
    const orderCreateDTO = {
      cartItemIds: selectedItems.map(item => item.id), // 购物车项ID列表
      remark: '', // 订单备注，默认为空
      addressId: null // 地址ID，需要用户选择
    };

    // 显示地址选择弹窗
    wx.showLoading({ title: '加载地址...' });

    // 获取用户地址列表
    const addressApi = require('../../utils/addressApi.js');
    addressApi.getAddressList()
      .then(addresses => {
        wx.hideLoading();

        if (!addresses || addresses.length === 0) {
          wx.showModal({
            title: '提示',
            content: '您还没有收货地址，是否前往添加？',
            success: (res) => {
              if (res.confirm) {
                wx.navigateTo({
                  url: '/pages/address/address'
                });
              }
            }
          });
          return Promise.reject('no_address');
        }

        // 找到默认地址或使用第一个地址
        const defaultAddress = addresses.find(addr => addr.isDefault) || addresses[0];
        orderCreateDTO.addressId = defaultAddress.id;

        // 创建订单
        return this.createOrder(orderCreateDTO);
      })
      .then(orderId => {
        if (orderId) {
          // 获取支付参数
          return this.getPaymentParams(orderId);
        }
      })
      .then(paymentParams => {
        if (paymentParams) {
          // 发起支付
          return this.requestPayment(paymentParams);
        }
      })
      .then(() => {
        // 支付成功后清理购物车选中项
        this.clearSelectedItems();

        // 跳转到订单列表页
        wx.redirectTo({
          url: '/pages/order/order'
        });
      })
      .catch(err => {
        if (err === 'no_address') {
          return; // 已经显示了添加地址的提示，不需要额外处理
        }
        console.error('结算失败:', err);
        wx.showToast({
          title: '结算失败',
          icon: 'none'
        });
      });
  },

  // 创建订单
  createOrder: function(orderCreateDTO) {
    return new Promise((resolve, reject) => {
      const request = require('../../utils/request.js');

      wx.showLoading({ title: '创建订单...' });
      request.post('/user/orders/create', orderCreateDTO, {
        success: (res) => {
          wx.hideLoading();
          if (res.data && res.data.code === 1 && res.data.data) {
            resolve(res.data.data); // 返回订单ID
          } else {
            reject(new Error(res.data.msg || '创建订单失败'));
          }
        },
        fail: reject
      });
    });
  },

  // 获取支付参数
  getPaymentParams: function(orderId) {
    return new Promise((resolve, reject) => {
      const request = require('../../utils/request.js');

      wx.showLoading({ title: '获取支付参数...' });

      // 使用新的支付API
      const paymentData = {
        orderId: orderId,
        paymentMethod: "" // 根据API文档，paymentMethod可以为空字符串
      };

      console.log('调用支付API - 请求参数:', paymentData);

      request.post('/user/orders/pay', paymentData, {
        success: (res) => {
          wx.hideLoading();
          console.log('支付API响应:', res);

          if (res.data && (res.data.code === 0 || res.data.code === 1) && res.data.data) {
            console.log('获取支付参数成功:', res.data.data);
            resolve(res.data.data);
          } else {
            console.error('获取支付参数失败:', res.data);
            reject(new Error(res.data.msg || '获取支付参数失败'));
          }
        },
        fail: (err) => {
          wx.hideLoading();
          console.error('支付API请求失败:', err);
          reject(err);
        }
      });
    });
  },

  // 发起支付
  requestPayment: function(paymentParams) {
    return new Promise((resolve, reject) => {
      wx.requestPayment({
        timeStamp: paymentParams.timeStamp,
        nonceStr: paymentParams.nonceStr,
        package: paymentParams.package,
        signType: paymentParams.signType || 'MD5',
        paySign: paymentParams.paySign,
        success: resolve,
        fail: reject
      });
    });
  },

  // 清理已选中的购物车项
  clearSelectedItems: function() {
    const cartList = this.data.cartList.filter(item => !item.selected);
    this.setData({
      cartList: cartList,
      allSelected: false
    });
    this.calculateTotal();
  },

  // 从推荐商品添加到购物车
  addToCart: function(e) {
    const id = e.currentTarget.dataset.id;
    const recommendItem = this.data.recommendList.find(item => item.id === id);

    if (recommendItem) {
      // 检查购物车中是否已有该商品
      const cartList = this.data.cartList;
      const existingItemIndex = cartList.findIndex(item => item.productId === id);

      if (existingItemIndex >= 0) {
        // 如果已有该商品，增加数量
        const newCount = cartList[existingItemIndex].count + 1;

        // 先在UI上更新数量
        cartList[existingItemIndex].count = newCount;
        this.setData({
          cartList: cartList
        });
        this.calculateTotal();

        // 调用API更新购物车
        wx.showLoading({ title: '更新中...' });
        cartApi.updateCartItem(cartList[existingItemIndex].id, newCount)
          .then(() => {
            console.log('购物车数量更新成功');
          })
          .catch(err => {
            console.error('更新购物车失败:', err);
            // 更新失败，恢复原来的数量
            cartList[existingItemIndex].count -= 1;
            this.setData({
              cartList: cartList
            });
            this.calculateTotal();

            wx.showToast({
              title: '更新失败',
              icon: 'none'
            });
          })
          .finally(() => {
            wx.hideLoading();
          });
      } else {
        // 如果没有该商品，添加到购物车
        wx.showLoading({ title: '添加中...' });
        cartApi.addToCart({
          id: recommendItem.id,
          quantity: 1
        })
          .then(result => {
            console.log('添加到购物车成功:', result);

            // 重新加载购物车数据
            return this.loadCartData();
          })
          .then(() => {
            // 提示添加成功
            wx.showToast({
              title: '已添加到购物车',
              icon: 'success'
            });
          })
          .catch(err => {
            console.error('添加到购物车失败:', err);
            wx.showToast({
              title: '添加失败',
              icon: 'none'
            });
          })
          .finally(() => {
            wx.hideLoading();
          });
      }
    }
  }
})