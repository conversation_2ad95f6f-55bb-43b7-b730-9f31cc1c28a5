/* pages/address/edit.wxss */
.container {
  padding: 0;
  background-color: #f8f8f8;
  min-height: 100vh;
}

/* 表单样式 */
.form-group {
  background-color: #fff;
  padding: 0 30rpx;
  margin-top: 20rpx;
}

.form-item {
  display: flex;
  padding: 30rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
  position: relative;
}

.form-item:last-child {
  border-bottom: none;
}

.form-label {
  width: 180rpx;
  font-size: 28rpx;
  color: #333;
  padding-top: 6rpx;
}

.form-input {
  flex: 1;
  position: relative;
}

.form-input input, .form-input textarea, .form-input .picker {
  width: 100%;
  font-size: 28rpx;
  color: #333;
}

.form-input .placeholder, .form-input input::placeholder, .form-input textarea::placeholder {
  color: #999;
}

.form-input textarea {
  min-height: 80rpx;
  line-height: 1.5;
}

.arrow {
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  color: #ccc;
  font-size: 28rpx;
}

.switch-item .form-input {
  display: flex;
  justify-content: flex-end;
}

/* 错误提示 */
.error-tip {
  font-size: 24rpx;
  color: #ff4c6a;
  padding: 0 0 20rpx 180rpx;
  margin-top: -20rpx;
}

/* 底部按钮 */
.form-actions {
  padding: 60rpx 30rpx;
}

.save-btn {
  background-color: #8ab6d6;
  color: #fff;
  font-size: 32rpx;
  height: 88rpx;
  line-height: 88rpx;
  border-radius: 44rpx;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.save-btn.disabled {
  opacity: 0.6;
}
