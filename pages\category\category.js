// pages/category/category.js
const request = require('../../utils/request.js');

Page({
  data: {
    // 分类数据
    categories: [],
    activeCategory: 0,
    activeSubCategory: 0, // 当前选中的子分类ID
    subCategories: [],
    products: [],
    loading: false,

    // 搜索相关
    searchKeyword: '', // 搜索关键词
    searchFocus: false, // 搜索框是否获取焦点
    isSearching: false, // 是否处于搜索状态
    searchResults: [] // 搜索结果
  },

  onLoad: function (options) {
    // 加载商品分类数据
    this.loadCategories(options);
  },

  onShow: function() {
    // 页面显示时，如果没有分类数据，则重新加载
    if (this.data.categories.length === 0) {
      this.loadCategories();
    }
  },

  // 加载商品分类数据
  loadCategories: function(options) {
    wx.showLoading({
      title: '加载中...',
    });

    this.setData({
      loading: true
    });

    request.get('/user/categories/list', {}, {
      success: (res) => {
        wx.hideLoading();

        if (res.data.code === 1 && res.data.data && res.data.data.length > 0) {
          const categories = res.data.data;

          // 设置默认选中的分类
          let activeCategory = categories[0].id;

          // 如果有传入分类ID，则设置为当前选中分类
          if (options && options.id) {
            const categoryId = parseInt(options.id);
            // 检查传入的分类ID是否存在
            const category = categories.find(item => item.id === categoryId);
            if (category) {
              activeCategory = categoryId;
            }
          }

          this.setData({
            categories: categories,
            activeCategory: activeCategory,
            loading: false
          });

          // 加载子分类数据
          this.loadSubCategories(activeCategory);
        } else {
          this.setData({
            loading: false
          });

          wx.showToast({
            title: '获取分类失败',
            icon: 'none'
          });
        }
      },
      fail: () => {
        wx.hideLoading();

        this.setData({
          loading: false
        });

        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        });
      }
    });
  },

  // 切换分类
  switchCategory: function(e) {
    const categoryId = e.currentTarget.dataset.id;

    // 如果点击的是当前选中的分类，则不做任何操作
    if (categoryId === this.data.activeCategory) {
      return;
    }

    this.setData({
      activeCategory: categoryId,
      activeSubCategory: 0, // 重置子分类选择
      subCategories: [], // 清空子分类数据
      products: [] // 清空商品数据
    });

    // 加载子分类数据
    this.loadSubCategories(categoryId);
  },

  // 切换子分类
  switchSubCategory: function(e) {
    const subcategoryId = parseInt(e.currentTarget.dataset.id);

    // 如果点击的是当前选中的子分类，则不做任何操作
    if (subcategoryId === this.data.activeSubCategory && subcategoryId !== 0) {
      return;
    }

    this.setData({
      activeSubCategory: subcategoryId,
      products: [] // 清空商品数据
    });

    // 如果选择的是"全部"，则加载大类下所有商品
    if (subcategoryId === 0) {
      this.loadProducts(this.data.activeCategory);
    } else {
      // 加载该子分类下的商品
      this.loadProductsBySubCategory(this.data.activeCategory, subcategoryId);
    }
  },

  // 根据子分类ID加载商品
  loadProductsBySubCategory: function(categoryId, subcategoryId) {
    if (!categoryId || !subcategoryId) {
      return;
    }

    wx.showLoading({
      title: '加载中...',
    });

    // 构建请求参数
    const params = {
      categoryId: categoryId,
      subcategoryId: subcategoryId,
      // 默认排序方式
      orderBy: 'time',
      orderDirection: 'desc'
    };

    // 调用商品搜索API
    request.get('/user/products/search', params, {
      success: (res) => {
        wx.hideLoading();

        if (res.data.code === 0 || res.data.code === 1) {
          // 处理返回的商品数据
          const products = res.data.data || [];

          // 格式化商品数据，适配页面显示
          const formattedProducts = products.map(item => {
            return {
              id: item.id,
              name: item.name,
              price: item.currentPrice,
              originalPrice: item.originalPrice,
              imageUrl: item.imageUrl,
              sales: item.salesCount || 0,
              subcategoryId: item.subcategoryId,
              subcategoryName: item.subcategoryName,
              status: item.status,
              totalStock: item.totalStock
            };
          });

          this.setData({
            products: formattedProducts
          });

          console.log('加载子分类商品成功:', formattedProducts);
        } else {
          this.setData({
            products: []
          });

          wx.showToast({
            title: res.data.msg || '获取商品失败',
            icon: 'none'
          });
        }
      },
      fail: (err) => {
        wx.hideLoading();
        console.error('请求子分类商品数据失败:', err);

        this.setData({
          products: []
        });

        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        });
      }
    });
  },

  // 加载子分类
  loadSubCategories: function(categoryId) {
    if (!categoryId) {
      return;
    }

    wx.showLoading({
      title: '加载中...',
    });

    request.get(`/user/subcategories/byCategoryId/${categoryId}`, {}, {
      success: (res) => {
        wx.hideLoading();

        if (res.data.code === 1) {
          // 添加"全部"选项到子分类列表
          const allSubCategories = [
            { id: 0, name: '全部', categoryId: categoryId }
          ].concat(res.data.data || []);

          this.setData({
            subCategories: allSubCategories,
            activeSubCategory: 0 // 默认选中"全部"
          });

          // 加载商品数据
          this.loadProducts(categoryId);
        } else {
          wx.showToast({
            title: res.data.msg || '获取子分类失败',
            icon: 'none'
          });
        }
      },
      fail: () => {
        wx.hideLoading();

        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        });
      }
    });
  },

  // 加载商品数据 - 根据大类ID加载所有商品
  loadProducts: function(categoryId) {
    if (!categoryId) {
      return;
    }

    wx.showLoading({
      title: '加载中...',
    });

    // 构建请求参数
    const params = {
      categoryId: categoryId,
      // 默认排序方式
      orderBy: 'time',
      orderDirection: 'desc'
    };

    // 调用商品搜索API
    request.get('/user/products/search', params, {
      success: (res) => {
        wx.hideLoading();

        if (res.data.code === 0 || res.data.code === 1) {
          // 处理返回的商品数据
          const products = res.data.data || [];

          // 格式化商品数据，适配页面显示
          const formattedProducts = products.map(item => {
            return {
              id: item.id,
              name: item.name,
              price: item.currentPrice,
              originalPrice: item.originalPrice,
              imageUrl: item.imageUrl,
              sales: item.salesCount || 0,
              subcategoryId: item.subcategoryId,
              subcategoryName: item.subcategoryName,
              status: item.status,
              totalStock: item.totalStock
            };
          });

          this.setData({
            products: formattedProducts
          });

          console.log('加载商品成功:', formattedProducts);
        } else {
          this.setData({
            products: []
          });

          wx.showToast({
            title: res.data.msg || '获取商品失败',
            icon: 'none'
          });
        }
      },
      fail: (err) => {
        wx.hideLoading();
        console.error('请求商品数据失败:', err);

        this.setData({
          products: []
        });

        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        });
      }
    });
  },

  // 跳转到商品详情
  navigateToDetail: function(e) {
    const id = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: '/pages/product/product?id=' + id
    });
  },

  // 搜索输入事件处理
  onSearchInput: function(e) {
    // 获取原始输入文本，不进行trim()处理
    const keyword = e.detail.value;
    this.setData({
      searchKeyword: keyword
    });
  },

  // 搜索确认事件处理
  onSearchConfirm: function(e) {
    // 获取原始输入文本，不进行trim()处理
    const keyword = e.detail.value;
    if (keyword) {
      this.setData({
        searchKeyword: keyword,
        isSearching: true
      });

      console.log('搜索关键词:', keyword);

      // 执行搜索，直接传递原始文本
      this.searchProducts(keyword);
    }
  },

  // 清除搜索
  clearSearch: function() {
    this.setData({
      searchKeyword: '',
      isSearching: false
    });

    // 恢复原来的分类显示
    if (this.data.activeSubCategory === 0) {
      this.loadProducts(this.data.activeCategory);
    } else {
      this.loadProductsBySubCategory(this.data.activeCategory, this.data.activeSubCategory);
    }
  },

  // 搜索商品
  searchProducts: function(keyword) {
    if (!keyword) {
      return;
    }

    wx.showLoading({
      title: '搜索中...',
    });

    // 直接使用原始文本作为关键词，不进行编码处理
    // 构建搜索参数 - 只使用关键词搜索，不限制分类
    const params = {
      keyword: keyword, // 直接传递文本关键词
      // 默认排序方式
      orderBy: 'time',
      orderDirection: 'desc'
    };

    console.log('搜索参数:', params);

    // 调用商品搜索API
    request.get('/user/products/search', params, {
      success: (res) => {
        wx.hideLoading();

        if (res.data.code === 0 || res.data.code === 1) {
          // 处理返回的商品数据
          const products = res.data.data || [];

          // 格式化商品数据，适配页面显示
          const formattedProducts = products.map(item => {
            return {
              id: item.id,
              name: item.name,
              price: item.currentPrice,
              originalPrice: item.originalPrice,
              imageUrl: item.imageUrl,
              sales: item.salesCount || 0,
              subcategoryId: item.subcategoryId,
              subcategoryName: item.subcategoryName,
              status: item.status,
              totalStock: item.totalStock
            };
          });

          this.setData({
            products: formattedProducts
          });

          // 显示搜索结果数量
          if (formattedProducts.length === 0) {
            wx.showToast({
              title: '未找到相关商品',
              icon: 'none'
            });
          } else {
            wx.showToast({
              title: `找到${formattedProducts.length}个商品`,
              icon: 'none'
            });
          }

          console.log('搜索结果:', formattedProducts);
        } else {
          this.setData({
            products: []
          });

          wx.showToast({
            title: res.data.msg || '搜索失败',
            icon: 'none'
          });
        }
      },
      fail: (err) => {
        wx.hideLoading();
        console.error('搜索请求失败:', err);

        this.setData({
          products: []
        });

        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        });
      }
    });
  }
})
