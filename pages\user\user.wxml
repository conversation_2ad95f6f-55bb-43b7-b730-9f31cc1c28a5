<!--pages/user/user.wxml-->
<view class="container">

  <!-- 用户信息 -->
  <view class="user-info-container">
    <!-- 未登录状态 -->
    <block wx:if="{{!isLoggedIn}}">
      <view class="login-section">
        <button class="login-tip" open-type="getUserInfo" bindgetuserinfo="onGetUserInfo">微信一键登录</button>
      </view>

    </block>

    <!-- 已登录状态 -->
    <block wx:else>
      <view class="user-profile">
        <view class="avatar-container">
          <image class="avatar" src="{{userInfo.avatarUrl || '/images/default-avatar.png'}}" mode="aspectFill"></image>
        </view>
        <view class="user-details">
          <text class="username">{{userInfo.nickName || '用户'}}</text>
        </view>
        <view class="settings-btn" bindtap="logout">
          <text>退出登录</text>
        </view>
      </view>


    </block>

    <!-- 心情语录 -->
    <view class="mood-quote">
        <text class="quote-cn">有你对我来说是最重要的事</text>
        <text class="quote-en">The happiest thing for me is having you</text>
      </view>
  </view>

  <!-- 我的订单 -->
  <view class="order-section">
    <view class="section-header">
      <text class="section-title">我的订单</text>
      <navigator url="/pages/order/order" class="view-all">查看全部订单 ></navigator>
    </view>

    <view class="order-menu">
      <view class="order-menu-item" bindtap="navigateToOrder" data-status="待支付">
        <image src="/images/icon-unpaid.png" class="menu-icon-1" />
        <text class="menu-text">待付款</text>
      </view>
      <view class="order-menu-item" bindtap="navigateToOrder" data-status="待发货">
        <image src="/images/icon-unshipped.png" class="menu-icon-1" />
        <text class="menu-text">待发货</text>
      </view>
      <view class="order-menu-item" bindtap="navigateToOrder" data-status="待收货">
        <image src="/images/icon-shipped.png" class="menu-icon-1" />
        <text class="menu-text">待收货</text>
      </view>
      <view class="order-menu-item" bindtap="navigateToOrder" data-status="已完成">
        <image src="/images/icon-review.png" class="menu-icon-1" />
        <text class="menu-text">已完成</text>
      </view>
      <view class="order-menu-item" bindtap="navigateToOrder" data-status="已取消">
        <image src="/images/icon-aftersale.png" class="menu-icon-1" />
        <text class="menu-text">已取消</text>
      </view>
    </view>
  </view>

  <!-- 功能菜单 -->
  <view class="menu-container">
    <view class="menu-item" bind:tap="navigateToCart">
      <view class="menu-left">
        <image src="/images/icon-cart.png" class="menu-icon" />
        <text class="menu-name">购物车</text>
      </view>
      <view class="menu-right">
        <text class="arrow">></text>
      </view>
    </view>
  </view>

  <!-- 设置菜单 -->
  <view class="settings-container">

    <navigator url="/pages/address/address" class="settings-item">
      <view class="menu-left">
        <image src="/images/icon-address.png" class="menu-icon" />
        <text class="menu-name">收货地址</text>
      </view>
      <view class="menu-right">
        <text class="arrow">></text>
      </view>
    </navigator>

    <navigator url="/pages/user/profile/profile" class="settings-item">
      <view class="menu-left">
        <image src="/images/icon-user.png" class="menu-icon" />
        <text class="menu-name">个人信息</text>
      </view>
      <view class="menu-right">
        <text class="arrow">></text>
      </view>
    </navigator>

    <navigator url="/pages/security/security" class="settings-item">
      <view class="menu-left">
        <image src="/images/icon-security.png" class="menu-icon" />
        <text class="menu-name">账号与安全</text>
      </view>
      <view class="menu-right">
        <text class="arrow">></text>
      </view>
    </navigator>
  </view>

</view>
