{"description": "项目配置文件", "packOptions": {"ignore": [], "include": []}, "miniprogramRoot": "", "compileType": "miniprogram", "projectname": "宠物衣服商城小程序", "setting": {"useCompilerPlugins": ["sass"], "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "es6": true, "enhance": true, "postcss": true, "minified": true, "ignoreUploadUnusedFiles": true, "compileWorklet": false, "uglifyFileName": false, "uploadWithSourceMap": true, "packNpmManually": false, "packNpmRelationList": [], "minifyWXSS": true, "minifyWXML": true, "localPlugins": false, "disableUseStrict": false, "condition": false, "swc": false, "disableSWC": true}, "condition": {}, "editorSetting": {"tabIndent": "insertSpaces", "tabSize": 2}, "libVersion": "2.30.2", "srcMiniprogramRoot": "", "appid": "wxb993541dfb7b5375", "simulatorPluginLibVersion": {}}