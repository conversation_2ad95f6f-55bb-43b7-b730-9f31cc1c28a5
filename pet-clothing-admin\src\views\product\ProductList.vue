<template>
  <div class="product-list-container">
    <!-- 搜索和操作区域 -->
    <el-card class="search-card" shadow="never">
      <div class="search-operations">
        <el-form :inline="true" :model="searchForm" class="search-form">
          <el-form-item label="商品名称">
            <el-input v-model="searchForm.name" placeholder="请输入商品名称" clearable />
          </el-form-item>
          <el-form-item label="商品状态">
            <el-select v-model="searchForm.status" placeholder="请选择状态" clearable style="width: 100px">
              <el-option label="上架" :value="1" />
              <el-option label="下架" :value="0" />
            </el-select>
          </el-form-item>
          <el-form-item label="商品大类">
            <el-select v-model="searchForm.categoryId" placeholder="请选择大类" clearable style="width: 140px">
              <el-option v-for="item in categoryOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item label="商品小类">
            <el-select v-model="searchForm.subcategoryId" placeholder="请选择小类" clearable style="width: 140px">
              <el-option v-for="item in subcategoryOptions" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item label="价格区间">
            <el-input v-model="searchForm.minPrice" placeholder="最低价" style="width: 90px; margin-right: 8px;" />
            <el-input v-model="searchForm.maxPrice" placeholder="最高价" style="width: 90px;" />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">搜索</el-button>
            <el-button @click="resetSearch">重置</el-button>
          </el-form-item>
        </el-form>
        <div class="operation-buttons">
          <el-button type="primary" @click="handleAdd">添加商品</el-button>
          <el-button type="danger" :disabled="!selectedProducts.length" @click="handleBatchDelete">批量删除</el-button>
        </div>
      </div>
    </el-card>

    <!-- 商品列表 -->
    <el-card class="product-table-card" shadow="never">
      <el-table
        v-loading="loading"
        :data="productList"
        border
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column label="商品图片" width="100">
          <template #default="scope">
            <el-image
              :src="scope.row.imageUrl"
              :preview-src-list="[scope.row.image]"
              fit="cover"
              style="width: 60px; height: 60px"
            >
              <template #error>
                <div class="image-slot">加载失败</div>
              </template>
            </el-image>
          </template>
        </el-table-column>
        <el-table-column prop="name" label="商品名称" min-width="200" show-overflow-tooltip />
        <el-table-column label="价格" width="120">
          <template #default="scope">
            <div>原价：¥{{ scope.row.originalPrice.toFixed(2) }}</div>
            <div>现价：¥{{ scope.row.currentPrice.toFixed(2) }}</div>
          </template>
        </el-table-column>
        <el-table-column prop="salesCount" label="销量" width="80" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.status === '上架' ? 'success' : 'info'">
              {{ scope.row.status }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="热门" width="100">
          <template #default="scope">
            <el-switch
              v-model="scope.row.isHot"
              :active-value="true"
              :inactive-value="false"
              @change="handleHotChange(scope.row)"
            />
          </template>
        </el-table-column>
        <el-table-column label="推荐" width="100">
          <template #default="scope">
            <el-switch
              v-model="scope.row.isRecommended"
              :active-value="true"
              :inactive-value="false"
              @change="handleRecommendedChange(scope.row)"
            />
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="scope">
            <el-button type="primary" link @click="handleEdit(scope.row)">编辑</el-button>
            <el-button type="primary" link @click="handleView(scope.row)">查看</el-button>
            <el-button type="danger" link @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getProductList, deleteProduct, updateProductStatus, updateProductHot, updateProductRecommended } from '@/api/product'
import { getCategoryList, getSubcategoriesByCategoryId } from '@/api/category'

const router = useRouter()
const loading = ref(false)
const productList = ref([])
const selectedProducts = ref([])
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(10)

// 分类选项
const categoryOptions = ref([])
const subcategoryOptions = ref([])

// 搜索表单
const searchForm = reactive({
  name: '',
  status: '',
  categoryId: '',
  subcategoryId: '',
  minPrice: '',
  maxPrice: ''
})

// 获取大类和小类
const fetchCategories = async () => {
  try {
    const res = await getCategoryList()
    if (res.code === 1) {
      categoryOptions.value = res.data.map(item => ({ value: item.id, label: item.name }))
    }
  } catch {}
}

const fetchSubcategories = async (categoryId) => {
  if (!categoryId) {
    subcategoryOptions.value = []
    return
  }
  try {
    const res = await getSubcategoriesByCategoryId(categoryId)
    if (res.code === 1) {
      subcategoryOptions.value = res.data.map(item => ({ value: item.id, label: item.name }))
    } else {
      subcategoryOptions.value = []
    }
  } catch {
    subcategoryOptions.value = []
  }
}

// 监听大类变化，自动加载小类
watch(() => searchForm.categoryId, (val) => {
  searchForm.subcategoryId = ''
  fetchSubcategories(val)
})

// 获取商品列表
const fetchProductList = async () => {
  loading.value = true
  try {
    const params = {
      page: currentPage.value,
      pageSize: pageSize.value,
      name: searchForm.name || undefined,
      status: searchForm.status === '' ? undefined : (searchForm.status === 1 ? '上架' : '下架'),
      categoryId: searchForm.categoryId || undefined,
      subcategoryId: searchForm.subcategoryId || undefined,
      minPrice: searchForm.minPrice || undefined,
      maxPrice: searchForm.maxPrice || undefined
    }
    const res = await getProductList(params)
    if (res.code === 1) {
      productList.value = res.data.records || []
      total.value = res.data.total || 0
    } else {
      ElMessage.error(res.msg || '获取商品列表失败')
    }
  } catch (error) {
    console.error('获取商品列表失败:', error)
    ElMessage.error('获取商品列表失败')
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  currentPage.value = 1
  fetchProductList()
}

// 重置搜索
const resetSearch = () => {
  Object.keys(searchForm).forEach(key => {
    searchForm[key] = ''
  })
  currentPage.value = 1
  fetchProductList()
}

// 添加商品
const handleAdd = () => {
  router.push('/product/add')
}

// 编辑商品
const handleEdit = (row) => {
  router.push(`/product/edit/${row.id}`)
}

// 查看商品
const handleView = (row) => {
  // 可以实现查看详情的逻辑，例如打开一个对话框
  console.log('查看商品:', row)
}

// 删除商品
const handleDelete = (row) => {
  ElMessageBox.confirm('确定要删除该商品吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      await deleteProduct(row.id)
      ElMessage.success('删除成功')
      fetchProductList()
    } catch (error) {
      console.error('删除商品失败:', error)
      ElMessage.error('删除商品失败')
    }
  }).catch(() => {})
}

// 批量删除
const handleBatchDelete = () => {
  if (selectedProducts.value.length === 0) {
    ElMessage.warning('请选择要删除的商品')
    return
  }
  ElMessageBox.confirm(`确定要删除选中的 ${selectedProducts.value.length} 个商品吗？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      await Promise.all(selectedProducts.value.map(product => deleteProduct(product.id)))
      ElMessage.success('批量删除成功')
      fetchProductList()
    } catch (error) {
      console.error('批量删除商品失败:', error)
      ElMessage.error('批量删除商品失败')
      fetchProductList()
    }
  }).catch(() => {})
}

// 更改商品状态
const handleStatusChange = async (row) => {
  try {
    await updateProductStatus(row.id, row.status)
    ElMessage.success(`商品已${row.status === '上架' ? '上架' : '下架'}`)
  } catch (error) {
    console.error('更新商品状态失败:', error)
    ElMessage.error('更新商品状态失败')
    // 恢复原状态
    row.status = row.status === '上架' ? '下架' : '上架'
  }
}

// 更改商品热门状态
const handleHotChange = async (row) => {
  try {
    // isHot 在您的API示例中是boolean类型
    const status = row.isHot ? '上架' : '下架'
    await updateProductStatus(row.id, status)
    ElMessage.success(`商品已设置为${row.isHot ? '上架' : '下架'}`)
  } catch (error) {
    console.error('更新商品状态失败:', error)
    ElMessage.error('更新商品状态失败')
    // 恢复原状态
    row.status = row.status === '上架' ? '下架' : '上架'
  }
}

// 更改商品推荐状态
const handleRecommendedChange = async (row) => {
  try {
    // isRecommended 在您的API示例中是boolean类型
    const status = row.isRecommended ? '上架' : '下架'
    await updateProductStatus(row.id, status)
    ElMessage.success(`商品已设置为${row.isRecommended ? '上架' : '下架'}`)
  } catch (error) {
    console.error('更新商品状态失败:', error)
    ElMessage.error('更新商品状态失败')
    // 恢复原状态
    row.status = row.status === '上架' ? '下架' : '上架'
  }
}

// 表格选择变化
const handleSelectionChange = (selection) => {
  selectedProducts.value = selection
}

// 页码变化
const handleCurrentChange = (page) => {
  currentPage.value = page
  fetchProductList()
}

// 每页条数变化
const handleSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1
  fetchProductList()
}

onMounted(() => {
  fetchCategories()
  fetchProductList()
})
</script>

<style scoped>
.product-list-container {
  padding: 20px;
}

.search-card {
  margin-bottom: 20px;
}

.search-operations {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
}

.search-form {
  flex: 1;
  min-width: 600px;
}

.operation-buttons {
  display: flex;
  align-items: flex-start;
  margin-left: 20px;
}

.product-table-card {
  margin-bottom: 20px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.image-slot {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  background: var(--el-fill-color-light);
  color: var(--el-text-color-secondary);
  font-size: 12px;
}
</style>
