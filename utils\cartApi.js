// utils/cartApi.js
const request = require('./request.js');

/**
 * 购物车相关API
 */
const cartApi = {
  /**
   * 获取购物车列表
   * @returns {Promise} 返回Promise对象
   */
  getCartList: function() {
    return new Promise((resolve, reject) => {
      request.get('/user/cart/list', {}, {
        success: (res) => {
          if (res.data.code === 0 || res.data.code === 1) {
            resolve(res.data.data || []);
          } else {
            reject(new Error(res.data.msg || '获取购物车失败'));
          }
        },
        fail: (err) => {
          reject(err);
        }
      });
    });
  },

  /**
   * 添加商品到购物车
   * @param {Object} data - 购物车数据
   * @param {number} data.productId - 商品ID
   * @param {number} data.quantity - 商品数量
   * @param {number} data.skuId - 商品SKU ID
   * @returns {Promise} 返回Promise对象
   */
  addToCart: function(data) {
    return new Promise((resolve, reject) => {
      if (!data.productId) {
        reject(new Error('商品ID不能为空'));
        return;
      }

      if (!data.quantity || data.quantity <= 0) {
        reject(new Error('商品数量必须大于0'));
        return;
      }

      // 构建符合API格式的请求数据
      const requestData = {
        productId: data.productId,
        quantity: data.quantity,
        skuId: data.skuId || 0
      };

      console.log('购物车API请求数据:', requestData);

      request.post('/user/cart/add', requestData, {
        success: (res) => {
          if (res.data.code === 0 || res.data.code === 1) {
            console.log("添加购物车响应内容",res.data)
            resolve(res.data.data);
          } else {
            reject(new Error(res.data.msg || '添加到购物车失败'));
          }
        },
        fail: (err) => {
          console.log("添加到购物车失败")
          reject(err);
        }
      });
    });
  },

  /**
   * 更新购物车商品数量
   * @param {string} id - 购物车项ID
   * @param {number} quantity - 新的商品数量
   * @returns {Promise} 返回Promise对象
   */
  updateCartItem: function(id, quantity) {
    return new Promise((resolve, reject) => {
      if (!id) {
        reject(new Error('购物车项ID不能为空'));
        return;
      }

      if (quantity <= 0) {
        reject(new Error('商品数量必须大于0'));
        return;
      }

      request.put(`/user/cart/update/${id}/${quantity}`, {}, {
        success: (res) => {
          if (res.data.code === 0 || res.data.code === 1) {
            resolve(res.data.data);
          } else {
            reject(new Error(res.data.msg || '更新购物车失败'));
          }
        },
        fail: (err) => {
          reject(err);
        }
      });
    });
  },

  /**
   * 从购物车中删除商品
   * @param {string} id - 购物车项ID
   * @returns {Promise} 返回Promise对象
   */
  removeFromCart: function(id) {
    return new Promise((resolve, reject) => {
      if (!id) {
        reject(new Error('购物车项ID不能为空'));
        return;
      }

      request.delete(`/user/cart/delete/${id}`, {}, {
        success: (res) => {
          if (res.data.code === 0 || res.data.code === 1) {
            resolve(res.data.data);
          } else {
            reject(new Error(res.data.msg || '从购物车删除失败'));
          }
        },
        fail: (err) => {
          reject(err);
        }
      });
    });
  },

  /**
   * 清空购物车
   * @returns {Promise} 返回Promise对象
   */
  clearCart: function() {
    return new Promise((resolve, reject) => {
      request.delete('/user/cart/clear', {}, {
        success: (res) => {
          if (res.data.code === 0 || res.data.code === 1) {
            resolve(res.data.data);
          } else {
            reject(new Error(res.data.msg || '清空购物车失败'));
          }
        },
        fail: (err) => {
          reject(err);
        }
      });
    });
  },

  /**
   * 获取购物车商品数量
   * @returns {Promise} 返回Promise对象，解析为购物车商品总数
   */
  getCartCount: function() {
    return new Promise((resolve, reject) => {
      request.get('/user/cart/count', {}, {
        success: (res) => {
          if (res.data.code === 0 || res.data.code === 1) {
            resolve(res.data.data || 0);
          } else {
            reject(new Error(res.data.msg || '获取购物车数量失败'));
          }
        },
        fail: (err) => {
          reject(err);
        }
      });
    });
  }
};

module.exports = cartApi;
