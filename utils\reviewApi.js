// utils/reviewApi.js
const request = require('./request.js');

/**
 * 评价相关API
 */
const reviewApi = {
  /**
   * 上传评价图片
   * @param {string} filePath - 图片文件路径
   * @returns {Promise} 返回Promise对象
   */
  uploadReviewImage: function(filePath) {
    return new Promise((resolve, reject) => {
      if (!filePath) {
        reject(new Error('图片路径不能为空'));
        return;
      }

      console.log('ReviewAPI - 上传评价图片:', filePath);

      wx.uploadFile({
        url: getApp().globalData.baseUrl + '/user/upload/image',
        filePath: filePath,
        name: 'file',
        header: {
          'Authorization': wx.getStorageSync('token')
        },
        success: (res) => {
          console.log('ReviewAPI - 图片上传响应:', {
            statusCode: res.statusCode,
            data: res.data
          });

          try {
            const data = JSON.parse(res.data);
            if (data.code === 1 && data.data) {
              // 拼接完整的图片URL
              const fullImageUrl = getApp().globalData.baseUrl + data.data;
              console.log('ReviewAPI - 图片上传成功:', fullImageUrl);
              resolve(fullImageUrl);
            } else {
              console.error('ReviewAPI - 图片上传失败:', data);
              reject(new Error(data.msg || '图片上传失败'));
            }
          } catch (parseError) {
            console.error('ReviewAPI - 响应解析失败:', parseError);
            reject(new Error('图片上传响应解析失败'));
          }
        },
        fail: (err) => {
          console.error('ReviewAPI - 图片上传请求失败:', err);
          reject(err);
        }
      });
    });
  },

  /**
   * 批量上传评价图片
   * @param {Array} imagePaths - 图片路径数组
   * @returns {Promise} 返回Promise对象
   */
  uploadReviewImages: function(imagePaths) {
    if (!Array.isArray(imagePaths) || imagePaths.length === 0) {
      return Promise.resolve([]);
    }

    console.log('ReviewAPI - 批量上传评价图片:', imagePaths.length, '张');

    const uploadPromises = imagePaths.map((imagePath, index) => {
      return this.uploadReviewImage(imagePath)
        .then(imageUrl => {
          console.log(`ReviewAPI - 第${index + 1}张图片上传成功:`, imageUrl);
          return imageUrl;
        })
        .catch(err => {
          console.error(`ReviewAPI - 第${index + 1}张图片上传失败:`, err);
          throw new Error(`第${index + 1}张图片上传失败: ${err.message}`);
        });
    });

    return Promise.all(uploadPromises);
  },

  /**
   * 提交商品评价
   * @param {Object} reviewData - 评价数据
   * @param {string} reviewData.content - 评价内容
   * @param {number} reviewData.orderId - 订单ID
   * @param {number} reviewData.productId - 商品ID
   * @param {number} reviewData.rating - 评分（1-5）
   * @param {Array} reviewData.reviewImages - 评价图片URL数组
   * @returns {Promise} 返回Promise对象
   */
  submitReview: function(reviewData) {
    return new Promise((resolve, reject) => {
      // 参数验证
      if (!reviewData) {
        reject(new Error('评价数据不能为空'));
        return;
      }

      if (!reviewData.content || !reviewData.content.trim()) {
        reject(new Error('评价内容不能为空'));
        return;
      }

      if (!reviewData.orderId) {
        reject(new Error('订单ID不能为空'));
        return;
      }

      if (!reviewData.productId) {
        reject(new Error('商品ID不能为空'));
        return;
      }

      if (!reviewData.rating || reviewData.rating < 1 || reviewData.rating > 5) {
        reject(new Error('评分必须在1-5之间'));
        return;
      }

      // 格式化请求数据
      const requestData = {
        content: reviewData.content.trim(),
        orderId: parseInt(reviewData.orderId),
        productId: parseInt(reviewData.productId),
        rating: parseInt(reviewData.rating),
        reviewImages: Array.isArray(reviewData.reviewImages) ? reviewData.reviewImages : []
      };

      console.log('ReviewAPI - 提交评价数据:', JSON.stringify(requestData));

      request.post('/user/reviews/submit', requestData, {
        success: (res) => {
          console.log('ReviewAPI - 评价提交响应:', {
            statusCode: res.statusCode,
            code: res.data?.code,
            hasData: !!res.data?.data,
            msg: res.data?.msg
          });

          if (res.data.code === 0 || res.data.code === 1) {
            console.log('ReviewAPI - 评价提交成功');
            resolve(res.data.data);
          } else {
            reject(new Error(res.data.msg || '评价提交失败'));
          }
        },
        fail: (err) => {
          console.error('ReviewAPI - 评价提交失败:', err);
          reject(err);
        }
      });
    });
  },

  /**
   * 检查商品评价状态
   * @param {number} orderId - 订单ID
   * @param {number} productId - 商品ID
   * @returns {Promise} 返回Promise对象，resolve(boolean) - true表示已评价，false表示未评价
   */
  checkReviewStatus: function(orderId, productId) {
    return new Promise((resolve, reject) => {
      // 参数验证
      if (!orderId) {
        reject(new Error('订单ID不能为空'));
        return;
      }

      if (!productId) {
        reject(new Error('商品ID不能为空'));
        return;
      }

      const params = {
        orderId: parseInt(orderId),
        productId: parseInt(productId)
      };

      console.log('ReviewAPI - 检查评价状态，参数:', params);

      request.get('/user/reviews/check-reviewed', params, {
        success: (res) => {
          console.log('ReviewAPI - 评价状态检查响应:', {
            statusCode: res.statusCode,
            code: res.data?.code,
            data: res.data?.data,
            msg: res.data?.msg
          });

          if (res.data.code === 0 || res.data.code === 1) {
            const isReviewed = !!res.data.data;
            console.log('ReviewAPI - 评价状态检查成功，已评价:', isReviewed);
            resolve(isReviewed);
          } else {
            console.error('ReviewAPI - 评价状态检查失败:', res.data);
            reject(new Error(res.data.msg || '检查评价状态失败'));
          }
        },
        fail: (err) => {
          console.error('ReviewAPI - 评价状态检查请求失败:', err);
          reject(err);
        }
      });
    });
  },

  /**
   * 批量检查订单商品的评价状态
   * @param {number} orderId - 订单ID
   * @param {Array} productIds - 商品ID数组
   * @returns {Promise} 返回Promise对象，resolve(Object) - 商品ID为key，评价状态为value的对象
   */
  batchCheckReviewStatus: function(orderId, productIds) {
    if (!Array.isArray(productIds) || productIds.length === 0) {
      return Promise.resolve({});
    }

    console.log('ReviewAPI - 批量检查评价状态，订单ID:', orderId, '商品数量:', productIds.length);

    const checkPromises = productIds.map(productId => {
      return this.checkReviewStatus(orderId, productId)
        .then(isReviewed => {
          console.log(`ReviewAPI - 商品${productId}评价状态:`, isReviewed);
          return { productId, isReviewed };
        })
        .catch(err => {
          console.error(`ReviewAPI - 商品${productId}评价状态检查失败:`, err);
          // 检查失败时默认为未评价
          return { productId, isReviewed: false };
        });
    });

    return Promise.all(checkPromises)
      .then(results => {
        const statusMap = {};
        results.forEach(result => {
          statusMap[result.productId] = result.isReviewed;
        });
        console.log('ReviewAPI - 批量评价状态检查完成:', statusMap);
        return statusMap;
      });
  },

  /**
   * 完整的评价提交流程（包含图片上传）
   * @param {Object} reviewData - 评价数据
   * @param {Array} imagePaths - 本地图片路径数组
   * @returns {Promise} 返回Promise对象
   */
  submitReviewWithImages: function(reviewData, imagePaths = []) {
    console.log('ReviewAPI - 开始完整评价提交流程');

    // 如果有图片，先上传图片
    if (imagePaths.length > 0) {
      return this.uploadReviewImages(imagePaths)
        .then(imageUrls => {
          console.log('ReviewAPI - 所有图片上传完成，URL列表:', imageUrls);

          // 将图片URL添加到评价数据中
          const finalReviewData = {
            ...reviewData,
            reviewImages: imageUrls
          };

          return this.submitReview(finalReviewData);
        });
    } else {
      // 没有图片，直接提交评价
      return this.submitReview({
        ...reviewData,
        reviewImages: []
      });
    }
  }
};

module.exports = reviewApi;
