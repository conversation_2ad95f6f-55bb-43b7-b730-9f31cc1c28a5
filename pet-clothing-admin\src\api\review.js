import request from '@/utils/request'

// 通过订单ID查询评论
export function getOrderReviews(orderId, page = 1, pageSize = 10) {
  return request({
    url: `/admin/reviews/order/${orderId}`,
    method: 'get',
    params: {
      page,
      pageSize
    }
  })
}

// 删除评论
export function deleteReview(reviewId) {
  return request({
    url: `/admin/reviews/${reviewId}`,
    method: 'delete'
  })
}

// 获取评论详情（备用）
export function getReviewDetail(reviewId) {
  return request({
    url: `/admin/reviews/${reviewId}`,
    method: 'get'
  })
}

// 获取评论列表（分页查询，备用）
export function getReviewList(params) {
  return request({
    url: '/admin/reviews/page',
    method: 'get',
    params
  })
}
