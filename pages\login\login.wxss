.login-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 60rpx 40rpx;
  height: 100vh;
  box-sizing: border-box;
  background-color: #fff;
}

.logo-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 60rpx;
}

.logo {
  width: 300rpx;
  height: 300rpx;
  border-radius: 10rpx;
  margin-top: 100rpx;
  margin-bottom: 20rpx;
}

.app-name {
  font-size: 40rpx;
  font-weight: bold;
  color: #333;
}

.login-options {
  width: 100%;
  margin-bottom: 60rpx;
  margin-top: 200rpx;
}

.wechat-login-btn {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 90rpx;
  background-color: #07c160;
  color: #fff;
  font-size: 32rpx;
  border-radius: 45rpx;
  margin-bottom: 40rpx;
  border: none;
}

.wechat-login-btn::after {
  border: none;
}

.wechat-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 20rpx;
  margin-bottom: 5rpx;
}

.other-login-options {
  margin-top: 60rpx;
}

.divider {
  display: flex;
  align-items: center;
  margin-bottom: 40rpx;
}

.line {
  flex: 1;
  height: 1rpx;
  background-color: #e5e5e5;
}

.divider-text {
  padding: 0 20rpx;
  font-size: 24rpx;
  color: #999;
}

.phone-login {
  text-align: center;
  font-size: 28rpx;
  color: #666;
  padding: 20rpx 0;
}

.agreement {
  display: flex;
  align-items: center;
  font-size: 24rpx;
  color: #999;
  position: absolute;
  bottom: 60rpx;
}

.agreement checkbox {
  transform: scale(0.7);
  margin-right: 5rpx;
}

.agreement-text {
  color: #999;
}

.agreement-link {
  color: #07c160;
}
