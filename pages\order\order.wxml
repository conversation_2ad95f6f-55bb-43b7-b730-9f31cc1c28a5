<!--pages/order/order.wxml-->
<view class="container">
  <!-- 状态筛选栏 -->
  <view class="status-filter">
    <view class="filter-item {{currentStatus === 'all' ? 'active' : ''}}"
          bindtap="onStatusChange" data-status="all">
      <text>全部</text>
    </view>
    <view class="filter-item {{currentStatus === '待支付' ? 'active' : ''}}"
          bindtap="onStatusChange" data-status="待支付">
      <text>待支付</text>
    </view>
    <view class="filter-item {{currentStatus === '待发货' ? 'active' : ''}}"
          bindtap="onStatusChange" data-status="待发货">
      <text>待发货</text>
    </view>
    <view class="filter-item {{currentStatus === '待收货' ? 'active' : ''}}"
          bindtap="onStatusChange" data-status="待收货">
      <text>待收货</text>
    </view>
    <view class="filter-item {{currentStatus === '已完成' ? 'active' : ''}}"
          bindtap="onStatusChange" data-status="已完成">
      <text>已完成</text>
    </view>
  </view>

  <!-- 订单列表 -->
  <view class="order-list">
    <!-- 加载状态 -->
    <view wx:if="{{loading}}" class="loading-container">
      <text class="loading-text">加载中...</text>
    </view>

    <!-- 空状态 -->
    <view wx:elif="{{orderList.length === 0}}" class="empty-container">
      <image src="/images/empty-cart.png" class="empty-image" mode="aspectFit"></image>
      <text class="empty-text">暂无订单</text>
      <navigator url="/pages/index/index" class="go-shopping-btn">
        <text>去购物</text>
      </navigator>
    </view>

    <!-- 订单项 -->
    <view wx:else>
      <view class="order-item" wx:for="{{orderList}}" wx:key="id" bindtap="onViewDetail" data-id="{{item.id}}">
        <!-- 订单头部 -->
        <view class="order-header">
          <view class="order-info">
            <text class="order-number">订单号：{{item.orderNumber}}</text>
            <text class="order-time">{{item.createdAt}}</text>
          </view>
          <view class="order-status {{item.status === '待支付' ? 'pending' : item.status === '已取消' ? 'cancelled' : 'normal'}}">
            <text>{{item.status}}</text>
          </view>
        </view>

        <!-- 订单商品列表 -->
        <view class="order-products">
          <view class="product-item" wx:for="{{item.orderItems}}" wx:for-item="product" wx:key="skuId">
            <image src="{{product.imageUrl || '/images/product-default.jpg'}}"
                   class="product-image" mode="aspectFill"></image>
            <view class="product-details">
              <text class="product-name">{{product.productName}}</text>
              <view class="product-specs">
                <text class="spec-text">{{product.color}} {{product.size}}</text>
              </view>
              <view class="product-price-qty">
                <text class="product-price">¥{{product.price}}</text>
                <text class="product-quantity">x{{product.quantity}}</text>
              </view>
            </view>
          </view>
        </view>

        <!-- 订单金额 -->
        <view class="order-amount">
          <text class="amount-label">实付款：</text>
          <text class="amount-value">¥{{item.totalAmount}}</text>
        </view>

        <!-- 支付方式 -->
        <view class="payment-info" wx:if="{{item.paymentMethod}}">
          <text class="payment-label">支付方式：</text>
          <text class="payment-method">{{item.paymentMethod}}</text>
        </view>

        <!-- 操作按钮 -->
        <view class="order-actions" catchtap="stopPropagation">
          <!-- 待支付状态 -->
          <block wx:if="{{item.status === '待支付'}}">
            <button class="action-btn cancel-btn" bindtap="onCancelOrder" data-id="{{item.id}}">
              取消订单
            </button>
            <button class="action-btn primary-btn" bindtap="onPayOrder" data-id="{{item.id}}">
              立即支付
            </button>
          </block>

          <!-- 待发货状态 -->
          <block wx:elif="{{item.status === '待发货'}}">
            <button class="action-btn secondary-btn" bindtap="onRequestRefund" data-id="{{item.id}}">
              申请退款
            </button>
          </block>

          <!-- 已发货状态 -->
          <block wx:elif="{{item.status === '已发货'}}">
            <button class="action-btn primary-btn" bindtap="onConfirmReceive" data-id="{{item.id}}">
              确认收货
            </button>
          </block>

          <!-- 待收货状态 -->
          <block wx:elif="{{item.status === '待收货'}}">
            <button class="action-btn primary-btn" bindtap="onConfirmReceive" data-id="{{item.id}}">
              确认收货
            </button>
          </block>

          <!-- 已完成状态 - 不显示操作按钮 -->
          <block wx:elif="{{item.status === '已完成'}}">
            <!-- 已完成订单不显示操作按钮 -->
          </block>

          <!-- 查看详情按钮（所有状态都有） -->
          <button class="action-btn detail-btn" bindtap="onViewDetail" data-id="{{item.id}}">
            查看详情
          </button>
        </view>
      </view>
    </view>
  </view>
</view>
