import request from '@/utils/request'

// 获取所有商品分类
export function getCategoryList() {
  return request({
    url: '/admin/categories/all',
    method: 'get'
  })
}

// 获取分类详情
export function getCategoryDetail(id) {
  return request({
    url: `/admin/categories/${id}`,
    method: 'get'
  })
}

// 添加商品分类
export function addCategory(data) {
  return request({
    url: '/admin/categories',
    method: 'post',
    headers: {
      'Content-Type': 'application/json'
    },
    data: {
      name: data.name,
      status: data.status || '启用'
    }
  })
}

// 更新商品分类
export function updateCategory(data) {
  return request({
    url: '/admin/categories',
    method: 'put',
    headers: {
      'Content-Type': 'application/json'
    },
    data: {
      id: data.id,
      name: data.name,
      status: data.status
    }
  })
}

// 删除商品分类
export function deleteCategory(id) {
  return request({
    url: `/admin/categories/${id}`,
    method: 'delete'
  })
}

// 获取子分类列表
export function getSubcategories(categoryId) {
  return request({
    url: `/admin/subcategories/byCategoryId/${categoryId}`,
    method: 'get'
  })
}

// 添加子分类
export function addSubcategory(data) {
  return request({
    url: '/admin/subcategories',
    method: 'post',
    data
  })
}

// 更新子分类
export function updateSubcategory(id, data) {
  return request({
    url: `/admin/subcategories/${id}`,
    method: 'put',
    data
  })
}

// 删除子分类
export function deleteSubcategory(id) {
  return request({
    url: `/admin/subcategories/${id}`,
    method: 'delete'
  })
}

// 更新分类状态
export function updateCategoryStatus(id, status) {
  return request({
    url: `/admin/categories/status/${status}`,
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    data: { id }
  })
}

// 获取大类下所有小类
export function getSubcategoriesByCategoryId(categoryId) {
  return request({
    url: `/admin/subcategories/all/${categoryId}`,
    method: 'get'
  })
}

// 启用/禁用商品小类
export function updateSubcategoryStatus(id, status) {
  return request({
    url: `/admin/subcategories/status/${status}`,
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    data: { id }
  })
}
