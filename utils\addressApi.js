// utils/addressApi.js
const request = require('./request.js');

/**
 * 地址相关API
 */
const addressApi = {
  /**
   * 获取用户地址列表
   * @returns {Promise} 返回Promise对象
   */
  getAddressList: function() {
    return new Promise((resolve, reject) => {
      const app = getApp();
      const userId = app.globalData.userId || wx.getStorageSync('userId');
      
      if (!userId) {
        reject(new Error('用户未登录'));
        return;
      }

      request.get('/user/address/list', {}, {
        success: (res) => {
          if (res.data.code === 0 || res.data.code === 1) {
            resolve(res.data.data || []);
          } else {
            reject(new Error(res.data.msg || '获取地址列表失败'));
          }
        },
        fail: (err) => {
          reject(err);
        }
      });
    });
  },

  /**
   * 添加新地址
   * @param {Object} addressData - 地址数据
   * @param {string} addressData.consignee - 收货人
   * @param {string} addressData.phone - 联系电话
   * @param {string} addressData.address - 详细地址
   * @param {boolean} addressData.isDefault - 是否默认地址
   * @returns {Promise} 返回Promise对象
   */
  addAddress: function(addressData) {
    return new Promise((resolve, reject) => {
      const app = getApp();
      const userId = app.globalData.userId || wx.getStorageSync('userId');
      
      if (!userId) {
        reject(new Error('用户未登录'));
        return;
      }

      // 构建请求数据
      const data = {
        ...addressData,
        userId: userId
      };

      request.post('/user/address/add', data, {
        success: (res) => {
          if (res.data.code === 0 || res.data.code === 1) {
            resolve(res.data.data);
          } else {
            reject(new Error(res.data.msg || '添加地址失败'));
          }
        },
        fail: (err) => {
          reject(err);
        }
      });
    });
  },

  /**
   * 更新地址
   * @param {Object} addressData - 地址数据
   * @param {number} addressData.id - 地址ID
   * @param {string} addressData.consignee - 收货人
   * @param {string} addressData.phone - 联系电话
   * @param {string} addressData.address - 详细地址
   * @param {boolean} addressData.isDefault - 是否默认地址
   * @returns {Promise} 返回Promise对象
   */
  updateAddress: function(addressData) {
    return new Promise((resolve, reject) => {
      const app = getApp();
      const userId = app.globalData.userId || wx.getStorageSync('userId');
      
      if (!userId) {
        reject(new Error('用户未登录'));
        return;
      }

      // 构建请求数据
      const data = {
        ...addressData,
        userId: userId
      };

      request.put('/user/address/update', data, {
        success: (res) => {
          if (res.data.code === 0 || res.data.code === 1) {
            resolve(res.data.data);
          } else {
            reject(new Error(res.data.msg || '更新地址失败'));
          }
        },
        fail: (err) => {
          reject(err);
        }
      });
    });
  },

  /**
   * 删除地址
   * @param {number} addressId - 地址ID
   * @returns {Promise} 返回Promise对象
   */
  deleteAddress: function(addressId) {
    return new Promise((resolve, reject) => {
      if (!addressId) {
        reject(new Error('地址ID不能为空'));
        return;
      }

      request.delete(`/user/address/delete/${addressId}`, {}, {
        success: (res) => {
          if (res.data.code === 0 || res.data.code === 1) {
            resolve(res.data.data);
          } else {
            reject(new Error(res.data.msg || '删除地址失败'));
          }
        },
        fail: (err) => {
          reject(err);
        }
      });
    });
  }

};

module.exports = addressApi;
