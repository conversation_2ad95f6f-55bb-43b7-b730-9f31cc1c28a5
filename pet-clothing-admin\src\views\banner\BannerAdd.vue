<template>
  <div class="banner-add-container">
    <el-card class="form-card" shadow="never" v-loading="loading">
      <template #header>
        <div class="card-header">
          <span>{{ isEdit ? '编辑轮播图' : '添加轮播图' }}</span>
        </div>
      </template>

      <el-form
        ref="bannerFormRef"
        :model="bannerForm"
        :rules="bannerRules"
        label-width="120px"
        label-position="right"
      >
        <el-form-item v-if="isEdit" label="轮播图ID">
          <el-input v-model="bannerId" disabled />
        </el-form-item>

        <el-form-item label="轮播图标题" prop="title">
          <el-input v-model="bannerForm.title" placeholder="请输入轮播图标题" />
        </el-form-item>

        <el-form-item label="轮播图图片" prop="imageUrl">
          <el-upload
            class="banner-image-uploader"
            action="/api/admin/upload/image"
            :headers="uploadHeaders"
            :show-file-list="false"
            :on-success="handleImageSuccess"
            :on-error="handleUploadError"
            :before-upload="beforeImageUpload"
            name="file"
          >
            <img v-if="bannerForm.imageUrl" :src="bannerForm.imageUrl" class="banner-image" />
            <el-icon v-else class="banner-image-uploader-icon"><Plus /></el-icon>
          </el-upload>
          <div class="form-item-tip">建议尺寸：750x300像素，支持JPG、PNG格式，大小不超过5MB</div>
        </el-form-item>

        <el-form-item label="状态">
          <el-switch
            v-model="bannerForm.status"
            :active-value="1"
            :inactive-value="0"
            active-text="启用"
            inactive-text="禁用"
          />
        </el-form-item>

        <el-form-item>
          <el-button type="primary" :loading="submitting" @click="submitForm">保存</el-button>
          <el-button @click="goBack">取消</el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import { getBannerDetail, addBanner, updateBanner } from '@/api/banner'

const router = useRouter()
const route = useRoute()
const bannerFormRef = ref(null)
const loading = ref(false)
const submitting = ref(false)

// 判断是编辑还是添加
const isEdit = computed(() => !!route.params.id)
const bannerId = computed(() => route.params.id)

// 上传图片的请求头
const uploadHeaders = computed(() => {
  return {
    token: localStorage.getItem('token')
  }
})

// 轮播图表单
const bannerForm = reactive({
  title: '',
  imageUrl: '',
  status: 0
})

// 表单验证规则
const bannerRules = {
  title: [
    { required: true, message: '请输入轮播图标题', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  imageUrl: [
    { required: true, message: '请上传轮播图图片', trigger: 'change' }
  ]
}

// 获取轮播图详情
const fetchBannerDetail = async () => {
  if (!isEdit.value) return

  loading.value = true
  try {
    // 直接使用模拟数据
    setTimeout(() => {
      Object.assign(bannerForm, {
        title: '春季新品上市',
        imageUrl: 'https://picsum.photos/400/200?random=1',
        status: 1
      })
      loading.value = false
    }, 500)
  } catch (error) {
    console.error('获取轮播图详情失败:', error)
    ElMessage.error('获取轮播图详情失败')
    loading.value = false
  }
}

// 上传图片前的验证
const beforeImageUpload = (file) => {
  const isImage = file.type.match('image.*')
  const isLt5M = file.size / 1024 / 1024 < 5

  if (!isImage) {
    ElMessage.error('请选择图片文件!')
    return false
  }
  if (!isLt5M) {
    ElMessage.error('图片大小不能超过 5MB!')
    return false
  }
  return true
}

// 图片上传成功
const handleImageSuccess = (response) => {
  if (response.code === 1) {
    bannerForm.imageUrl = `http://124.223.109.237:11223/${response.data}`
    ElMessage.success('图片上传成功')
  } else {
    ElMessage.error(response.msg || '上传失败')
  }
}

// 图片上传失败
const handleUploadError = (error) => {
  if (error.status === 401) {
    ElMessage.error('JWT令牌验证失败，请检查令牌是否正确')
  } else {
    ElMessage.error(`上传失败: ${error.message || '网络错误'}`)
  }
}

// 提交表单
const submitForm = async () => {
  await bannerFormRef.value.validate(async (valid) => {
    if (valid) {
      submitting.value = true
      try {
        const bannerData = {
          imageUrl: bannerForm.imageUrl,
          status: bannerForm.status,
          title: bannerForm.title
        }
        
        const response = await addBanner(bannerData)
        if (response.code === 1) {
          ElMessage.success('添加轮播图成功')
          router.push('/banner')
        } else {
          ElMessage.error(response.msg || '添加轮播图失败')
        }
      } catch (error) {
        console.error('添加轮播图失败:', error)
        ElMessage.error('添加轮播图失败')
      } finally {
        submitting.value = false
      }
    }
  })
}

// 返回上一页
const goBack = () => {
  router.back()
}

onMounted(() => {
  if (isEdit.value) {
    fetchBannerDetail()
  }
})
</script>

<style scoped>
.banner-add-container {
  padding: 20px;
}

.form-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.banner-image-uploader {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  width: 300px;
  height: 120px;
}

.banner-image-uploader:hover {
  border-color: #409EFF;
}

.banner-image-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 300px;
  height: 120px;
  line-height: 120px;
  text-align: center;
}

.banner-image {
  width: 300px;
  height: 120px;
  display: block;
  object-fit: cover;
}

.form-item-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
}
</style>
