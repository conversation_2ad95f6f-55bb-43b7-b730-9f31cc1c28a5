<template>
  <div class="category-add-container">
    <el-card class="form-card" shadow="never" v-loading="loading">
      <template #header>
        <div class="card-header">
          <span>{{ isEdit ? '编辑分类' : '添加分类' }}</span>
        </div>
      </template>
      
      <el-form
        ref="categoryFormRef"
        :model="categoryForm"
        :rules="categoryRules"
        label-width="100px"
        label-position="right"
      >
        <el-form-item v-if="isEdit" label="分类ID">
          <el-input v-model="categoryForm.id" disabled />
        </el-form-item>

        <el-form-item label="分类名称" prop="name">
          <el-input v-model="categoryForm.name" placeholder="请输入分类名称" />
        </el-form-item>
        
        <el-form-item label="状态">
          <el-radio-group v-model="categoryForm.status">
            <el-radio label="启用">启用</el-radio>
            <el-radio label="禁用">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" :loading="submitting" @click="submitForm">保存</el-button>
          <el-button @click="goBack">取消</el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { addCategory, updateCategory, getCategoryDetail } from '@/api/category'

const router = useRouter()
const route = useRoute()
const categoryFormRef = ref(null)
const loading = ref(false)
const submitting = ref(false)

// 判断是编辑还是添加
const isEdit = computed(() => !!route.query.id)

// 分类表单
const categoryForm = reactive({
  id: route.query.id || '',
  name: '',
  status: '启用'
})

// 表单验证规则
const categoryRules = {
  name: [
    { required: true, message: '请输入分类名称', trigger: 'blur' },
    { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' }
  ]
}

// 获取分类详情
const fetchCategoryDetail = async () => {
  if (!isEdit.value) return
  
  loading.value = true
  try {
    const response = await getCategoryDetail(categoryForm.id)
    if (response.code === 1) {
      const { name, status } = response.data
      categoryForm.name = name
      categoryForm.status = status
    } else {
      ElMessage.error(response.msg || '获取分类详情失败')
    }
  } catch (error) {
    console.error('获取分类详情失败:', error)
    ElMessage.error('获取分类详情失败')
  } finally {
    loading.value = false
  }
}

// 提交表单
const submitForm = async () => {
  await categoryFormRef.value.validate(async (valid) => {
    if (valid) {
      submitting.value = true
      try {
        const submitFn = isEdit.value ? updateCategory : addCategory
        const response = await submitFn(categoryForm)
        
        if (response.code === 1) {
          ElMessage.success(isEdit.value ? '更新分类成功' : '添加分类成功')
          router.replace({
            path: '/category',
            query: { t: Date.now() }
          })
        } else {
          ElMessage.error(response.msg || (isEdit.value ? '更新分类失败' : '添加分类失败'))
        }
      } catch (error) {
        console.error(isEdit.value ? '更新分类失败:' : '添加分类失败:', error)
        ElMessage.error(isEdit.value ? '更新分类失败' : '添加分类失败')
      } finally {
        submitting.value = false
      }
    }
  })
}

// 返回上一页
const goBack = () => {
  router.back()
}

onMounted(() => {
  if (isEdit.value) {
    fetchCategoryDetail()
  }
})
</script>

<style scoped>
.category-add-container {
  padding: 20px;
}

.form-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
