<!--pages/address/edit.wxml-->
<view class="container">
  <form bindsubmit="saveAddress">
    <view class="form-group">
      <!-- 收货人 -->
      <view class="form-item">
        <view class="form-label">收货人</view>
        <view class="form-input">
          <input 
            type="text" 
            placeholder="请输入收货人姓名" 
            value="{{addressInfo.consignee}}" 
            bindinput="onInputChange" 
            data-field="consignee"
            maxlength="20"
          />
        </view>
      </view>
      <view class="error-tip" wx:if="{{errors.consignee}}">{{errors.consignee}}</view>
      
      <!-- 手机号码 -->
      <view class="form-item">
        <view class="form-label">手机号码</view>
        <view class="form-input">
          <input 
            type="number" 
            placeholder="请输入手机号码" 
            value="{{addressInfo.phone}}" 
            bindinput="onInputChange" 
            data-field="phone"
            maxlength="11"
          />
        </view>
      </view>
      <view class="error-tip" wx:if="{{errors.phone}}">{{errors.phone}}</view>
      
      <!-- 所在地区 -->
      <view class="form-item">
        <view class="form-label">所在地区</view>
        <view class="form-input">
          <picker 
            mode="region" 
            value="{{region}}" 
            bindchange="onRegionChange"
          >
            <view class="picker {{region[0] ? '' : 'placeholder'}}">
              {{region[0] ? region[0] + ' ' + region[1] + ' ' + region[2] : '请选择所在地区'}}
            </view>
          </picker>
          <view class="arrow">></view>
        </view>
      </view>
      
      <!-- 详细地址 -->
      <view class="form-item">
        <view class="form-label">详细地址</view>
        <view class="form-input">
          <textarea 
            placeholder="请输入详细地址，如街道、小区、楼栋号、单元室等" 
            value="{{detailAddress}}" 
            bindinput="onDetailAddressChange"
            maxlength="100"
            auto-height
          />
        </view>
      </view>
      <view class="error-tip" wx:if="{{errors.address}}">{{errors.address}}</view>
      
      <!-- 设为默认地址 -->
      <view class="form-item switch-item">
        <view class="form-label">设为默认地址</view>
        <view class="form-input">
          <switch 
            checked="{{addressInfo.isDefault}}" 
            bindchange="onDefaultChange" 
            color="#ff4c6a"
          />
        </view>
      </view>
    </view>
    
    <!-- 保存按钮 -->
    <view class="form-actions">
      <button 
        class="save-btn {{submitting ? 'disabled' : ''}}" 
        form-type="submit" 
        disabled="{{submitting}}"
      >
        保存
      </button>
    </view>
  </form>
</view>
