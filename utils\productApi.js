// utils/productApi.js
const request = require('./request.js');

/**
 * 商品相关API
 */
const productApi = {
  /**
   * 获取商品详情
   * @param {string} id - 商品ID
   * @returns {Promise} 返回Promise对象
   */
  getProductDetail: function(id) {
    return new Promise((resolve, reject) => {
      if (!id) {
        reject(new Error('商品ID不能为空'));
        return;
      }

      request.get(`/user/products/detail/${id}`, {}, {
        success: (res) => {
          if (res.data.code === 0 || res.data.code === 1) {
            resolve(res.data.data);
          } else {
            reject(new Error(res.data.msg || '获取商品详情失败'));
          }
        },
        fail: (err) => {
          reject(err);
        }
      });
    });
  },

  /**
   * 获取商品列表
   * @param {Object} params - 查询参数
   * @param {number} params.categoryId - 分类ID (可选)
   * @param {string} params.keyword - 搜索关键词 (可选)
   * @param {number} params.page - 页码 (可选，默认1)
   * @param {number} params.pageSize - 每页数量 (可选，默认10)
   * @returns {Promise} 返回Promise对象
   */
  getProductList: function(params = {}) {
    return new Promise((resolve, reject) => {
      request.get('/user/products/search', params, {
        success: (res) => {
          if (res.data.code === 0 || res.data.code === 1) {
            resolve(res.data.data || {});
          } else {
            reject(new Error(res.data.msg || '获取商品列表失败'));
          }
        },
        fail: (err) => {
          reject(err);
        }
      });
    });
  },

  /**
   * 获取热门商品
   * @param {number} limit - 获取数量
   * @returns {Promise} 返回Promise对象
   */
  getHotProducts: function(limit = 10) {
    return new Promise((resolve, reject) => {
      request.get('/user/products/hot', { limit }, {
        success: (res) => {
          if (res.data.code === 0 || res.data.code === 1) {
            resolve(res.data.data || []);
          } else {
            reject(new Error(res.data.msg || '获取热门商品失败'));
          }
        },
        fail: (err) => {
          reject(err);
        }
      });
    });
  },

  /**
   * 获取推荐商品
   * @param {number} limit - 获取数量
   * @returns {Promise} 返回Promise对象
   */
  getRecommendProducts: function(limit = 10) {
    return new Promise((resolve, reject) => {
      request.get('/user/products/recommended', { limit }, {
        success: (res) => {
          if (res.data.code === 0 || res.data.code === 1) {
            resolve(res.data.data || []);
          } else {
            reject(new Error(res.data.msg || '获取推荐商品失败'));
          }
        },
        fail: (err) => {
          reject(err);
        }
      });
    });
  },

  /**
   * 获取商品评论
   * @param {string} productId - 商品ID
   * @param {number} page - 页码 (可选，默认1)
   * @param {number} pageSize - 每页数量 (可选，默认10)
   * @returns {Promise} 返回Promise对象
   */
  getProductComments: function(productId, page = 1, pageSize = 10) {
    return new Promise((resolve, reject) => {
      if (!productId) {
        reject(new Error('商品ID不能为空'));
        return;
      }

      request.get('/user/products/comments', {
        productId,
        page,
        pageSize
      }, {
        success: (res) => {
          if (res.data.code === 0 || res.data.code === 1) {
            resolve(res.data.data || {});
          } else {
            reject(new Error(res.data.msg || '获取商品评论失败'));
          }
        },
        fail: (err) => {
          reject(err);
        }
      });
    });
  },

  /**
   * 收藏商品
   * @param {string} productId - 商品ID
   * @returns {Promise} 返回Promise对象
   */
  favoriteProduct: function(productId) {
    return new Promise((resolve, reject) => {
      if (!productId) {
        reject(new Error('商品ID不能为空'));
        return;
      }

      request.post('/user/favorites/add', { productId }, {
        success: (res) => {
          if (res.data.code === 0 || res.data.code === 1) {
            resolve(res.data.data);
          } else {
            reject(new Error(res.data.msg || '收藏商品失败'));
          }
        },
        fail: (err) => {
          reject(err);
        }
      });
    });
  },

  /**
   * 取消收藏商品
   * @param {string} productId - 商品ID
   * @returns {Promise} 返回Promise对象
   */
  unfavoriteProduct: function(productId) {
    return new Promise((resolve, reject) => {
      if (!productId) {
        reject(new Error('商品ID不能为空'));
        return;
      }

      request.post('/user/favorites/remove', { productId }, {
        success: (res) => {
          if (res.data.code === 0 || res.data.code === 1) {
            resolve(res.data.data);
          } else {
            reject(new Error(res.data.msg || '取消收藏商品失败'));
          }
        },
        fail: (err) => {
          reject(err);
        }
      });
    });
  },

  /**
   * 检查商品是否已收藏
   * @param {string} productId - 商品ID
   * @returns {Promise} 返回Promise对象
   */
  checkFavorite: function(productId) {
    return new Promise((resolve, reject) => {
      if (!productId) {
        reject(new Error('商品ID不能为空'));
        return;
      }

      request.get('/user/favorites/check', { productId }, {
        success: (res) => {
          if (res.data.code === 0 || res.data.code === 1) {
            resolve(res.data.data || false);
          } else {
            reject(new Error(res.data.msg || '检查收藏状态失败'));
          }
        },
        fail: (err) => {
          reject(err);
        }
      });
    });
  }
};

module.exports = productApi;
