// pages/order/order.js
const orderApi = require('../../utils/orderApi.js');
const auth = require('../../utils/auth.js');

Page({
  data: {
    currentStatus: 'all', // 当前选中的状态
    orderList: [], // 订单列表
    loading: false, // 加载状态
    // 退款相关
    showRefundPopup: false,
    refundReason: '',
    refundAmount: 0,
    currentOrderId: null,
    // 退款原因选项
    refundReasons: [
      '商品质量问题',
      '商品与描述不符',
      '商品损坏',
      '尺码不合适',
      '颜色与图片不符',
      '其他原因'
    ]
  },

  onLoad: function (options) {
    // 检查登录状态
    if (!auth.checkLogin()) {
      auth.navigateToLogin('/pages/order/order');
      return;
    }

    // 从参数中获取状态筛选
    if (options.status) {
      this.setData({
        currentStatus: options.status
      });
    }

    // 加载订单列表
    this.loadOrderList();
  },

  onShow: function () {
    // 检查登录状态
    if (!auth.checkLogin()) {
      auth.navigateToLogin('/pages/order/order');
      return;
    }
  },

  onPullDownRefresh: function () {
    // 下拉刷新
    this.loadOrderList();
  },

  // 状态切换
  onStatusChange: function (e) {
    const status = e.currentTarget.dataset.status;
    console.log('状态切换到:', status);
    this.setData({
      currentStatus: status,
      orderList: [] // 清空现有列表
    });
    this.loadOrderList();
  },

  // 加载订单列表
  loadOrderList: function () {
    if (this.data.loading) return;

    this.setData({ loading: true });

    // 构建请求参数 - 不传分页参数，获取所有订单
    const params = {};

    // 添加状态筛选
    if (this.data.currentStatus !== 'all') {
      params.status = this.data.currentStatus;
    }

    console.log('加载所有订单 - 请求参数:', params);

    orderApi.getOrderList(params)
      .then(orders => {
        // 确保orders是数组
        const orderArray = Array.isArray(orders) ? orders : [];

        // 格式化订单数据
        const formattedOrders = this.formatOrderList(orderArray);

        console.log('获取到订单数据:', formattedOrders.length, '条');

        // 直接设置所有订单数据
        this.setData({
          orderList: formattedOrders,
          loading: false
        });

        console.log('加载完成 - 总订单数:', formattedOrders.length);

        // 停止下拉刷新
        wx.stopPullDownRefresh();
      })
      .catch(err => {
        console.error('获取订单列表失败:', err);
        this.setData({ loading: false });

        wx.showToast({
          title: err.message || '获取订单失败',
          icon: 'none'
        });

        // 停止下拉刷新
        wx.stopPullDownRefresh();
      });
  },

  // 格式化订单列表数据
  formatOrderList: function (orders) {
    if (!Array.isArray(orders)) {
      console.warn('formatOrderList - 输入不是数组:', typeof orders);
      return [];
    }

    const formatted = orders.map(order => ({
      ...order,
      // 格式化创建时间
      createdAt: this.formatDate(order.createdAt),
      // 确保订单项是数组
      orderItems: Array.isArray(order.orderItems) ? order.orderItems : []
    }));

    console.log('formatOrderList - 格式化完成:', formatted.length, '条订单');
    return formatted;
  },

  // 格式化日期
  formatDate: function (dateString) {
    if (!dateString) return '';

    const date = new Date(dateString);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');

    return `${year}-${month}-${day} ${hours}:${minutes}`;
  },

  // 取消订单
  onCancelOrder: function (e) {
    const orderId = e.currentTarget.dataset.id;

    wx.showModal({
      title: '确认取消',
      content: '确定要取消这个订单吗？',
      success: (res) => {
        if (res.confirm) {
          this.cancelOrder(orderId);
        }
      }
    });
  },

  // 执行取消订单
  cancelOrder: function (orderId) {
    wx.showLoading({ title: '取消中...' });

    orderApi.cancelOrder(orderId)
      .then(() => {
        wx.hideLoading();
        wx.showToast({
          title: '订单已取消',
          icon: 'success'
        });

        // 刷新订单列表
        this.loadOrderList();
      })
      .catch(err => {
        wx.hideLoading();
        wx.showToast({
          title: err.message || '取消失败',
          icon: 'none'
        });
      });
  },

  // 支付订单
  onPayOrder: function (e) {
    const orderId = e.currentTarget.dataset.id;

    if (!orderId) {
      wx.showToast({
        title: '订单ID不能为空',
        icon: 'none'
      });
      return;
    }

    wx.showLoading({ title: '获取支付参数...' });

    // 调用支付API
    this.getPaymentParams(orderId)
      .then(paymentParams => {
        if (paymentParams) {
          return this.requestPayment(paymentParams);
        }
      })
      .then(() => {
        wx.hideLoading();
        wx.showToast({
          title: '支付成功',
          icon: 'success'
        });

        // 重新加载订单列表
        this.loadOrderList();
      })
      .catch(err => {
        wx.hideLoading();
        console.error('支付失败:', err);
        wx.showToast({
          title: err.message || '支付失败',
          icon: 'none'
        });
      });
  },

  // 获取支付参数
  getPaymentParams: function(orderId) {
    return new Promise((resolve, reject) => {
      const request = require('../../utils/request.js');

      const paymentData = {
        orderId: orderId,
        paymentMethod: "" // 根据API文档，paymentMethod可以为空字符串
      };

      console.log('调用支付API - 请求参数:', paymentData);

      request.post('/user/orders/pay', paymentData, {
        success: (res) => {
          console.log('支付API响应:', res);

          if (res.data && (res.data.code === 0 || res.data.code === 1) && res.data.data) {
            console.log('获取支付参数成功:', res.data.data);
            resolve(res.data.data);
          } else {
            console.error('获取支付参数失败:', res.data);
            reject(new Error(res.data.msg || '获取支付参数失败'));
          }
        },
        fail: (err) => {
          console.error('支付API请求失败:', err);
          reject(err);
        }
      });
    });
  },

  // 发起微信支付
  requestPayment: function(paymentParams) {
    return new Promise((resolve, reject) => {
      wx.requestPayment({
        timeStamp: paymentParams.timeStamp,
        nonceStr: paymentParams.nonceStr,
        package: paymentParams.package,
        signType: paymentParams.signType || 'MD5',
        paySign: paymentParams.paySign,
        success: resolve,
        fail: reject
      });
    });
  },

  // 确认收货
  onConfirmReceive: function (e) {
    const orderId = e.currentTarget.dataset.id;

    if (!orderId) {
      wx.showToast({
        title: '订单ID不能为空',
        icon: 'none'
      });
      return;
    }

    // 查找订单信息进行状态验证
    const order = this.data.orderList.find(o => o.id === orderId);
    if (!order) {
      wx.showToast({
        title: '订单信息不存在',
        icon: 'none'
      });
      return;
    }

    // 检查订单状态
    if (order.status !== '已发货' && order.status !== '待收货') {
      wx.showToast({
        title: '当前订单状态不支持确认收货',
        icon: 'none'
      });
      return;
    }

    wx.showModal({
      title: '确认收货',
      content: '请确认您已收到商品并检查无误。确认收货后订单将完成，无法撤销。',
      confirmText: '确认收货',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          this.confirmReceive(orderId);
        }
      }
    });
  },

  // 执行确认收货
  confirmReceive: function (orderId) {
    if (!orderId) {
      wx.showToast({
        title: '订单ID不能为空',
        icon: 'none'
      });
      return;
    }

    console.log('确认收货 - 订单ID:', orderId);
    wx.showLoading({ title: '确认收货中...' });

    orderApi.confirmReceive(orderId)
      .then((result) => {
        wx.hideLoading();
        console.log('确认收货成功:', result);

        wx.showToast({
          title: '确认收货成功',
          icon: 'success',
          duration: 2000
        });

        // 延迟刷新订单列表，确保后端状态已更新
        setTimeout(() => {
          this.loadOrderList();
        }, 1000);
      })
      .catch(err => {
        wx.hideLoading();
        console.error('确认收货失败:', err);

        wx.showModal({
          title: '确认收货失败',
          content: err.message || '网络异常，请稍后重试',
          showCancel: true,
          confirmText: '重试',
          cancelText: '取消',
          success: (res) => {
            if (res.confirm) {
              // 用户选择重试
              this.confirmReceive(orderId);
            }
          }
        });
      });
  },

  // 申请退款
  onRequestRefund: function (e) {
    const orderId = e.currentTarget.dataset.id;

    if (!orderId) {
      wx.showToast({
        title: '订单ID不能为空',
        icon: 'none'
      });
      return;
    }

    // 查找订单信息进行状态验证
    const order = this.data.orderList.find(o => o.id === orderId);
    if (!order) {
      wx.showToast({
        title: '订单信息不存在',
        icon: 'none'
      });
      return;
    }

    // 检查订单状态 - 只有待发货状态才能申请退款
    if (order.status !== '待发货') {
      wx.showToast({
        title: '只有待发货状态的订单才能申请退款',
        icon: 'none',
        duration: 3000
      });
      return;
    }

    wx.showModal({
      title: '申请退款',
      content: '确定要申请退款吗？申请后商家将停止发货并处理退款。',
      confirmText: '申请退款',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          this.requestRefund(orderId);
        }
      }
    });
  },

  // 执行申请退款（简单版本，用于快速退款）
  requestRefund: function (orderId) {
    if (!orderId) {
      wx.showToast({
        title: '订单ID不能为空',
        icon: 'none'
      });
      return;
    }

    // 查找订单信息获取退款金额
    const order = this.data.orderList.find(o => o.id === orderId);
    if (!order) {
      wx.showToast({
        title: '订单信息不存在',
        icon: 'none'
      });
      return;
    }

    console.log('申请退款 - 订单ID:', orderId);
    wx.showLoading({ title: '提交退款申请...' });

    const refundData = {
      orderId: orderId,
      reason: '用户申请退款',
      refundAmount: order.totalAmount || 0
    };

    orderApi.requestRefund(refundData)
      .then((result) => {
        wx.hideLoading();
        console.log('退款申请成功:', result);

        wx.showToast({
          title: '退款申请已提交',
          icon: 'success',
          duration: 2000
        });

        // 延迟刷新订单列表，确保后端状态已更新
        setTimeout(() => {
          this.loadOrderList();
        }, 1000);
      })
      .catch(err => {
        wx.hideLoading();
        console.error('申请退款失败:', err);

        wx.showModal({
          title: '退款申请失败',
          content: err.message || '网络异常，请稍后重试',
          showCancel: true,
          confirmText: '重试',
          cancelText: '取消',
          success: (res) => {
            if (res.confirm) {
              // 用户选择重试
              this.requestRefund(orderId);
            }
          }
        });
      });
  },

  // 查看订单详情
  onViewDetail: function (e) {
    const orderId = e.currentTarget.dataset.id;

    if (!orderId) {
      wx.showToast({
        title: '订单ID不能为空',
        icon: 'none'
      });
      return;
    }

    console.log('跳转到订单详情页面，订单ID:', orderId);
    wx.navigateTo({
      url: `/pages/order/detail?id=${orderId}`
    });
  },

  // 阻止事件冒泡
  stopPropagation: function () {
    // 阻止事件冒泡，防止按钮点击触发订单项点击
  },

  // 显示退款弹窗
  showRefundPopup: function(e) {
    const orderId = e.currentTarget.dataset.id;
    const order = this.data.orderList.find(o => o.id === orderId);

    if (!order) {
      wx.showToast({
        title: '订单信息不存在',
        icon: 'none'
      });
      return;
    }

    this.setData({
      showRefundPopup: true,
      currentOrderId: orderId,
      refundAmount: order.totalAmount,
      refundReason: ''
    });
  },

  // 隐藏退款弹窗
  hideRefundPopup: function() {
    this.setData({
      showRefundPopup: false,
      refundReason: '',
      refundAmount: 0,
      currentOrderId: null
    });
  },

  // 选择退款原因
  selectRefundReason: function(e) {
    const reason = e.currentTarget.dataset.reason;
    this.setData({
      refundReason: reason
    });
  },

  // 提交退款申请（详细版本，用于弹窗退款）
  submitRefund: function() {
    if (!this.data.refundReason) {
      wx.showToast({
        title: '请选择退款原因',
        icon: 'none'
      });
      return;
    }

    if (!this.data.currentOrderId) {
      wx.showToast({
        title: '订单ID不能为空',
        icon: 'none'
      });
      return;
    }

    if (!this.data.refundAmount || this.data.refundAmount <= 0) {
      wx.showToast({
        title: '退款金额必须大于0',
        icon: 'none'
      });
      return;
    }

    const refundData = {
      orderId: this.data.currentOrderId,
      reason: this.data.refundReason,
      refundAmount: this.data.refundAmount
    };

    console.log('提交退款申请 - 参数:', refundData);

    wx.showLoading({
      title: '提交中...',
      mask: true
    });

    // 使用orderApi统一处理退款申请
    orderApi.requestRefund(refundData)
      .then((result) => {
        wx.hideLoading();
        console.log('退款申请成功:', result);

        wx.showToast({
          title: '退款申请已提交',
          icon: 'success',
          duration: 2000
        });

        this.hideRefundPopup();

        // 延迟刷新订单列表，确保后端状态已更新
        setTimeout(() => {
          this.loadOrderList();
        }, 1000);
      })
      .catch(err => {
        wx.hideLoading();
        console.error('退款申请失败:', err);

        wx.showModal({
          title: '退款申请失败',
          content: err.message || '网络异常，请稍后重试',
          showCancel: true,
          confirmText: '重试',
          cancelText: '取消',
          success: (res) => {
            if (res.confirm) {
              // 用户选择重试
              this.submitRefund();
            }
          }
        });
      });
  }
});
