import request from '@/utils/request'

// 分页查询轮播图
export function getBannerList(params) {
  return request({
    url: '/admin/banners/page',
    method: 'get',
    params
  })
}

// 获取所有轮播图（不分页）
export function getAllBanners() {
  return request({
    url: '/admin/banners',
    method: 'get'
  })
}

// 根据ID获取轮播图详情
export function getBannerDetail(id) {
  return request({
    url: `/admin/banners/${id}`,
    method: 'get'
  })
}

// 新增轮播图
export function addBanner(data) {
  return request({
    url: '/admin/banners',
    method: 'post',
    data
  })
}

// 修改轮播图
export function updateBanner(data) {
  return request({
    url: '/admin/banners',
    method: 'put',
    data
  })
}

// 删除轮播图
export function deleteBanner(id) {
  return request({
    url: `/admin/banners/${id}`,
    method: 'delete'
  })
}

// 启用禁用轮播图
export function updateBannerStatus(id, status) {
  return request({
    url: `/admin/banners/status/${status}`,
    method: 'post',
    data: { id }
  })
}

// 批量删除轮播图
export function batchDeleteBanners(ids) {
  return request({
    url: '/admin/banners/batch',
    method: 'delete',
    data: { ids }
  })
}

// 上传轮播图图片
export function uploadBannerImage(data) {
  return request({
    url: '/admin/upload/image',
    method: 'post',
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    data
  })
}
