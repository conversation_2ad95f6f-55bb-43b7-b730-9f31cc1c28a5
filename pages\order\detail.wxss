/* pages/order/detail.wxss */
.container {
  min-height: 100vh;
  background-color: #f8f8f8;
  padding-bottom: 120rpx; /* 为底部操作栏留出空间 */
}

/* 加载状态 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 200rpx 0;
}

.loading-text {
  font-size: 28rpx;
  color: #999;
}

/* 错误状态 */
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 100rpx 0;
}

.error-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
}

.error-text {
  font-size: 28rpx;
  color: #999;
  margin-bottom: 40rpx;
}

.retry-btn {
  padding: 20rpx 40rpx;
  background-color: #ff6b6b;
  color: #fff;
  border-radius: 30rpx;
  font-size: 28rpx;
  border: none;
}

/* 详情内容 */
.detail-content {
  padding: 20rpx;
}

/* 通用卡片样式 */
.status-card,
.shipping-card,
.address-card,
.products-card,
.payment-card,
.remark-card {
  background-color: #fff;
  border-radius: 10rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.card-header {
  padding: 30rpx;
  border-bottom: 1rpx solid #f5f5f5;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  font-size: 30rpx;
  color: #333;
  font-weight: bold;
}

.header-actions {
  display: flex;
  align-items: center;
}

.refresh-btn {
  font-size: 26rpx;
  color: #ff6b6b;
  padding: 10rpx 20rpx;
  border: 1rpx solid #ff6b6b;
  border-radius: 20rpx;
}

.loading-text {
  font-size: 26rpx;
  color: #999;
}

/* 订单状态卡片 */
.status-header {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40rpx 30rpx 20rpx;
}

.status-icon {
  padding: 20rpx 40rpx;
  border-radius: 50rpx;
  text-align: center;
}

.status-icon.pending {
  background-color: #fff2f0;
  border: 2rpx solid #ff6b6b;
}

.status-icon.cancelled {
  background-color: #f5f5f5;
  border: 2rpx solid #999;
}

.status-icon.normal {
  background-color: #f6ffed;
  border: 2rpx solid #52c41a;
}

.status-text {
  font-size: 32rpx;
  font-weight: bold;
}

.status-icon.pending .status-text {
  color: #ff6b6b;
}

.status-icon.cancelled .status-text {
  color: #999;
}

.status-icon.normal .status-text {
  color: #52c41a;
}

.order-basic-info {
  padding: 20rpx 30rpx 30rpx;
  display: flex;
  flex-direction: column;
  gap: 10rpx;
}

.order-number,
.order-time,
.update-time {
  font-size: 26rpx;
  color: #666;
}

/* 物流信息 */
.shipping-content {
  padding: 0 30rpx 30rpx;
}

.shipping-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
}

.shipping-item:last-child {
  border-bottom: none;
}

.shipping-label {
  font-size: 28rpx;
  color: #666;
}

.shipping-value {
  font-size: 28rpx;
  color: #333;
  flex: 1;
  text-align: right;
}

/* 物流状态样式 */
.shipping-value.status-1 {
  color: #1890ff; /* 揽件 */
}

.shipping-value.status-2 {
  color: #faad14; /* 运输中 */
}

.shipping-value.status-3 {
  color: #722ed1; /* 派送中 */
}

.shipping-value.status-4 {
  color: #52c41a; /* 已签收 */
}

.shipping-value.status-5 {
  color: #f5222d; /* 包裹异常 */
}

.shipping-value.status-10 {
  color: #8c8c8c; /* 退回 */
}

/* 操作按钮 */
.copy-btn, .call-btn {
  font-size: 24rpx;
  color: #ff6b6b;
  margin-left: 20rpx;
  padding: 8rpx 16rpx;
  border: 1rpx solid #ff6b6b;
  border-radius: 16rpx;
  background-color: #fff;
}

/* 物流轨迹 */
.express-timeline {
  margin-top: 20rpx;
  border-top: 1rpx solid #f5f5f5;
}

.timeline-header {
  padding: 20rpx 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #fafafa;
}

.timeline-title {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.toggle-btn {
  font-size: 26rpx;
  color: #ff6b6b;
}

.timeline-content {
  padding: 20rpx 30rpx;
  max-height: 600rpx;
  overflow-y: auto;
}

.timeline-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 30rpx;
  position: relative;
}

.timeline-item:last-child {
  margin-bottom: 0;
}

.timeline-item:not(:last-child)::after {
  content: '';
  position: absolute;
  left: 8rpx;
  top: 24rpx;
  width: 2rpx;
  height: calc(100% + 10rpx);
  background-color: #e8e8e8;
}

.timeline-dot {
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  background-color: #e8e8e8;
  margin-right: 20rpx;
  margin-top: 4rpx;
  flex-shrink: 0;
}

.timeline-dot.active {
  background-color: #ff6b6b;
}

.timeline-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.timeline-time {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 8rpx;
}

.timeline-desc {
  font-size: 26rpx;
  color: #333;
  line-height: 1.5;
}

/* 最新物流信息 */
.latest-express {
  padding: 20rpx 30rpx;
  background-color: #f8f9fa;
}

.latest-item {
  display: flex;
  flex-direction: column;
}

.latest-time {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 8rpx;
}

.latest-desc {
  font-size: 26rpx;
  color: #333;
  line-height: 1.5;
}

/* 快递查询错误 */
.express-error {
  padding: 30rpx;
  text-align: center;
  background-color: #fafafa;
}

.error-text {
  font-size: 26rpx;
  color: #999;
  margin-right: 20rpx;
}

.retry-btn {
  font-size: 26rpx;
  color: #ff6b6b;
  padding: 8rpx 16rpx;
  border: 1rpx solid #ff6b6b;
  border-radius: 16rpx;
}

/* 收货地址 */
.address-content {
  padding: 0 30rpx 30rpx;
}

.address-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15rpx;
}

.consignee-name {
  font-size: 30rpx;
  color: #333;
  font-weight: bold;
}

.consignee-phone {
  font-size: 28rpx;
  color: #666;
}

.address-detail {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
}

/* 商品信息 */
.products-content {
  padding: 0 30rpx 30rpx;
}

.product-item {
  display: flex;
  padding: 30rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
}

.product-item:last-child {
  border-bottom: none;
}

.product-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 8rpx;
  background-color: #f5f5f5;
  margin-right: 20rpx;
}

.product-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.product-name {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.product-specs {
  margin-bottom: 10rpx;
}

.spec-text {
  font-size: 24rpx;
  color: #999;
  background-color: #f5f5f5;
  padding: 4rpx 12rpx;
  border-radius: 4rpx;
}

.product-price-qty {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx;
}

.product-price {
  font-size: 28rpx;
  color: #ff6b6b;
  font-weight: bold;
}

.product-quantity {
  font-size: 24rpx;
  color: #999;
}

.product-subtotal {
  font-size: 26rpx;
  color: #333;
  text-align: right;
}

/* 费用明细 */
.payment-content {
  padding: 0 30rpx 30rpx;
}

.payment-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15rpx 0;
}

.payment-label {
  font-size: 28rpx;
  color: #666;
}

.payment-value {
  font-size: 28rpx;
  color: #333;
}

.payment-divider {
  height: 1rpx;
  background-color: #f5f5f5;
  margin: 15rpx 0;
}

.payment-item.total {
  padding-top: 20rpx;
}

.payment-item.total .payment-label {
  font-size: 30rpx;
  color: #333;
  font-weight: bold;
}

.total-amount {
  font-size: 32rpx;
  color: #ff6b6b;
  font-weight: bold;
}

/* 订单备注 */
.remark-content {
  padding: 0 30rpx 30rpx;
}

.remark-text {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
}

/* 底部操作栏 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  padding: 20rpx 30rpx;
  border-top: 1rpx solid #eee;
  display: flex;
  gap: 20rpx;
  z-index: 100;
}

.action-btn {
  flex: 1;
  padding: 24rpx 0;
  border-radius: 30rpx;
  font-size: 28rpx;
  border: 1rpx solid #ddd;
  background-color: #fff;
  color: #666;
  margin: 0;
}

.action-btn.primary-btn {
  background-color: #ff6b6b;
  color: #fff;
  border-color: #ff6b6b;
}

.action-btn.secondary-btn {
  background-color: #52c41a;
  color: #fff;
  border-color: #52c41a;
}

.action-btn.cancel-btn {
  color: #999;
  border-color: #ddd;
}

.action-btn.back-btn {
  color: #666;
  border-color: #ddd;
}

/* 确认收货按钮特殊样式 */
.action-btn.confirm-receive-btn {
  background: linear-gradient(135deg, #52c41a, #73d13d) !important;
  color: #fff !important;
  border: 1rpx solid #52c41a !important;
  box-shadow: 0 4rpx 12rpx rgba(82, 196, 26, 0.3);
  font-weight: bold;
  position: relative;
  overflow: hidden;
  flex: 1;
  padding: 24rpx 0;
  border-radius: 30rpx;
  font-size: 28rpx;
  margin: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-btn.confirm-receive-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.action-btn.confirm-receive-btn:active::before {
  left: 100%;
}

.action-btn.confirm-receive-btn .btn-icon {
  font-size: 28rpx;
  margin-right: 8rpx;
}

.action-btn.confirm-receive-btn .btn-text {
  font-size: 28rpx;
  font-weight: bold;
}

/* 发货状态提示 */
.shipping-status-tip {
  margin: 20rpx 0;
  padding: 30rpx;
  background: linear-gradient(135deg, #f6ffed, #f0f9ff);
  border: 1rpx solid #b7eb8f;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 2rpx 8rpx rgba(82, 196, 26, 0.1);
}

.tip-icon {
  font-size: 48rpx;
  margin-right: 20rpx;
}

.tip-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.tip-title {
  font-size: 30rpx;
  color: #52c41a;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.tip-desc {
  font-size: 26rpx;
  color: #389e0d;
  line-height: 1.4;
}

/* 待发货状态提示 */
.pending-shipment-tip {
  margin: 20rpx 0;
  padding: 30rpx;
  background: linear-gradient(135deg, #fff7e6, #fef9f0);
  border: 1rpx solid #ffd591;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 2rpx 8rpx rgba(255, 193, 7, 0.1);
}

.pending-shipment-tip .tip-icon {
  font-size: 48rpx;
  margin-right: 20rpx;
}

.pending-shipment-tip .tip-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.pending-shipment-tip .tip-title {
  font-size: 30rpx;
  color: #fa8c16;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.pending-shipment-tip .tip-desc {
  font-size: 26rpx;
  color: #d48806;
  line-height: 1.4;
}

/* 评价弹窗样式 */
.review-popup {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.review-popup-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
}

.review-popup-content {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  border-radius: 24rpx 24rpx 0 0;
  padding: 30rpx;
  max-height: 85vh;
  display: flex;
  flex-direction: column;
  box-shadow: 0 -8rpx 32rpx rgba(0, 0, 0, 0.1);
  animation: slideUp 0.3s ease;
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

.review-popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.review-popup-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.review-popup-close {
  font-size: 40rpx;
  color: #999;
  padding: 10rpx;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.review-popup-close:active {
  background-color: #f5f5f5;
  color: #666;
}

.review-popup-body {
  flex: 1;
  overflow-y: auto;
}

.review-label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 16rpx;
  display: block;
}

/* 评分样式 */
.review-rating {
  margin-bottom: 30rpx;
}

.rating-stars {
  display: flex;
  gap: 15rpx;
  align-items: center;
  margin-top: 10rpx;
}

.star {
  font-size: 50rpx;
  color: #e8e8e8;
  cursor: pointer;
  transition: all 0.2s ease;
  text-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.1);
}

.star:active {
  transform: scale(1.1);
}

.star.active {
  color: #ffd700;
  text-shadow: 0 2rpx 8rpx rgba(255, 215, 0, 0.3);
}

/* 评价内容样式 */
.review-content {
  margin-bottom: 30rpx;
}

.review-textarea {
  width: 100%;
  height: 200rpx;
  background: #f8f8f8;
  border-radius: 12rpx;
  padding: 20rpx;
  font-size: 28rpx;
  box-sizing: border-box;
}

/* 图片上传样式 */
.review-images {
  margin-bottom: 30rpx;
}

.image-list {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.image-item {
  position: relative;
  width: 160rpx;
  height: 160rpx;
}

.image-item image {
  width: 100%;
  height: 100%;
  border-radius: 8rpx;
}

.delete-btn {
  position: absolute;
  top: -20rpx;
  right: -20rpx;
  width: 40rpx;
  height: 40rpx;
  background: rgba(0, 0, 0, 0.5);
  color: #fff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
}

.upload-btn {
  width: 160rpx;
  height: 160rpx;
  background: #f8f8f8;
  border: 2rpx dashed #ddd;
  border-radius: 8rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.upload-btn:active {
  background: #f0f0f0;
  border-color: #ccc;
}

.upload-icon {
  font-size: 48rpx;
  color: #999;
  margin-bottom: 8rpx;
}

.upload-text {
  font-size: 20rpx;
  color: #999;
}

.upload-tip {
  font-size: 22rpx;
  color: #999;
  margin-top: 16rpx;
  text-align: center;
  line-height: 1.4;
}

/* 底部按钮样式 */
.review-popup-footer {
  display: flex;
  gap: 20rpx;
  margin-top: 30rpx;
  padding-top: 20rpx;
  border-top: 1rpx solid #f0f0f0;
}

.cancel-btn, .submit-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 40rpx;
  font-size: 28rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  transition: all 0.2s ease;
  font-weight: 500;
}

.cancel-btn {
  background: #f8f8f8;
  color: #666;
}

.cancel-btn:active {
  background: #e8e8e8;
  transform: scale(0.98);
}

.submit-btn {
  background: linear-gradient(135deg, #ff6b6b, #ff8e8e);
  color: #fff;
  box-shadow: 0 4rpx 12rpx rgba(255, 107, 107, 0.3);
}

.submit-btn:active {
  background: linear-gradient(135deg, #ff5252, #ff7979);
  transform: scale(0.98);
  box-shadow: 0 2rpx 8rpx rgba(255, 107, 107, 0.4);
}

/* 商品信息中的评价相关样式 */
.item-actions {
  margin-top: 15rpx;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  min-height: 60rpx;
}

/* 评价状态加载中 */
.review-loading {
  display: flex;
  align-items: center;
  justify-content: center;
}

.review-loading .loading-text {
  font-size: 22rpx;
  color: #999;
  padding: 8rpx 16rpx;
}

/* 已评价状态 */
.reviewed-status {
  display: flex;
  align-items: center;
  justify-content: center;
}

.reviewed-text {
  font-size: 24rpx;
  color: #52c41a;
  background: #f6ffed;
  border: 1rpx solid #b7eb8f;
  border-radius: 20rpx;
  padding: 8rpx 20rpx;
  line-height: 1.4;
  min-width: 80rpx;
  text-align: center;
  font-weight: 500;
}

/* 评价按钮样式 */
.review-btn {
  font-size: 24rpx;
  color: #ff6b6b;
  background: #fff;
  border: 1rpx solid #ff6b6b;
  border-radius: 20rpx;
  padding: 8rpx 20rpx;
  line-height: 1.4;
  min-width: 80rpx;
  text-align: center;
  transition: all 0.3s ease;
}

.review-btn:active {
  background-color: #ff6b6b;
  color: #fff;
  transform: scale(0.95);
}
