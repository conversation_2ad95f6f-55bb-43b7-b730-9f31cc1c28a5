<!--pages/index/index.wxml-->
<view class="container">
  <!-- 搜索栏 -->
  <view class="search-container">
    <view class="search-box">
      <icon type="search" size="14" color="#999"></icon>
      <input type="text" placeholder="搜索宠物服饰" confirm-type="search" bindconfirm="onSearch" />
    </view>
  </view>
  
  <!-- 轮播图 -->
  <view class="banner-container">
    <swiper indicator-dots="{{true}}" autoplay="{{true}}" interval="{{3000}}" duration="{{500}}" circular="{{true}}">
      <block wx:for="{{bannerList}}" wx:key="id">
        <swiper-item>
          <image src="{{item.imageUrl}}" mode="aspectFill" class="banner-image" />
        </swiper-item>
      </block>
    </swiper>
  </view>
  
  <!-- 分类导航 -->
  <view class="category-container">
    <view class="category-list">
      <view class="category-item" wx:for="{{categoryList}}" wx:key="id" bindtap="navigateToCategory" data-id="{{item.id}}">
        <image src="{{item.icon}}" mode="aspectFit" class="category-icon" />
        <text class="category-name">{{item.name}}</text>
      </view>
    </view>
  </view>
  
  <!-- 广告图 -->
  <view class="ad-container">
    <image src="/images/ad-banner.jpg" mode="widthFix" class="ad-image" />
  </view>
  
  <!-- 热门商品 -->
  <view class="hot-product-container">
    <view class="section-title">
      <text class="title-text">热门商品</text>
      <text class="more-text">查看更多 ></text>
    </view>
    <view class="product-list">
      <view class="product-item" wx:for="{{hotProductList}}" wx:key="id" bindtap="navigateToDetail" data-id="{{item.id}}">
        <image src="{{item.imageUrl}}" mode="aspectFill" class="product-image" />
        <view class="product-info">
          <text class="product-name">{{item.name}}</text>
          <view class="product-price-container">
            <text class="product-price">¥{{item.price}}</text>
            <text class="product-original-price">¥{{item.originalPrice}}</text>
          </view>
          <text class="product-sales">已售{{item.sales}}件</text>
        </view>
      </view>
    </view>
  </view>
  
  <!-- 底部标语 -->
  <view class="footer-slogan">
    <image src="/images/footer-image.jpg" mode="aspectFit" class="footer-image" />
    <text class="slogan-text">只和你，永远幸福。</text>
  </view>
</view>
