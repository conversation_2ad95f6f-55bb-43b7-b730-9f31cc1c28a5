.profile-container {
  padding: 20px;
  background-color: #f8f8f8;
  min-height: 100vh;
  box-sizing: border-box;
}

.avatar-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 30px;
  padding: 20px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.avatar-image {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  border: 1px solid #eee;
  margin-bottom: 10px;
}

.avatar-btn {
  background: none;
  border: none;
  padding: 0;
  margin: 0;
  font-size: 13px;
  color: #007aff;
  line-height: 1.5;
}

.avatar-btn::after {
  border: none;
}

.nickname-btn {
  background: none;
  border: none;
  padding: 0;
  margin: 0;
  width: auto;
  text-align: right;
  flex: 1;
}

.nickname-btn::after {
  border: none;
}

.nickname-text {
  font-size: 15px;
  color: #333;
}

.form-group {
  background-color: #fff;
  padding: 12px 15px;
  display: flex;
  align-items: center;
  min-height: 48px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.02);
}

.form-label {
  width: 100px;
  font-size: 15px;
  color: #333;
  margin-right: 10px;
}

.form-input {
  flex: 1;
  font-size: 15px;
  color: #333;
  text-align: right;
  margin-left: 120rpx;
}

.form-input::placeholder {
  color: #bbb;
  font-size: 14px;
}

.form-radio-group {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  flex: 1;
}

.form-radio-group .radio {
  margin-left: 15px;
  font-size: 15px;
  color: #333;
  display: flex;
  align-items: center;
}

.form-radio-group .radio radio {
  transform: scale(0.8);
  margin-right: 4px;
}

.picker.form-input {
  width: 100%;
  text-align: right;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.picker-arrow {
  margin-left: 5px;
  color: #999;
  font-size: 14px;
}

.switch-group {
  justify-content: space-between;
}

.switch-group switch {
  transform: scale(0.8);
}

.save-btn {
  margin-top: 25px;
  background-color: #07c160;
  color: white;
  font-size: 16px;
  border-radius: 8px;
  padding: 12px 0;
  text-align: center;
  border: none;
}

.save-btn:active {
  background-color: #059c4a;
} 