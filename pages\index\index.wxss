/* pages/index/index.wxss */
.container {
  padding-bottom: 30rpx;
}

/* 搜索栏样式 */
.search-container {
  padding: 20rpx 30rpx;
  background-color: #fff;
  position: sticky;
  top: 0;
  z-index: 100;
}

.search-box {
  display: flex;
  align-items: center;
  background-color: #f5f5f5;
  border-radius: 30rpx;
  padding: 10rpx 20rpx;
}

.search-box icon {
  margin-right: 10rpx;
}

.search-box input {
  flex: 1;
  height: 60rpx;
  font-size: 28rpx;
}

/* 轮播图样式 */
.banner-container {
  width: 100%;
  height: 350rpx;
}

.banner-container swiper {
  width: 100%;
  height: 100%;
}

.banner-image {
  width: 100%;
  height: 100%;
}

/* 分类导航样式 */
.category-container {
  background-color: #fff;
  padding: 20rpx 0;
  margin-bottom: 20rpx;
}

.category-list {
  display: flex;
  justify-content: space-around;
}

.category-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 20%;
}

.category-icon {
  width: 80rpx;
  height: 80rpx;
  margin-bottom: 10rpx;
}

.category-name {
  font-size: 24rpx;
  color: #333;
}

/* 广告图样式 */
.ad-container {
  padding: 0 20rpx;
  margin-bottom: 20rpx;
}

.ad-image {
  width: 100%;
  border-radius: 10rpx;
}

/* 热门商品样式 */
.hot-product-container {
  background-color: #fff;
  padding: 20rpx;
  margin-bottom: 20rpx;
}

.section-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.title-text {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  position: relative;
  padding-left: 20rpx;
}

.title-text::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 8rpx;
  height: 30rpx;
  background-color: #8ab6d6;
  border-radius: 4rpx;
}

.more-text {
  font-size: 24rpx;
  color: #999;
}

.product-list {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}

.product-item {
  width: 48%;
  background-color: #fff;
  border-radius: 10rpx;
  overflow: hidden;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.product-image {
  width: 100%;
  height: 300rpx;
}

.product-info {
  padding: 16rpx;
}

.product-name {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
}

.product-price-container {
  display: flex;
  align-items: baseline;
  margin-bottom: 10rpx;
}

.product-price {
  font-size: 32rpx;
  color: #ff6b81;
  font-weight: bold;
  margin-right: 10rpx;
}

.product-original-price {
  font-size: 24rpx;
  color: #999;
  text-decoration: line-through;
}

.product-sales {
  font-size: 22rpx;
  color: #999;
}

/* 底部标语样式 */
.footer-slogan {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 30rpx 0;
}

.footer-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 10rpx;
}

.slogan-text {
  font-size: 28rpx;
  color: #8ab6d6;
}
