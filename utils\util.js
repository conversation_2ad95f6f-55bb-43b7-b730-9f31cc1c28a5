// utils/util.js

/**
 * 格式化时间
 * @param {Date} date 日期对象
 * @param {String} format 格式化模式
 * @returns {String} 格式化后的时间字符串
 */
const formatTime = (date, format = 'YYYY-MM-DD HH:mm:ss') => {
  const year = date.getFullYear()
  const month = date.getMonth() + 1
  const day = date.getDate()
  const hour = date.getHours()
  const minute = date.getMinutes()
  const second = date.getSeconds()

  format = format.replace('YYYY', year)
  format = format.replace('MM', formatNumber(month))
  format = format.replace('DD', formatNumber(day))
  format = format.replace('HH', formatNumber(hour))
  format = format.replace('mm', formatNumber(minute))
  format = format.replace('ss', formatNumber(second))

  return format
}

/**
 * 数字补零
 * @param {Number} n 数字
 * @returns {String} 补零后的字符串
 */
const formatNumber = n => {
  n = n.toString()
  return n[1] ? n : `0${n}`
}

/**
 * 价格格式化
 * @param {Number} price 价格
 * @param {Number} decimals 小数位数
 * @returns {String} 格式化后的价格
 */
const formatPrice = (price, decimals = 2) => {
  return parseFloat(price).toFixed(decimals)
}

/**
 * 获取当前页面路径
 * @returns {String} 当前页面路径
 */
const getCurrentPageUrl = () => {
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1]
  const url = currentPage.route
  return url
}

/**
 * 获取当前页面路径与参数
 * @returns {String} 当前页面路径与参数
 */
const getCurrentPageUrlWithArgs = () => {
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1]
  const url = currentPage.route
  const options = currentPage.options
  
  let urlWithArgs = `/${url}?`
  for (let key in options) {
    const value = options[key]
    urlWithArgs += `${key}=${value}&`
  }
  urlWithArgs = urlWithArgs.substring(0, urlWithArgs.length - 1)
  
  return urlWithArgs
}

/**
 * 显示消息提示框
 * @param {String} title 提示的内容
 * @param {String} icon 图标，有效值 "success", "loading", "none"
 * @param {Number} duration 提示的延迟时间
 */
const showToast = (title, icon = 'none', duration = 1500) => {
  wx.showToast({
    title: title,
    icon: icon,
    duration: duration
  })
}

/**
 * 显示模态对话框
 * @param {String} title 提示的标题
 * @param {String} content 提示的内容
 * @param {Boolean} showCancel 是否显示取消按钮
 * @returns {Promise} Promise对象
 */
const showModal = (title, content, showCancel = true) => {
  return new Promise((resolve, reject) => {
    wx.showModal({
      title: title,
      content: content,
      showCancel: showCancel,
      success(res) {
        if (res.confirm) {
          resolve(true)
        } else if (res.cancel) {
          resolve(false)
        }
      },
      fail(err) {
        reject(err)
      }
    })
  })
}

module.exports = {
  formatTime,
  formatNumber,
  formatPrice,
  getCurrentPageUrl,
  getCurrentPageUrlWithArgs,
  showToast,
  showModal
}
