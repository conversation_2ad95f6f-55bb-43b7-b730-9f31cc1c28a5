<view class="container profile-container">
  <view class="avatar-section">
    <image class="avatar-image" src="{{avatarUrl ? avatarUrl : '/images/default-avatar.png'}}" mode="aspectFill"></image>
    <button class="avatar-btn" open-type="chooseAvatar" bindchooseavatar="onChooseAvatar">点击修改头像</button>
  </view>

  <form bindsubmit="saveProfile">
    <view class="form-group">
      <label class="form-label">昵称</label>
      <input class="form-input" name="name" type="nickname" value="{{userInfo.name}}" placeholder="请输入昵称" bindnicknamereview="onChooseNickname" bindinput="onNameInput"/>
    </view>

    <view class="form-group">
      <label class="form-label">性别</label>
      <radio-group name="gender" class="form-radio-group" bindchange="bindGenderChange">
        <label class="radio"><radio value="male" checked="{{userInfo.gender === 'male'}}"/>男</label>
        <label class="radio"><radio value="female" checked="{{userInfo.gender === 'female'}}"/>女</label>
        <label class="radio"><radio value="other" checked="{{userInfo.gender === 'other' || !userInfo.gender}}"/>保密</label>
      </radio-group>
    </view>

    <view class="form-group">
      <label class="form-label">生日</label>
      <picker mode="date" value="{{userInfo.birthday}}" start="1900-01-01" end="{{endDate}}" bindchange="bindBirthdayChange">
        <view class="picker form-input">
          <text>{{userInfo.birthday ? userInfo.birthday : '请选择出生日期'}}</text>
          <text class="picker-arrow">></text>
        </view>
      </picker>
    </view>

    <view class="form-group">
      <label class="form-label">手机号</label>
      <input name="phone" class="form-input" type="number" value="{{userInfo.phone}}" placeholder="请输入手机号" />
    </view>
    
    <view class="form-group">
      <label class="form-label">地区</label>
      <input name="region" class="form-input" value="{{userInfo.region}}" placeholder="请输入地区" />
    </view>

    <view class="form-group switch-group">
      <label class="form-label">开启个性化推荐</label>
      <switch name="enableRecommendation" checked="{{userInfo.enableRecommendation}}" bindchange="bindRecommendationChange"/>
    </view>

    <button class="save-btn" form-type="submit" type="primary">保存信息</button>
  </form>
</view> 