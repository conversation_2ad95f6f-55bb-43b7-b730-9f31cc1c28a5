/* pages/product/product.wxss */
.container {
  padding-bottom: 140rpx;
  background-color: #f8f8f8;
  min-height: 100vh;
  box-sizing: border-box;
  width: 100%;
  overflow-x: hidden;
}

/* 加载中样式 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 600rpx;
  width: 100%;
}

.loading-spinner {
  width: 80rpx;
  height: 80rpx;
  border: 8rpx solid #f3f3f3;
  border-top: 8rpx solid #ff4757;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.05);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 30rpx;
  color: #666;
  font-weight: 500;
}

/* 商品轮播图 */
.product-swiper {
  width: 100%;
  height: 750rpx;
  position: relative;
  background-color: #f8f8f8;
}

.slide-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: opacity 0.3s ease;
}

.swiper-controls {
  position: absolute;
  right: 30rpx;
  bottom: 30rpx;
  display: flex;
  align-items: center;
  z-index: 10;
}

.swiper-indicator {
  position: absolute;
  right: 30rpx;
  bottom: 50%;
  background-color: rgba(0, 0, 0, 0.5);
  color: #fff;
  font-size: 26rpx;
  padding: 8rpx 20rpx;
  border-radius: 30rpx;
  font-weight: 500;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
  z-index: 10;
}

.share-btn-float {
  background-color: rgba(0, 0, 0, 0.5);
  color: #fff;
  width: 70rpx;
  height: 70rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  margin: 0;
  line-height: 1;
  border: none;
}

.share-icon-float {
  width: 40rpx;
  height: 40rpx;
}

/* 商品价格信息 */
.product-price-container {
  background-color: #fff;
  padding: 30rpx;
  margin-top: 20rpx;
  border-radius: 20rpx 20rpx 0 0;
  box-shadow: 0 -4rpx 16rpx rgba(0, 0, 0, 0.05);
  position: relative;
  z-index: 5;
}

.price-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.price-info {
  display: flex;
  align-items: baseline;
  flex-wrap: wrap;
}

.price-symbol {
  font-size: 60rpx;
  color: #ff4757;
  font-weight: bold;
}

.price-value {
  font-size: 45rpx;
  color: #ff4757;
  font-weight: bold;
  margin-right: 20rpx;
  line-height: 1;
}

.original-price {
  font-size: 26rpx;
  color: #999;
  text-decoration: line-through;
  margin-right: 20rpx;
}

.sales-info {
  font-size: 24rpx;
  color: #666;
  margin-left: auto;
}

/* 商品名称 */
.product-name-row {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24rpx;
}

.product-name {
  font-size: 36rpx;
  color: #333;
  font-weight: bold;
  flex: 1;
  margin-right: 20rpx;
  margin-bottom: -30rpx;
  line-height: 1.4;
  letter-spacing: 0.5rpx;
}

/* 服务信息 */
.service-row {
  display: flex;
  align-items: center;
  margin-top: 24rpx;
  background-color: #f9f9f9;
  padding: 16rpx 20rpx;
  border-radius: 12rpx;
}

.service-tag {
  display: flex;
  align-items: center;
  margin-right: 12rpx;
  background-color: rgba(255, 71, 87, 0.1);
  padding: 6rpx 12rpx;
  border-radius: 8rpx;
}

.service-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 8rpx;
}

.service-tag text {
  font-size: 24rpx;
  color: #ff4757;
  font-weight: 500;
}

.service-desc {
  font-size: 24rpx;
  color: #666;
  flex: 1;
}

.service-arrow {
  font-size: 24rpx;
  color: #999;
  font-family: "Arial";
  font-weight: bold;
}

/* 配送信息 */
.delivery-section, .service-section {
  background-color: #fff;
  padding: 0 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.03);
}

.section-item {
  display: flex;
  align-items: center;
  padding: 30rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
}

.section-label {
  font-size: 28rpx;
  color: #333;
  width: 120rpx;
  font-weight: 500;
}

.section-content {
  flex: 1;
  font-size: 28rpx;
  color: #333;
  margin-left: -30rpx;
}

.section-arrow {
  font-size: 24rpx;
  color: #999;
  font-family: "Arial";
  font-weight: bold;
}

.delivery-info {
  padding: 20rpx 0 30rpx 120rpx;
  font-size: 26rpx;
  display: flex;
  align-items: center;
  margin-left: 20rpx;
}

.delivery-label {
  color: #999;
  margin-right: 8rpx;
  margin-left: -20rpx;
}

.delivery-value {
  color: #ff4757;
  font-weight: 500;
}

/* 选项卡 */
.tab-container {
  margin-top: 20rpx;
  background-color: #fff;
  overflow: hidden;
}

.tab-header {
  display: flex;
  border-bottom: 1rpx solid #f5f5f5;
  background-color: #fff;
  position: sticky;
  top: 0;
  z-index: 10;
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 30rpx 0;
  font-size: 28rpx;
  color: #666;
  position: relative;
  transition: all 0.3s;
}

.tab-item.active {
  color: #ff4757;
  font-weight: bold;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60rpx;
  height: 6rpx;
  background-color: #ff4757;
  border-radius: 3rpx;
}

.tab-content {
  padding: 30rpx;
}

/* 详情页面特殊处理 - 移除padding以实现无缝展示 */
.tab-panel[hidden="false"]:nth-child(3) .product-detail {
  margin: -30rpx;
  padding: 0;
}

.tab-panel[hidden="false"]:nth-child(3) .detail-description {
  padding: 30rpx;
  margin: 0 0 30rpx 0;
}

.tab-panel[hidden="false"]:nth-child(3) .no-detail-images {
  padding: 30rpx;
  margin: 0;
}

/* 商品描述和详情 */
.product-desc {
  font-size: 28rpx;
  color: #333;
  line-height: 1.8;
  text-align: justify;
}

.product-detail {
  padding: 0;
}

.detail-description {
  margin-bottom: 30rpx;
}

.detail-text {
  font-size: 28rpx;
  color: #333;
  line-height: 1.8;
  text-align: justify;
  display: block;
}

/* 详情图片无缝展示 */
.detail-images {
  width: 100%;
  margin: 0;
  padding: 0;
}

.detail-image {
  width: 100%;
  display: block;
  margin: 0;
  padding: 0;
  border: none;
  vertical-align: top;
}

.no-detail-images {
  padding: 30rpx 0;
  text-align: center;
  color: #999;
}

/* 评价统计 */
.review-summary {
  background-color: #f8f9fa;
  padding: 30rpx;
  margin-bottom: 20rpx;
  border-radius: 12rpx;
}

.review-stats {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.average-rating {
  display: flex;
  align-items: center;
}

.rating-number {
  font-size: 48rpx;
  font-weight: bold;
  color: #ff4757;
  margin-right: 20rpx;
}

.rating-stars {
  display: flex;
  align-items: center;
}

.review-count {
  font-size: 28rpx;
  color: #666;
}

/* 评价列表 */
.comment-list {
  padding-bottom: 20rpx;
}

/* 无评价状态 */
.no-comments {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 80rpx 0;
  text-align: center;
}

.empty-comment-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
  opacity: 0.6;
}

.empty-comment-text {
  font-size: 32rpx;
  color: #999;
  margin-bottom: 10rpx;
  font-weight: 500;
}

.empty-comment-desc {
  font-size: 26rpx;
  color: #ccc;
}

.comment-item {
  padding: 24rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
}

.comment-header {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.comment-avatar {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  margin-right: 16rpx;
  border: 1rpx solid #f0f0f0;
}

.comment-user {
  font-size: 28rpx;
  color: #333;
  margin-right: 16rpx;
  font-weight: 500;
}

.comment-star {
  display: flex;
  margin-left: auto;
}

.star-icon {
  width: 28rpx;
  height: 28rpx;
  margin-right: 4rpx;
}

.comment-content {
  font-size: 28rpx;
  color: #333;
  line-height: 1.6;
  margin-bottom: 20rpx;
  text-align: justify;
}

.comment-images {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 20rpx;
}

.comment-image {
  width: 180rpx;
  height: 180rpx;
  margin-right: 12rpx;
  margin-bottom: 12rpx;
  border-radius: 8rpx;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
}

.comment-footer {
  font-size: 24rpx;
  color: #999;
  display: flex;
  justify-content: space-between;
}

.comment-date {
  color: #999;
}

/* 推荐 */
.recommend-list {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -10rpx;
}

.recommend-item {
  width: calc(50% - 20rpx);
  margin: 0 10rpx 20rpx;
  background-color: #fff;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.recommend-item:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.recommend-image {
  width: 100%;
  height: 320rpx;
  object-fit: cover;
}

.recommend-info {
  padding: 20rpx;
}

.recommend-name {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 12rpx;
  display: block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-weight: 500;
}

.recommend-price {
  font-size: 30rpx;
  color: #ff4757;
  font-weight: bold;
}

/* 底部操作栏 */
.footer {
  position: fixed;
  left: 5rpx;
  bottom: 15rpx;
  right: 5rpx;
  width: 100%;
  height: 90rpx;
  background-color: #fff;
  display: flex;
  border-top: 1rpx solid #f0f0f0;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.footer-left {
  width: 35%;
  display: flex;
  justify-content: space-around;
  align-items: center;
}

.footer-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 0 5rpx;
}

.footer-icon {
  width: 50rpx;
  height: 50rpx;
  margin-bottom: 4rpx;
}

.footer-btn-text {
  font-size: 20rpx;
  color: #666;
}

.footer-right {
  flex: 65%;
  display: flex;
  padding: 5rpx 15rpx;
  justify-content: center;
  align-items: center;
}

.btn-container {
  display: flex;
  justify-content: center;
  align-items: center;
  width: auto !important;
  flex-wrap: nowrap !important;
}

.add-cart-btn {
  width: 220rpx !important;
  max-width: 220rpx !important;
  min-width: 0 !important;
  background-color: #fff8f8 !important;
  color: #000000 !important;
  border: 1rpx solid #000000 !important;
  height: 70rpx !important;
  line-height: 70rpx !important;
  text-align: center !important;
  font-size: 22rpx !important;
  font-weight: bold !important;
  border-radius: 8rpx !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  margin: 0 8rpx !important;
  padding: 0 !important;
  box-sizing: border-box !important;
}

.buy-now-btn {
  width: 220rpx !important;
  max-width: 220rpx !important;
  min-width: 0 !important;
  background-color: #000000 !important;
  border: 1rpx solid #000000 !important;
  color: rgb(255, 255, 255) !important;
  height: 70rpx !important;
  line-height: 70rpx !important;
  text-align: center !important;
  font-size: 22rpx !important;
  font-weight: bold !important;
  border-radius: 8rpx !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  margin: 0 8rpx !important;
  padding: 0 !important;
  box-sizing: border-box !important;
}

/* SKU选择面板 */
.sku-panel-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 1000;
  backdrop-filter: blur(3px);
}

.sku-panel {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #fff;
  border-radius: 24rpx 24rpx 0 0;
  transform: translateY(100%);
  transition: transform 0.3s ease, opacity 0.3s ease;
  z-index: 1001;
  max-height: 80vh;
  overflow-y: auto;
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);
  opacity: 0;
}

.sku-panel.show {
  transform: translateY(0);
  opacity: 1;
}

.sku-header {
  padding: 30rpx;
  position: relative;
}

.sku-close {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  font-size: 40rpx;
  color: #999;
  width: 60rpx;
  height: 60rpx;
  line-height: 60rpx;
  text-align: center;
  z-index: 10;
  border-radius: 50%;
  background-color: #f8f8f8;
}

.sku-product-info {
  display: flex;
  padding-bottom: 30rpx;
  border-bottom: 1rpx solid #f5f5f5;
}

.sku-image {
  width: 180rpx;
  height: 180rpx;
  border-radius: 12rpx;
  margin-right: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.sku-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.sku-price-row {
  display: flex;
  align-items: baseline;
  margin-bottom: 12rpx;
}

.sku-price-symbol {
  font-size: 30rpx;
  color: #ff4757;
  font-weight: bold;
}

.sku-price-value {
  font-size: 40rpx;
  color: #ff4757;
  font-weight: bold;
}

.sku-stock {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 12rpx;
  background-color: #f8f8f8;
  display: inline-block;
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
}

.sku-selected {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.sku-body {
  padding: 0 30rpx;
}

.sku-row {
  padding: 30rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
}

.sku-title {
  font-size: 30rpx;
  color: #333;
  margin-bottom: 24rpx;
  display: block;
  font-weight: bold;
}

.sku-options {
  display: flex;
  flex-wrap: wrap;
}

.sku-option {
  min-width: 160rpx;
  height: 80rpx;
  border: 1rpx solid #ddd;
  border-radius: 40rpx;
  font-size: 28rpx;
  color: #333;
  margin-right: 20rpx;
  margin-bottom: 20rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  position: relative;
  overflow: hidden;
  padding: 0 24rpx;
  transition: all 0.2s ease;
}

.sku-option.selected {
  border-color: #ff4757;
  background-color: rgba(255, 71, 87, 0.05);
  color: #ff4757;
}

.sku-option.disabled {
  border-color: #eee;
  background-color: #f8f8f8;
  color: #ccc;
  opacity: 0.8;
  position: relative;
}

.sku-option.disabled::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.4);
  z-index: 1;
}

.sku-option-name {
  font-size: 28rpx;
  font-weight: 500;
}

.sku-option-price {
  font-size: 24rpx;
  color: #999;
  margin-top: 4rpx;
}

.sku-option-stock {
  font-size: 22rpx;
  color: #999;
  margin-top: 4rpx;
}

.disabled .sku-option-stock {
  color: #ff4757;
}

.quantity-selector {
  display: flex;
  align-items: center;
}

.quantity-btn {
  width: 70rpx;
  height: 70rpx;
  border: 1rpx solid #eee;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  background-color: #f8f8f8;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.quantity-btn:active {
  background-color: #f0f0f0;
}

.quantity-btn.disabled {
  color: #ccc;
  background-color: #f8f8f8;
}

.quantity-input {
  width: 100rpx;
  height: 70rpx;
  margin: 0 20rpx;
  text-align: center;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  background-color: #fff;
}

.sku-footer {
  padding: 30rpx;
}

.sku-confirm-btn {
  width: 100%;
  height: 90rpx;
  line-height: 90rpx;
  background: #000000;
  color: rgb(255, 255, 255);
  font-size: 32rpx;
  font-weight: bold;
  border-radius: 45rpx;
  border: none;
  letter-spacing: 2rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.3);
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.sku-confirm-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 6rpx rgba(255, 71, 87, 0.2);
}

/* 地址选择弹窗 */
.address-popup-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 1000;
  backdrop-filter: blur(3px);
}

.address-popup {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #fff;
  border-radius: 24rpx 24rpx 0 0;
  transform: translateY(100%);
  transition: transform 0.3s ease, opacity 0.3s ease;
  z-index: 1001;
  max-height: 70vh;
  display: flex;
  flex-direction: column;
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);
  opacity: 0;
}

.address-popup.show {
  transform: translateY(0);
  opacity: 1;
}

.address-popup-header {
  padding: 30rpx;
  text-align: center;
  position: relative;
  border-bottom: 1rpx solid #f5f5f5;
}

.address-popup-title {
  font-size: 34rpx;
  color: #333;
  font-weight: bold;
}

.address-popup-close {
  position: absolute;
  right: 20rpx;
  top: 20rpx;
  font-size: 40rpx;
  color: #999;
  width: 60rpx;
  height: 60rpx;
  line-height: 60rpx;
  text-align: center;
  border-radius: 50%;
  background-color: #f8f8f8;
}

.address-list {
  flex: 1;
  overflow-y: auto;
}

.address-item {
  display: flex;
  padding: 30rpx;
  border-bottom: 1rpx solid #f5f5f5;
  transition: background-color 0.2s ease;
}

.address-item.selected {
  background-color: #fff8f8;
}

.address-select-icon {
  margin-right: 20rpx;
  display: flex;
  align-items: flex-start;
  padding-top: 6rpx;
}

.address-info {
  flex: 1;
}

.address-contact {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  margin-bottom: 12rpx;
}

.address-name {
  font-size: 30rpx;
  color: #333;
  margin-right: 20rpx;
  font-weight: 500;
}

.address-phone {
  font-size: 28rpx;
  color: #666;
}

.address-default {
  font-size: 22rpx;
  color: #ff4757;
  background-color: rgba(255, 71, 87, 0.1);
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
  margin-left: 20rpx;
  font-weight: 500;
}

.address-detail {
  font-size: 28rpx;
  color: #333;
  line-height: 1.5;
}

.address-popup-footer {
  padding: 30rpx;
  border-top: 1rpx solid #f5f5f5;
}

.add-address-btn {
  width: 100%;
  height: 90rpx;
  line-height: 90rpx;
  background-color: #ff4757;
  color: #fff;
  font-size: 30rpx;
  font-weight: bold;
  border-radius: 45rpx;
  border: none;
  transition: all 0.2s ease;
  display: flex;
  justify-content: center;
  align-items: center;
}

.other-area-btn:active {
  background-color: #f0f0f0;
}

/* 动画效果 */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from { transform: translateY(20rpx); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

.product-price-container, .delivery-section, .tab-container {
  animation: fadeIn 0.5s ease-out;
}

.sku-confirm-btn:active, .submit-btn:active {
  animation: pulse 0.3s ease-in-out;
}

/* 响应式设计 - 适配不同屏幕尺寸 */
@media screen and (min-width: 768px) {
  .product-swiper {
    height: 600rpx;
  }

  .sku-panel, .address-popup, .order-confirm {
    max-width: 650rpx;
    left: 50%;
    transform: translateX(-50%) translateY(100%);
  }

  .sku-panel.show, .address-popup.show, .order-confirm.show {
    transform: translateX(-50%) translateY(0);
  }

  .footer {
    max-width: 750rpx;
    left: 50%;
    transform: translateX(-50%);
  }
}

/* 订单确认面板 */
.order-confirm-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 1000;
  backdrop-filter: blur(3px);
}

.order-confirm {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #fff;
  transform: translateY(100%);
  transition: transform 0.3s ease, opacity 0.3s ease;
  z-index: 1001;
  max-height: 90vh;
  overflow-y: auto;
  border-radius: 24rpx 24rpx 0 0;
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);
  opacity: 0;
}

.order-confirm.show {
  transform: translateY(0);
  opacity: 1;
}

.order-confirm-header {
  padding: 30rpx;
  position: relative;
  border-bottom: 1rpx solid #f5f5f5;
}

.order-confirm-close {
  position: absolute;
  right: 20rpx;
  top: 20rpx;
  font-size: 40rpx;
  color: #999;
  width: 60rpx;
  height: 60rpx;
  line-height: 60rpx;
  text-align: center;
  z-index: 10;
  border-radius: 50%;
  background-color: #f8f8f8;
}

.order-price-info {
  display: flex;
  flex-direction: column;
}

.stock-info {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 12rpx;
  background-color: #f8f8f8;
  display: inline-block;
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
  max-width: 60%;
}

.selected-info {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.safety-info {
  display: flex;
  align-items: center;
  padding: 24rpx 30rpx;
  background-color: #f9f9f9;
  border-bottom: 1rpx solid #f5f5f5;
}

.safety-tag {
  display: flex;
  align-items: center;
  margin-right: 12rpx;
  background-color: rgba(7, 193, 96, 0.1);
  padding: 6rpx 12rpx;
  border-radius: 8rpx;
}

.safety-tag text {
  font-size: 26rpx;
  color: #07c160;
  margin-left: 8rpx;
  font-weight: 500;
}

.safety-desc {
  flex: 1;
  font-size: 26rpx;
  color: #333;
}

.safety-arrow {
  font-size: 24rpx;
  color: #999;
  font-family: "Arial";
  font-weight: bold;
}

/* 配送方式选择 */
.delivery-type-section {
  padding: 30rpx;
  border-bottom: 1rpx solid #f5f5f5;
  margin-bottom: 10rpx;
}

.delivery-type-title {
  font-size: 30rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 20rpx;
}

.delivery-type-options {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
}

.delivery-type-option {
  display: flex;
  align-items: center;
  padding: 20rpx;
  width: 48%;
  border: 1rpx solid #f0f0f0;
  border-radius: 8rpx;
  box-sizing: border-box;
}

.delivery-type-radio {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  border: 2rpx solid #ddd;
  margin-right: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.radio-inner {
  width: 24rpx;
  height: 24rpx;
  border-radius: 50%;
  background-color: transparent;
}

.radio-inner.active {
  background-color: #ff4757;
}

.delivery-type-info {
  flex: 1;
}

.delivery-type-name {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 6rpx;
}

.delivery-type-desc {
  font-size: 24rpx;
  color: #999;
}

.delivery-type-option.selected {
  border-color: #ff4757;
  background-color: rgba(255, 71, 87, 0.05);
}

.delivery-type-option.selected .delivery-type-radio {
  border-color: #ff4757;
}

.shop-info {
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx 10rpx;
}

.shop-tag {
  font-size: 24rpx;
  color: #fff;
  background-color: #07c160;
  padding: 4rpx 10rpx;
  border-radius: 4rpx;
  margin-right: 10rpx;
}

.shop-name {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
  margin-right: 20rpx;
}

.shop-distance {
  flex: 1;
  font-size: 24rpx;
  color: #999;
}

.shop-arrow {
  font-size: 24rpx;
  color: #999;
}

.shop-address {
  font-size: 24rpx;
  color: #666;
  padding: 0 30rpx 20rpx;
  border-bottom: 1rpx solid #f5f5f5;
}

.order-section {
  display: flex;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f5f5f5;
}

.section-label {
  font-size: 28rpx;
  color: #333;
  width: 150rpx;
}

.section-content {
  flex: 1;
  font-size: 28rpx;
  color: #666;
}

.section-arrow {
  font-size: 24rpx;
  color: #999;
}

.order-product {
  display: flex;
  padding: 30rpx;
  border-bottom: 1rpx solid #f5f5f5;
}

.product-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 8rpx;
  margin-right: 20rpx;
}

.product-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.product-price {
  display: flex;
  align-items: baseline;
  margin-bottom: 10rpx;
}

.price-symbol {
  font-size: 24rpx;
  color: #333;
}

.product-stock {
  font-size: 24rpx;
  color: #999;
}

.order-size {
  padding: 30rpx;
  border-bottom: 1rpx solid #f5f5f5;
}

.size-label {
  font-size: 28rpx;
  color: #333;
  display: block;
  margin-bottom: 20rpx;
}

.size-options {
  display: flex;
  flex-wrap: wrap;
}

.size-option {
  width: 220rpx;
  height: 80rpx;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  font-size: 26rpx;
  color: #333;
  margin-right: 20rpx;
  margin-bottom: 20rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  position: relative;
  overflow: hidden;
}

.size-option.selected {
  border-color: #333;
}

.size-option.selected::after {
  content: '';
  position: absolute;
  bottom: 0;
  right: 0;
  width: 0;
  height: 0;
  border-style: solid;
  border-width: 0 0 20rpx 20rpx;
  border-color: transparent transparent #333 transparent;
}

.size-option-name {
  font-size: 26rpx;
  color: #333;
}

.size-option-price {
  font-size: 24rpx;
  color: #999;
}

.order-quantity {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  border-bottom: 1rpx solid #f5f5f5;
}

.quantity-label {
  font-size: 28rpx;
  color: #333;
}

.quantity-selector {
  display: flex;
  align-items: center;
}

.quantity-btn {
  width: 60rpx;
  height: 60rpx;
  border: 1rpx solid #ddd;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 28rpx;
}

.quantity-btn.disabled {
  color: #ddd;
}

.quantity-input {
  width: 80rpx;
  height: 60rpx;
  border-top: 1rpx solid #ddd;
  border-bottom: 1rpx solid #ddd;
  text-align: center;
  font-size: 28rpx;
}

.payment-method {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  border-bottom: 1rpx solid #f5f5f5;
}

.payment-label {
  font-size: 28rpx;
  color: #333;
}

.payment-option {
  display: flex;
  align-items: center;
}

.payment-option text {
  font-size: 28rpx;
  color: #333;
  margin-left: 10rpx;
}

.order-remark {
  display: flex;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f5f5f5;
}

.remark-label {
  font-size: 28rpx;
  color: #333;
  width: 150rpx;
}

.remark-content {
  flex: 1;
  font-size: 28rpx;
  color: #999;
}

.remark-arrow {
  font-size: 24rpx;
  color: #999;
}

.order-total {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  border-bottom: 1rpx solid #f5f5f5;
}

.total-label {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
}

.order-submit {
  padding: 30rpx;
}

.submit-btn {
  width: 100%;
  height: 90rpx;
  line-height: 90rpx;
  background: linear-gradient(to right, #ff6b6b, #ff4757);
  color: #fff;
  font-size: 32rpx;
  font-weight: bold;
  border-radius: 45rpx;
  border: none;
  letter-spacing: 2rpx;
  box-shadow: 0 4rpx 12rpx rgba(255, 71, 87, 0.3);
  transition: all 0.2s ease;
  display: flex;
  justify-content: center;
  align-items: center;
}

.submit-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 6rpx rgba(255, 71, 87, 0.2);
}

/* 提货人选择弹窗 */
.receiver-popup-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 1002;
  backdrop-filter: blur(3px);
}

.receiver-popup {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #fff;
  border-radius: 24rpx 24rpx 0 0;
  transform: translateY(100%);
  transition: transform 0.3s ease, opacity 0.3s ease;
  z-index: 1003;
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);
  opacity: 0;
}

.receiver-popup.show {
  transform: translateY(0);
  opacity: 1;
}

.receiver-popup-header {
  padding: 30rpx;
  text-align: center;
  position: relative;
  border-bottom: 1rpx solid #f5f5f5;
}

.receiver-popup-title {
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
}

.receiver-popup-close {
  position: absolute;
  right: 20rpx;
  top: 20rpx;
  font-size: 40rpx;
  color: #999;
  width: 60rpx;
  height: 60rpx;
  line-height: 60rpx;
  text-align: center;
  border-radius: 50%;
  background-color: #f8f8f8;
}

.receiver-form {
  padding: 30rpx;
}

.form-item {
  margin-bottom: 30rpx;
}

.form-label {
  font-size: 28rpx;
  color: #333;
  display: block;
  margin-bottom: 10rpx;
}

.form-input {
  width: 100%;
  height: 80rpx;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  box-sizing: border-box;
}

.receiver-confirm-btn {
  margin: 0 30rpx 30rpx;
  height: 90rpx;
  line-height: 90rpx;
  background: linear-gradient(to right, #ff6b6b, #ff4757);
  color: #fff;
  font-size: 32rpx;
  font-weight: bold;
  border-radius: 45rpx;
  border: none;
  letter-spacing: 2rpx;
  box-shadow: 0 4rpx 12rpx rgba(255, 71, 87, 0.3);
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.receiver-confirm-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 6rpx rgba(255, 71, 87, 0.2);
}

/* 提货时间选择弹窗 */
.time-popup-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 1002;
  backdrop-filter: blur(3px);
}

.time-popup {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #fff;
  border-radius: 24rpx 24rpx 0 0;
  transform: translateY(100%);
  transition: transform 0.3s ease, opacity 0.3s ease;
  z-index: 1003;
  max-height: 70vh;
  overflow-y: auto;
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);
  opacity: 0;
}

.time-popup.show {
  transform: translateY(0);
  opacity: 1;
}

.time-popup-header {
  padding: 30rpx;
  text-align: center;
  position: relative;
  border-bottom: 1rpx solid #f5f5f5;
}

.time-popup-title {
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
}

.time-popup-close {
  position: absolute;
  right: 30rpx;
  top: 30rpx;
  font-size: 40rpx;
  color: #999;
  width: 60rpx;
  height: 60rpx;
  line-height: 60rpx;
  text-align: center;
}

.time-list {
  padding: 0 30rpx;
}

.time-item {
  padding: 30rpx 20rpx;
  border-bottom: 1rpx solid #f5f5f5;
  font-size: 30rpx;
  color: #333;
  font-weight: 500;
  border-radius: 12rpx;
  margin-bottom: 10rpx;
  transition: all 0.2s ease;
}

.time-item:active {
  background-color: #fff8f8;
  color: #ff4757;
}

/* 备注输入弹窗 */
.remark-popup-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 1002;
  backdrop-filter: blur(3px);
}

.remark-popup {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #fff;
  border-radius: 24rpx 24rpx 0 0;
  transform: translateY(100%);
  transition: transform 0.3s ease, opacity 0.3s ease;
  z-index: 1003;
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);
  opacity: 0;
}

.remark-popup.show {
  transform: translateY(0);
  opacity: 1;
}

.remark-popup-header {
  padding: 30rpx;
  text-align: center;
  position: relative;
  border-bottom: 1rpx solid #f5f5f5;
}

.remark-popup-title {
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
}

.remark-popup-close {
  position: absolute;
  right: 30rpx;
  top: 30rpx;
  font-size: 40rpx;
  color: #999;
  width: 60rpx;
  height: 60rpx;
  line-height: 60rpx;
  text-align: center;
}

.remark-form {
  padding: 30rpx;
}

.remark-textarea {
  width: 100%;
  height: 200rpx;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  padding: 20rpx;
  font-size: 28rpx;
  box-sizing: border-box;
}

.remark-confirm-btn {
  margin: 0 30rpx 30rpx;
  height: 90rpx;
  line-height: 90rpx;
  background: linear-gradient(to right, #ff6b6b, #ff4757);
  color: #fff;
  font-size: 32rpx;
  font-weight: bold;
  border-radius: 45rpx;
  border: none;
  letter-spacing: 2rpx;
  box-shadow: 0 4rpx 12rpx rgba(255, 71, 87, 0.3);
  transition: all 0.2s ease;
}

.remark-confirm-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 6rpx rgba(255, 71, 87, 0.2);
}
