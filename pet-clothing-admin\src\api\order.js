import request from '@/utils/request'

// 分页查询订单列表
export function getOrderList(params) {
  return request({
    url: '/admin/orders/page',
    method: 'get',
    params
  })
}

// 获取订单详情
export function getOrderDetail(orderId) {
  return request({
    url: `/admin/orders/${orderId}`,
    method: 'get'
  })
}

// 发货
export function shipOrder(data) {
  return request({
    url: '/admin/orders/ship',
    method: 'post',
    data
  })
}

// 取消订单
export function cancelOrder(id) {
  return request({
    url: `/admin/orders/${id}/cancel`,
    method: 'post'
  })
}

// 更新订单状态
export function updateOrderStatus(id, status) {
  return request({
    url: `/admin/orders/${id}/status`,
    method: 'put',
    data: { status }
  })
}

// 获取订单统计数据
export function getOrderStats() {
  return request({
    url: '/admin/orders/stats',
    method: 'get'
  })
}

// 导出订单
export function exportOrders(params) {
  return request({
    url: '/admin/orders/export',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

// 获取最新订单
export function getLatestOrders() {
  return request({
    url: '/admin/orders/latest',
    method: 'get'
  })
}

// 获取退款申请列表
export function getRefundRequests(params) {
  return request({
    url: '/admin/refund-requests/page',
    method: 'get',
    params
  })
}

// 审核退款申请
export function reviewRefundRequest(data) {
  return request({
    url: '/admin/refund-requests/review',
    method: 'post',
    data
  })
}
