// app.js
App({
  onLaunch: function () {
    // 展示本地存储能力
    const logs = wx.getStorageSync('logs') || []
    logs.unshift(Date.now())
    wx.setStorageSync('logs', logs)

    // 检查登录状态
    this.checkLoginStatus()
  },

  // 检查登录状态
  checkLoginStatus: function() {
    // 获取本地存储的token
    const token = wx.getStorageSync('token')

    if (token) {
      // 已登录，设置全局数据
      this.globalData.token = token
      this.globalData.userId = wx.getStorageSync('userId')
      this.globalData.userInfo = wx.getStorageSync('userInfo')
      this.globalData.isLoggedIn = true

      // 可以在这里验证token是否有效
      this.validateToken(token)
    } else {
      // 未登录
      this.globalData.isLoggedIn = false
    }
  },

  // 验证token是否有效
  validateToken: function(token) {
    // 这里可以添加验证token的逻辑
    // 例如，发送请求到后端验证token是否有效
    // 如果token无效，清除本地存储并设置未登录状态

    // 示例代码（实际使用时需要替换为真实的API地址）
    /*
    wx.request({
      url: this.globalData.baseUrl + '/users/check-token',
      method: 'GET',
      header: {
        'Authorization': 'Bearer ' + token
      },
      success: (res) => {
        if (!res.data.valid) {
          // token无效，清除登录信息
          this.clearLoginInfo()
        }
      },
      fail: () => {
        // 请求失败，保持当前登录状态
      }
    })
    */
  },

  // 清除登录信息
  clearLoginInfo: function() {
    wx.removeStorageSync('token')
    wx.removeStorageSync('userId')
    wx.removeStorageSync('userInfo')
    wx.removeStorageSync('openid')
    wx.removeStorageSync('isNewUser')

    this.globalData.token = null
    this.globalData.userId = null
    this.globalData.userInfo = null
    this.globalData.isLoggedIn = false
  },

  globalData: {
    userInfo: null,
    token: null,
    userId: null,
    isLoggedIn: false,
    baseUrl: 'https://www.cloudspuppy.cn' // API地址
  }
})
