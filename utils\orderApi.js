// utils/orderApi.js
const request = require('./request.js');

/**
 * 订单相关API
 */
const orderApi = {
  /**
   * 获取订单列表
   * @param {Object} params - 查询参数
   * @param {string} params.status - 订单状态 (可选)
   * @returns {Promise} 返回Promise对象
   */
  getOrderList: function(params = {}) {
    return new Promise((resolve, reject) => {
      console.log('OrderAPI - 获取订单列表，参数:', JSON.stringify(params));

      request.get('/user/orders/list', params, {
        success: (res) => {
          if (res.data.code === 0 || res.data.code === 1) {
            const orders = res.data.data || [];
            console.log('OrderAPI - 成功获取', orders.length, '条订单');
            resolve(orders);
          } else {
            reject(new Error(res.data.msg || '获取订单列表失败'));
          }
        },
        fail: (err) => {
          console.error('OrderAPI - 请求失败:', err);
          reject(err);
        }
      });
    });
  },

  /**
   * 获取订单详情
   * @param {string|number} orderId - 订单ID
   * @returns {Promise} 返回Promise对象
   */
  getOrderDetail: function(orderId) {
    return new Promise((resolve, reject) => {
      if (!orderId) {
        reject(new Error('订单ID不能为空'));
        return;
      }

      console.log('OrderAPI - 获取订单详情，订单ID:', orderId);

      request.get(`/user/orders/detail/${orderId}`, {}, {
        success: (res) => {
          console.log('OrderAPI - 订单详情响应:', {
            statusCode: res.statusCode,
            code: res.data?.code,
            hasData: !!res.data?.data,
            msg: res.data?.msg
          });

          if (res.data.code === 0 || res.data.code === 1) {
            console.log('OrderAPI - 成功获取订单详情');
            resolve(res.data.data);
          } else {
            reject(new Error(res.data.msg || '获取订单详情失败'));
          }
        },
        fail: (err) => {
          console.error('OrderAPI - 获取订单详情失败:', err);
          reject(err);
        }
      });
    });
  },

  /**
   * 取消订单
   * @param {string|number} orderId - 订单ID
   * @returns {Promise} 返回Promise对象
   */
  cancelOrder: function(orderId) {
    return new Promise((resolve, reject) => {
      if (!orderId) {
        reject(new Error('订单ID不能为空'));
        return;
      }

      request.post(`/user/orders/cancel/${orderId}`, {}, {
        success: (res) => {
          if (res.data.code === 0 || res.data.code === 1) {
            resolve(res.data.data);
          } else {
            reject(new Error(res.data.msg || '取消订单失败'));
          }
        },
        fail: (err) => {
          reject(err);
        }
      });
    });
  },

  /**
   * 确认收货
   * @param {string|number} orderId - 订单ID
   * @returns {Promise} 返回Promise对象
   */
  confirmReceive: function(orderId) {
    return new Promise((resolve, reject) => {
      if (!orderId) {
        reject(new Error('订单ID不能为空'));
        return;
      }

      request.post(`/user/orders/confirm/${orderId}`, {}, {
        success: (res) => {
          if (res.data.code === 0 || res.data.code === 1) {
            resolve(res.data.data);
          } else {
            reject(new Error(res.data.msg || '确认收货失败'));
          }
        },
        fail: (err) => {
          reject(err);
        }
      });
    });
  },

  /**
   * 申请退款
   * @param {Object} refundData - 退款申请数据
   * @param {string|number} refundData.orderId - 订单ID
   * @param {string} refundData.reason - 退款原因
   * @param {number} refundData.refundAmount - 申请退款金额
   * @returns {Promise} 返回Promise对象
   */
  requestRefund: function(refundData) {
    return new Promise((resolve, reject) => {
      if (!refundData || !refundData.orderId) {
        reject(new Error('订单ID不能为空'));
        return;
      }

      if (!refundData.reason) {
        reject(new Error('退款原因不能为空'));
        return;
      }

      if (!refundData.refundAmount || refundData.refundAmount <= 0) {
        reject(new Error('退款金额必须大于0'));
        return;
      }

      console.log('OrderAPI - 申请退款，参数:', JSON.stringify(refundData));

      request.post('/user/refund-requests/create', refundData, {
        success: (res) => {
          console.log('OrderAPI - 退款申请响应:', {
            statusCode: res.statusCode,
            code: res.data?.code,
            hasData: !!res.data?.data,
            msg: res.data?.msg
          });

          if (res.data.code === 0 || res.data.code === 1) {
            console.log('OrderAPI - 退款申请成功');
            resolve(res.data.data);
          } else {
            reject(new Error(res.data.msg || '申请退款失败'));
          }
        },
        fail: (err) => {
          console.error('OrderAPI - 退款申请失败:', err);
          reject(err);
        }
      });
    });
  },

  /**
   * 获取订单状态统计
   * @returns {Promise} 返回Promise对象
   */
  getOrderStats: function() {
    return new Promise((resolve, reject) => {
      request.get('/user/orders/stats', {}, {
        success: (res) => {
          if (res.data.code === 0 || res.data.code === 1) {
            resolve(res.data.data || {});
          } else {
            reject(new Error(res.data.msg || '获取订单统计失败'));
          }
        },
        fail: (err) => {
          reject(err);
        }
      });
    });
  }
};

module.exports = orderApi;
