<!--pages/cart/cart.wxml-->
<view class="container">
  <!-- 加载中 -->
  <view class="loading-container" wx:if="{{loading}}">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 购物车为空 -->
  <view class="empty-cart" wx:elif="{{cartList.length === 0}}">
    <image src="/images/empty-cart.png" mode="aspectFit" class="empty-cart-image" />
    <text class="empty-cart-text">购物车暂时没有商品</text>
    <text class="empty-cart-tip">您可以添加喜欢的商品到购物车</text>
    <navigator url="/pages/index/index" open-type="switchTab" class="empty-cart-btn">前往选购</navigator>
    <view class="recommend-section">
      <text class="recommend-title">更多个性化推荐</text>
      <view class="recommend-list">
        <view class="recommend-item" wx:for="{{recommendList}}" wx:key="id" bindtap="addToCart" data-id="{{item.id}}">
          <image src="{{item.imageUrl}}" mode="aspectFill" class="recommend-image" />
          <text class="recommend-name">{{item.name}}</text>
          <text wx:if="{{item.tip}}" class="recommend-tip">{{item.tip}}</text>
          <text class="recommend-price">¥{{item.price}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 购物车有商品 -->
  <block wx:else>
    <!-- 购物车列表 -->
    <view class="cart-list">
      <view class="cart-item" wx:for="{{cartList}}" wx:key="id">
        <!-- 选择框 -->
        <view class="item-select" bindtap="selectItem" data-index="{{index}}">
          <icon type="{{item.selected ? 'success' : 'circle'}}" size="20" color="{{item.selected ? '#8ab6d6' : '#999'}}" />
        </view>

        <!-- 商品图片 -->
        <image src="{{item.imageUrl}}" mode="aspectFill" class="item-image" />

        <!-- 商品信息 -->
        <view class="item-info">
          <text class="item-name">{{item.name}}</text>
          <view class="item-sku" wx:if="{{item.attributes}}">
            <text wx:for="{{item.attributes}}" wx:key="key" class="sku-attr">
              {{item.key}}:{{item.value}}
            </text>
          </view>
          <view class="item-bottom">
            <text class="item-price">¥{{item.price}}</text>

            <!-- 数量控制 -->
            <view class="item-count">
              <view class="count-btn minus" bindtap="minusCount" data-index="{{index}}">-</view>
              <view class="count-number">{{item.count}}</view>
              <view class="count-btn plus" bindtap="addCount" data-index="{{index}}">+</view>
            </view>
          </view>
        </view>

        <!-- 删除按钮 -->
        <view class="item-delete" bindtap="deleteItem" data-index="{{index}}">
          <icon type="clear" size="20" color="#999" />
        </view>
      </view>
    </view>

    <!-- 底部结算栏 -->
    <view class="cart-footer">
      <view class="select-all" bindtap="selectAll">
        <icon type="{{allSelected ? 'success' : 'circle'}}" size="20" color="{{allSelected ? '#8ab6d6' : '#999'}}" />
        <text>全选</text>
      </view>

      <view class="total-info">
        <text class="total-price-text">合计：</text>
        <text class="total-price">¥{{totalPrice}}</text>
      </view>

      <view class="checkout-btn" bindtap="goToCheckout">
        <text>结算({{totalCount}})</text>
      </view>
    </view>
  </block>
</view>