<template>
  <div class="user-list-container">
    <!-- 搜索和操作区域 -->
    <el-card class="search-card" shadow="never">
      <div class="search-operations">
        <el-form :inline="true" :model="searchForm" class="search-form">
          <el-form-item label="用户名">
            <el-input v-model="searchForm.username" placeholder="请输入用户名" clearable />
          </el-form-item>
          <el-form-item label="手机号">
            <el-input v-model="searchForm.phone" placeholder="请输入手机号" clearable />
          </el-form-item>
          <el-form-item label="注册时间">
            <el-date-picker
              v-model="searchForm.dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="YYYY-MM-DD"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">搜索</el-button>
            <el-button @click="resetSearch">重置</el-button>
          </el-form-item>
        </el-form>
        <div class="operation-buttons">
          <el-button type="primary" @click="handleExport">导出用户</el-button>
        </div>
      </div>
    </el-card>

    <!-- 用户列表 -->
    <el-card class="user-table-card" shadow="never">
      <el-table
        v-loading="loading"
        :data="userList"
        border
        style="width: 100%"
      >
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column label="头像" width="100">
          <template #default="scope">
            <el-avatar :src="scope.row.avatar" :size="40">
              {{ scope.row.name ? scope.row.name.substring(0, 1).toUpperCase() : '-' }}
            </el-avatar>
          </template>
        </el-table-column>
        <el-table-column prop="name" label="用户名/昵称" width="150" />
        <el-table-column prop="phone" label="手机号" width="120" />
        <!-- 隐藏余额、积分、会员等级列 -->
        <!-- <el-table-column prop="balance" label="余额" width="100" /> -->
        <!-- <el-table-column prop="points" label="积分" width="100" /> -->
        <el-table-column prop="gender" label="性别" width="80" />
        <el-table-column prop="birthday" label="生日" width="120" />
        <el-table-column prop="region" label="地区" width="150" show-overflow-tooltip />
         <!-- <el-table-column prop="membershipLevel" label="会员等级" width="100" /> -->
        <!-- 状态列暂时保留，假设后端会返回status字段或需要其他映射 -->
        <!-- <el-table-column prop="status" label="状态" width="100"> -->
          <!-- <template #default="scope"> -->
            <!-- 假设status字段存在且为1/0 -->
            <!-- <el-switch -->
              <!-- v-model="scope.row.status" -->
              <!-- :active-value="1" -->
              <!-- :inactive-value="0" -->
              <!-- @change="handleStatusChange(scope.row)" -->
            <!-- /> -->
          <!-- </template> -->
        <!-- </el-table-column> -->
        <el-table-column prop="createdAt" label="注册时间" width="180" />

        <el-table-column label="操作" width="150" fixed="right">
          <template #default="scope">
            <el-button type="primary" link @click="handleDetail(scope.row)">详情</el-button>
            <el-button type="danger" link @click="handleDelete(scope.row)">删除</el-button>
            
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getUserList, updateUserStatus, deleteUser } from '@/api/user'

const router = useRouter()
const loading = ref(false)
const userList = ref([])
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(10)

// 搜索表单
const searchForm = reactive({
  username: '',
  phone: '',
  dateRange: []
})

// 获取用户列表
const fetchUserList = async () => {
  loading.value = true
  try {
    const params = {
      page: currentPage.value,
      pageSize: pageSize.value,
      username: searchForm.username,
      phone: searchForm.phone,
      startTime: searchForm.dateRange && searchForm.dateRange.length > 0 ? searchForm.dateRange[0] : undefined,
      endTime: searchForm.dateRange && searchForm.dateRange.length > 1 ? searchForm.dateRange[1] : undefined
    }

    const res = await getUserList(params)

    if (res.code === 1) { // 假设code 1 表示成功
      // 映射数据字段以匹配表格列
      userList.value = res.data.records.map(user => ({
          ...user,
          // 如果后端返回的gender是字符串，直接使用
          // 如果需要将enableRecommendation映射到status，可以在这里处理
          // status: user.enableRecommendation ? 1 : 0
      })) || []
      total.value = res.data.total || 0
    } else {
       ElMessage.error(res.msg || '获取用户列表失败')
       // 使用模拟数据
       userList.value = [
         {
           id: 1,
           name: '张三',
           avatar: 'https://picsum.photos/40/40?random=1',
           phone: '13800138001',
           gender: '男',
           birthday: '1990-01-01',
           region: '北京市朝阳区',
           createdAt: '2023-01-15 10:30:45'
         },
         {
           id: 2,
           name: '李四',
           avatar: 'https://picsum.photos/40/40?random=2',
           phone: '13800138002',
           gender: '女',
           birthday: '1992-05-15',
           region: '上海市浦东新区',
           createdAt: '2023-02-20 14:25:12'
         }
       ]
       total.value = 2
    }
  } catch (error) {
    console.error('获取用户列表失败:', error)
    ElMessage.error('获取用户列表失败')
    // 使用模拟数据作为备用
    userList.value = [
      {
        id: 1,
        name: '张三',
        avatar: 'https://picsum.photos/40/40?random=1',
        phone: '13800138001',
        gender: '男',
        birthday: '1990-01-01',
        region: '北京市朝阳区',
        createdAt: '2023-01-15 10:30:45'
      },
      {
        id: 2,
        name: '李四',
        avatar: 'https://picsum.photos/40/40?random=2',
        phone: '13800138002',
        gender: '女',
        birthday: '1992-05-15',
        region: '上海市浦东新区',
        createdAt: '2023-02-20 14:25:12'
      }
    ]
    total.value = 2
  } finally {
    loading.value = false
  }
}

// 获取性别文本 (不再需要，因为后端直接返回字符串)
// const getGenderText = (gender) => {
//   switch (gender) {
//     case 1:
//       return '男'
//     case 2:
//       return '女'
//     default:
//       return '未知'
//   }
// }

// 搜索
const handleSearch = () => {
  currentPage.value = 1
  fetchUserList()
}

// 重置搜索
const resetSearch = () => {
  Object.keys(searchForm).forEach(key => {
    searchForm[key] = key === 'dateRange' ? [] : ''
  })
  currentPage.value = 1
  fetchUserList()
}

// 导出用户
const handleExport = () => {
  ElMessage.success('用户数据导出成功')
}

// 查看用户详情
const handleDetail = (row) => {
  // TODO: 实现用户详情页面的跳转或弹窗
   console.log('查看用户详情:', row)
  router.push(`/user/detail/${row.id}`)
}

// 删除用户
const handleDelete = (row) => {
  ElMessageBox.confirm('确定要删除该用户吗？删除后将无法恢复！', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      // TODO: 调用API删除用户
      console.log('删除用户:', row.id)
      const res = await deleteUser(row.id)
      if(res.code === 1) { // 假设code 1 表示成功
            ElMessage.success('删除成功')
            fetchUserList() // 删除成功后刷新列表
      } else {
           ElMessage.error(res.msg || '删除用户失败')
      }
    } catch (error) {
      console.error('删除用户失败:', error)
      ElMessage.error('删除用户失败')
    }
  }).catch(() => {})
}

// 处理编辑用户
const handleEdit = (row) => {
    router.push(`/user/edit/${row.id}`)
}

// 页码变化
const handleCurrentChange = (page) => {
  currentPage.value = page
  fetchUserList()
}

// 每页条数变化
const handleSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1
  fetchUserList()
}

onMounted(() => {
  fetchUserList()
})
</script>

<style scoped>
.user-list-container {
  padding: 20px;
}

.search-card {
  margin-bottom: 20px;
}

.search-operations {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
}

.search-form {
  flex: 1;
  min-width: 600px;
}

.operation-buttons {
  display: flex;
  align-items: flex-start;
  margin-left: 20px;
}

.user-table-card {
  margin-bottom: 20px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}
</style>
