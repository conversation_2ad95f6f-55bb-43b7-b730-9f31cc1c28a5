/* pages/address/address.wxss */
.container {
  padding: 0;
  background-color: #f8f8f8;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* 地址列表 */
.address-list {
  width: 100%;
  flex: 1;
  padding: 20rpx 0;
}

.address-item {
  background-color: #fff;
  margin: 20rpx 30rpx;
  border-radius: 12rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  position: relative;
}

.address-item.selected {
  border: 2rpx solid #ff4c6a;
  background-color: #fff9fa;
}

.address-header {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.consignee {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-right: 20rpx;
}

.phone {
  font-size: 28rpx;
  color: #666;
}

.default-tag {
  font-size: 22rpx;
  color: #ff4c6a;
  background-color: #fff0f2;
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
  margin-left: 20rpx;
}

.address-detail {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
}

/* 地址操作按钮 */
.address-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 20rpx;
  padding-top: 20rpx;
  border-top: 1rpx solid #f0f0f0;
}

.action-item {
  display: flex;
  align-items: center;
  margin-left: 30rpx;
  font-size: 24rpx;
  color: #666;
}

.action-item.disabled {
  color: #999;
}

.action-icon {
  width: 28rpx;
  height: 28rpx;
  margin-right: 6rpx;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
  flex: 1;
}

.empty-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

/* 底部按钮 */
.footer {
  padding: 30rpx;
  background-color: #fff;
  border-top: 1rpx solid #f0f0f0;
  position: sticky;
  bottom: 0;
}

.add-btn {
  background-color: #8ab6d6;
  color: #fff;
  font-size: 32rpx;
  height: 88rpx;
  line-height: 88rpx;
  border-radius: 44rpx;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}
