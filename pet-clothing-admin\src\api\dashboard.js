import request from '@/utils/request'

// 获取仪表盘统计数据
export function getDashboardStats() {
  return request({
    url: '/admin/dashboard/stats',
    method: 'get'
  })
}

// 获取订单统计数据（基于订单分页API）
export function getOrderStats(params) {
  return request({
    url: '/admin/orders/page',
    method: 'get',
    params: {
      ...params,
      pageSize: 1000 // 设置较大值以获取完整统计数据
    }
  })
}

// 获取销售额统计数据（基于订单分页API）
export function getSalesStats(params) {
  return request({
    url: '/admin/orders/page',
    method: 'get',
    params: {
      ...params,
      pageSize: 1000, // 设置较大值以获取完整数据
      status: '已完成' // 只统计已完成的订单
    }
  })
}

// 获取最新订单列表
export function getLatestOrders(limit = 10) {
  return request({
    url: '/admin/orders/latest',
    method: 'get',
    params: { limit }
  })
}

// 获取总交易完成金额
export function getTotalCompletedAmount() {
  return request({
    url: '/admin/orders/total-completed-amount',
    method: 'get'
  })
}

// 获取总订单数
export function getTotalOrdersCount() {
  return request({
    url: '/admin/orders/total-orders-count',
    method: 'get'
  })
}

// 获取总用户数
export function getTotalUsersCount() {
  return request({
    url: '/admin/orders/total-users-count',
    method: 'get'
  })
}