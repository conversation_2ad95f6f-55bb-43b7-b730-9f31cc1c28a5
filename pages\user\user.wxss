/* pages/user/user.wxss */
.container {
  min-height: 100vh;
  background-color: #f8f8f8;
  padding-bottom: 30rpx;
}

/* 用户信息样式 */
.user-info-container {
  background-color: #fff;
  padding: 0 30rpx 30rpx;
}

/* 未登录状态 */
.login-section {
  display: flex;
  justify-content: center; /* 水平居中 */
  align-items: center;
  padding: 20rpx 0;
}

.login-tip {
  font-size: 28rpx;
  color: #8ab6d6;
  background-color: #fff;
}

/* 已登录状态 */
.user-profile {
  display: flex;
  align-items: center;
  padding: 30rpx 0;
  border-bottom: 1rpx solid #eee;
}

.avatar-container {
  margin-right: 20rpx;
}

.avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  background-color: #f5f5f5;
}

.user-details {
  flex: 1;
}

.username {
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 10rpx;
  display: block;
}

.user-level {
  display: flex;
  align-items: center;
}

.level-tag {
  font-size: 22rpx;
  color: #ff6b6b;
  border: 1rpx solid #ff6b6b;
  padding: 2rpx 10rpx;
  border-radius: 20rpx;
}

.settings-btn {
  font-size: 26rpx;
  color: #999;
  padding: 10rpx 20rpx;
  border: 1rpx solid #ddd;
  border-radius: 30rpx;
}

/* 会员注册栏 */
.member-register-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #3a4268;
  color: #fff;
  padding: 20rpx 30rpx;
  border-radius: 10rpx;
  margin: 20rpx 0;
}

.member-register-text {
  font-size: 28rpx;
}

.register-btn {
  font-size: 26rpx;
  color: #fff;
}

/* 登录提示栏 */
.login-tip-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #eee;
}

.login-tip-bar text {
  font-size: 26rpx;
  color: #666;
  flex: 1;
}

.login-btn {
  margin: 0;
  padding: 0 30rpx;
  height: 60rpx;
  line-height: 60rpx;
  font-size: 28rpx;
  color: #fff;
  background-color: #ff6b6b;
  border-radius: 30rpx;
}



/* 心情语录 */
.mood-quote {
  padding: 30rpx 0 10rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.quote-cn {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
}

.quote-en {
  font-size: 24rpx;
  color: #999;
}

/* 订单样式 */
.order-section {
  margin-top: 20rpx;
  background-color: #fff;
  padding: 0 0 20rpx;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
  border-bottom: 1rpx solid #eee;
}

.section-title {
  font-size: 30rpx;
  color: #333;
  font-weight: bold;
}

.view-all {
  font-size: 26rpx;
  color: #999;
}

.order-menu {
  display: flex;
  padding: 30rpx 0;
}

.order-menu-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.menu-icon-1 {
  width: 50rpx;
  height: 50rpx;
  margin-bottom: 10rpx;
}

.menu-icon {
  width: 50rpx;
  height: 50rpx;

}

.menu-text {
  font-size: 24rpx;
  color: #666;
  margin-bottom: -10rpx;
}

/* 功能菜单样式 */
.menu-container {
  margin-top: 20rpx;
  background-color: #fff;
}

.menu-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #eee;
}

.menu-left {
  display: flex;
  align-items: center;
}

.menu-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 20rpx;
}

.menu-name {
  font-size: 28rpx;
  color: #333;
}

.menu-right {
  font-size: 26rpx;
  color: #999;
}

.arrow {
  margin-left: 10rpx;
}

/* 设置菜单样式 */
.settings-container {
  margin-top: 20rpx;
  background-color: #fff;
}

.settings-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #eee;
}

/* 个性化推荐样式 */
.recommend-section {
  margin-top: 20rpx;
  padding-bottom: 30rpx;
}

.recommend-divider {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40rpx 0;
}

.recommend-divider::before,
.recommend-divider::after {
  content: '';
  position: absolute;
  top: 50%;
  width: 80rpx;
  height: 1rpx;
  background-color: #ddd;
}

.recommend-divider::before {
  left: 50%;
  margin-left: -150rpx;
}

.recommend-divider::after {
  right: 50%;
  margin-right: -150rpx;
}

.recommend-title {
  font-size: 28rpx;
  color: #666;
}

.recommend-products {
  display: flex;
  justify-content: space-between;
  padding: 0 20rpx;
}

.product-item {
  width: 48%;
  background-color: #fff;
  border-radius: 10rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.product-image {
  width: 100%;
  height: 340rpx;
  background-color: #f5f5f5;
}

.product-info {
  padding: 20rpx;
}

.product-name {
  font-size: 26rpx;
  color: #333;
  display: block;
  margin-bottom: 10rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.product-price {
  font-size: 28rpx;
  color: #ff6b6b;
  font-weight: bold;
}
