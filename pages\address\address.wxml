<!--pages/address/address.wxml-->
<view class="container">
  <!-- 地址列表 -->
  <view class="address-list" wx:if="{{addressList.length > 0}}">
    <view class="address-item {{selectedId === item.id ? 'selected' : ''}}" 
          wx:for="{{addressList}}" 
          wx:key="id"
          bindtap="{{isFromOrder ? 'selectAddress' : ''}}" 
          data-index="{{index}}" 
          data-id="{{item.id}}">
      
      <!-- 地址信息 -->
      <view class="address-info">
        <view class="address-header">
          <text class="consignee">{{item.consignee}}</text>
          <text class="phone">{{item.phone}}</text>
          <text class="default-tag" wx:if="{{item.isDefault}}">默认</text>
        </view>
        <view class="address-detail">{{item.address}}</view>
      </view>
      
      <!-- 操作按钮 -->
      <view class="address-actions">
        <view class="action-item" catchtap="editAddress" data-id="{{item.id}}">
          <image src="/images/icon-edit.png" class="action-icon" />
          <text>编辑</text>
        </view>
        <view class="action-item" catchtap="deleteAddress" data-id="{{item.id}}" data-index="{{index}}">
          <image src="/images/icon-delete.png" class="action-icon" />
          <text>删除</text>
        </view>
      </view>
    </view>
  </view>
  
  <!-- 空状态 -->
  <view class="empty-state" wx:else>
    <image src="/images/empty-address.png" mode="aspectFit" class="empty-image" />
    <text class="empty-text">您还没有添加收货地址</text>
  </view>
  
  <!-- 底部按钮 -->
  <view class="footer">
    <button class="add-btn" bindtap="addAddress">+ 新增收货地址</button>
  </view>
</view>
