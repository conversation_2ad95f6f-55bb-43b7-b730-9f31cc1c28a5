// utils/auth.js

// 检查是否登录
const checkLogin = () => {
  const token = wx.getStorageSync('token');
  return !!token;
};

// 跳转到登录页面
const navigateToLogin = (redirect) => {
  let url = '/pages/login/login';
  if (redirect) {
    url += `?redirect=${encodeURIComponent(redirect)}`;
  }
  wx.navigateTo({
    url: url
  });
};

// 获取用户信息
const getUserInfo = () => {
  return wx.getStorageSync('userInfo') || null;
};

// 登出
const logout = () => {
  wx.removeStorageSync('token');
  wx.removeStorageSync('userInfo');
  wx.showToast({
    title: '已退出登录',
    icon: 'success',
    duration: 1500,
    success: () => {
      setTimeout(() => {
        wx.reLaunch({
          url: '/pages/index/index'
        });
      }, 1500);
    }
  });
};

// 检查登录状态并跳转
const checkLoginStatus = (pageObj) => {
  const token = wx.getStorageSync('token');
  if (!token) {
    navigateToLogin(pageObj.route);
    return false;
  }
  return true;
};

module.exports = {
  checkLogin,
  navigateToLogin,
  getUserInfo,
  logout,
  checkLoginStatus
};
