/* pages/order/order.wxss */
.container {
  min-height: 100vh;
  background-color: #f8f8f8;
  padding-bottom: 30rpx;
}

/* 状态筛选栏 */
.status-filter {
  display: flex;
  background-color: #fff;
  border-bottom: 1rpx solid #eee;
  position: sticky;
  top: 0;
  z-index: 100;
}

.filter-item {
  flex: 1;
  text-align: center;
  padding: 30rpx 0;
  position: relative;
}

.filter-item text {
  font-size: 28rpx;
  color: #666;
}

.filter-item.active text {
  color: #ff6b6b;
  font-weight: bold;
}

.filter-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60rpx;
  height: 4rpx;
  background-color: #ff6b6b;
  border-radius: 2rpx;
}

/* 订单列表 */
.order-list {
  padding: 20rpx;
}

/* 加载状态 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 100rpx 0;
}

.loading-text {
  font-size: 28rpx;
  color: #999;
}

/* 空状态 */
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 100rpx 0;
}

.empty-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
  margin-bottom: 40rpx;
}

.go-shopping-btn {
  padding: 20rpx 40rpx;
  background-color: #ff6b6b;
  color: #fff;
  border-radius: 30rpx;
  font-size: 28rpx;
}

/* 订单项 */
.order-item {
  background-color: #fff;
  border-radius: 10rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

/* 订单头部 */
.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f5f5f5;
}

.order-info {
  flex: 1;
}

.order-number {
  font-size: 26rpx;
  color: #333;
  display: block;
  margin-bottom: 10rpx;
}

.order-time {
  font-size: 24rpx;
  color: #999;
}

.order-status {
  font-size: 26rpx;
  font-weight: bold;
}

.order-status.pending {
  color: #ff6b6b;
}

.order-status.cancelled {
  color: #999;
}

.order-status.normal {
  color: #52c41a;
}

/* 订单商品列表 */
.order-products {
  padding: 0 30rpx;
}

.product-item {
  display: flex;
  padding: 30rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
}

.product-item:last-child {
  border-bottom: none;
}

.product-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 8rpx;
  background-color: #f5f5f5;
  margin-right: 20rpx;
}

.product-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.product-name {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.product-specs {
  margin-bottom: 10rpx;
}

.spec-text {
  font-size: 24rpx;
  color: #999;
  background-color: #f5f5f5;
  padding: 4rpx 12rpx;
  border-radius: 4rpx;
}

.product-price-qty {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.product-price {
  font-size: 28rpx;
  color: #ff6b6b;
  font-weight: bold;
}

.product-quantity {
  font-size: 24rpx;
  color: #999;
}

/* 订单金额 */
.order-amount {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding: 20rpx 30rpx;
  border-bottom: 1rpx solid #f5f5f5;
}

.amount-label {
  font-size: 26rpx;
  color: #666;
  margin-right: 10rpx;
}

.amount-value {
  font-size: 30rpx;
  color: #ff6b6b;
  font-weight: bold;
}

/* 收货信息 */
.shipping-info {
  padding: 20rpx 30rpx;
  border-bottom: 1rpx solid #f5f5f5;
}

.shipping-label {
  font-size: 26rpx;
  color: #666;
  margin-right: 10rpx;
}

.shipping-details {
  font-size: 26rpx;
  color: #333;
}

/* 支付方式 */
.payment-info {
  padding: 20rpx 30rpx;
  border-bottom: 1rpx solid #f5f5f5;
}

.payment-label {
  font-size: 26rpx;
  color: #666;
  margin-right: 10rpx;
}

.payment-method {
  font-size: 26rpx;
  color: #333;
}

/* 操作按钮 */
.order-actions {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding: 30rpx;
  gap: 20rpx;
}

.action-btn {
  padding: 16rpx 32rpx;
  border-radius: 30rpx;
  font-size: 26rpx;
  border: 1rpx solid #ddd;
  background-color: #fff;
  color: #666;
  margin: 0;
}

.action-btn.primary-btn {
  background-color: #ff6b6b;
  color: #fff;
  border-color: #ff6b6b;
}

.action-btn.secondary-btn {
  background-color: #52c41a;
  color: #fff;
  border-color: #52c41a;
}

.action-btn.cancel-btn {
  color: #999;
  border-color: #ddd;
}

.action-btn.detail-btn {
  color: #666;
  border-color: #ddd;
}
