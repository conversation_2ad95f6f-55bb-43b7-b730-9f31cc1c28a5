/* 全局样式 */

/* 清除浮动 */
.clearfix:after {
  content: "";
  display: table;
  clear: both;
}

/* 文本溢出省略号 */
.ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 多行文本溢出省略号 */
.multi-ellipsis-2 {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

/* 页面容器 */
.page-container {
  padding: 20px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
}

/* 操作按钮容器 */
.operation-container {
  margin-bottom: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* 表格操作按钮 */
.table-operation {
  margin-right: 10px;
}

/* 分页容器 */
.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

/* 表单容器 */
.form-container {
  max-width: 800px;
  margin: 0 auto;
}

/* 表单底部按钮 */
.form-footer {
  margin-top: 30px;
  text-align: center;
}

/* 卡片容器 */
.card-container {
  margin-bottom: 20px;
}

/* 详情页标题 */
.detail-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #ebeef5;
}

/* 详情项 */
.detail-item {
  margin-bottom: 15px;
}

.detail-item-label {
  color: #606266;
  margin-right: 10px;
}

/* 状态标签 */
.status-tag {
  padding: 2px 8px;
  border-radius: 2px;
  font-size: 12px;
}

.status-tag-success {
  background-color: #f0f9eb;
  color: #67c23a;
  border: 1px solid #e1f3d8;
}

.status-tag-warning {
  background-color: #fdf6ec;
  color: #e6a23c;
  border: 1px solid #faecd8;
}

.status-tag-danger {
  background-color: #fef0f0;
  color: #f56c6c;
  border: 1px solid #fde2e2;
}

.status-tag-info {
  background-color: #f4f4f5;
  color: #909399;
  border: 1px solid #e9e9eb;
}
