// pages/index/index.js
const productApi = require('../../utils/productApi.js');

Page({
  data: {
    bannerList: [],
    categoryList: [
      {
        id: 1,
        name: '服饰',
        icon: '/images/category-clothes.png'
      },
      {
        id: 2,
        name: '发饰',
        icon: '/images/category-accessories.png'
      },
      {
        id: 3,
        name: '套装',
        icon: '/images/category-suits.png'
      },
      {
        id: 4,
        name: '外出用品',
        icon: '/images/category-outdoor.png'
      },
      {
        id: 5,
        name: '福利',
        icon: '/images/category-sales.png'
      }
    ],
    hotProductList: []
  },

  onLoad: function (options) {
    // 页面加载时执行
    this.getHomeData();
    this.getBannerList();
  },

  // 获取轮播图数据
  getBannerList: function() {
    const app = getApp();
    wx.request({
      url: app.globalData.baseUrl + '/user/banners/list',
      method: 'GET',
      success: (res) => {
        if (res.data.code === 1 && res.data.data) {
          this.setData({
            bannerList: res.data.data
          });
        }
      },
      fail: (err) => {
        console.error('获取轮播图失败:', err);
        wx.showToast({
          title: '获取轮播图失败',
          icon: 'none'
        });
      }
    });
  },

  // 获取首页数据
  getHomeData: function() {
    // 显示加载中
    wx.showLoading({
      title: '加载中...',
    });

    // 获取最新的10个商品作为热门商品
    productApi.getRecommendProducts(10)
      .then(products => {
        if (products && Array.isArray(products)) {
          // 处理商品数据
          const formattedProducts = products.map(item => ({
            id: item.id,
            name: item.name,
            price: item.currentPrice || item.price,
            originalPrice: item.originalPrice,
            imageUrl: item.imageUrl || (item.images && item.images.length > 0 ? item.images[0] : '/images/product-default.jpg'),
            sales: item.salesCount || 0
          }));

          // 更新热门商品列表
          this.setData({
            hotProductList: formattedProducts
          });
        }
      })
      .catch(err => {
        console.error('获取热门商品失败:', err);
        wx.showToast({
          title: '获取商品失败',
          icon: 'none'
        });
      })
      .finally(() => {
        wx.hideLoading();
      });
  },

  // 跳转到商品详情页
  navigateToDetail: function(e) {
    const id = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: '/pages/product/product?id=' + id
    })
  },

  // 跳转到分类页
  navigateToCategory: function(e) {
    const id = e.currentTarget.dataset.id;
    wx.switchTab({
      url: '/pages/category/category?id=' + id
    })
  },

  // 搜索功能
  onSearch: function(e) {
    const keyword = e.detail.value;
    wx.navigateTo({
      url: '/pages/search/search?keyword=' + keyword
    })
  }
})
