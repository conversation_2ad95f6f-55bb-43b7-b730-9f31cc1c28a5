<template>
  <div class="settings-container">
    <el-card class="settings-card" shadow="never">
      <template #header>
        <div class="card-header">
          <span>系统设置</span>
        </div>
      </template>
      
      <el-tabs v-model="activeTab">
        <!-- 基本设置 -->
        <el-tab-pane label="基本设置" name="basic">
          <el-form
            ref="basicFormRef"
            :model="basicForm"
            :rules="basicRules"
            label-width="120px"
            label-position="right"
          >
            <el-form-item label="系统名称" prop="systemName">
              <el-input v-model="basicForm.systemName" placeholder="请输入系统名称" />
            </el-form-item>
            
            <el-form-item label="系统Logo">
              <el-upload
                class="logo-uploader"
                action="/api/admin/upload/image"
                :headers="uploadHeaders"
                :show-file-list="false"
                :on-success="handleLogoSuccess"
                :before-upload="beforeLogoUpload"
              >
                <img v-if="basicForm.logo" :src="basicForm.logo" class="logo-image" />
                <el-icon v-else class="logo-uploader-icon"><Plus /></el-icon>
              </el-upload>
            </el-form-item>
            
            <el-form-item label="系统描述" prop="description">
              <el-input
                v-model="basicForm.description"
                type="textarea"
                :rows="3"
                placeholder="请输入系统描述"
              />
            </el-form-item>
            
            <el-form-item label="联系电话" prop="contactPhone">
              <el-input v-model="basicForm.contactPhone" placeholder="请输入联系电话" />
            </el-form-item>
            
            <el-form-item label="联系邮箱" prop="contactEmail">
              <el-input v-model="basicForm.contactEmail" placeholder="请输入联系邮箱" />
            </el-form-item>
            
            <el-form-item label="版权信息" prop="copyright">
              <el-input v-model="basicForm.copyright" placeholder="请输入版权信息" />
            </el-form-item>
            
            <el-form-item>
              <el-button type="primary" :loading="submitting" @click="saveBasicSettings">保存设置</el-button>
            </el-form-item>
          </el-form>
        </el-tab-pane>
        
        <!-- 支付设置 -->
        <el-tab-pane label="支付设置" name="payment">
          <el-form
            ref="paymentFormRef"
            :model="paymentForm"
            label-width="120px"
            label-position="right"
          >
            <el-divider content-position="left">微信支付</el-divider>
            
            <el-form-item label="启用微信支付">
              <el-switch v-model="paymentForm.enableWechat" />
            </el-form-item>
            
            <el-form-item label="AppID" v-if="paymentForm.enableWechat">
              <el-input v-model="paymentForm.wechatAppId" placeholder="请输入微信AppID" />
            </el-form-item>
            
            <el-form-item label="商户号" v-if="paymentForm.enableWechat">
              <el-input v-model="paymentForm.wechatMchId" placeholder="请输入微信商户号" />
            </el-form-item>
            
            <el-form-item label="API密钥" v-if="paymentForm.enableWechat">
              <el-input v-model="paymentForm.wechatApiKey" placeholder="请输入微信API密钥" show-password />
            </el-form-item>
            
            <el-divider content-position="left">支付宝</el-divider>
            
            <el-form-item label="启用支付宝">
              <el-switch v-model="paymentForm.enableAlipay" />
            </el-form-item>
            
            <el-form-item label="AppID" v-if="paymentForm.enableAlipay">
              <el-input v-model="paymentForm.alipayAppId" placeholder="请输入支付宝AppID" />
            </el-form-item>
            
            <el-form-item label="私钥" v-if="paymentForm.enableAlipay">
              <el-input
                v-model="paymentForm.alipayPrivateKey"
                type="textarea"
                :rows="3"
                placeholder="请输入支付宝私钥"
              />
            </el-form-item>
            
            <el-form-item label="公钥" v-if="paymentForm.enableAlipay">
              <el-input
                v-model="paymentForm.alipayPublicKey"
                type="textarea"
                :rows="3"
                placeholder="请输入支付宝公钥"
              />
            </el-form-item>
            
            <el-form-item>
              <el-button type="primary" :loading="submitting" @click="savePaymentSettings">保存设置</el-button>
            </el-form-item>
          </el-form>
        </el-tab-pane>
        
        <!-- 物流设置 -->
        <el-tab-pane label="物流设置" name="logistics">
          <el-form
            ref="logisticsFormRef"
            :model="logisticsForm"
            label-width="120px"
            label-position="right"
          >
            <el-form-item label="启用物流查询">
              <el-switch v-model="logisticsForm.enableLogisticsQuery" />
            </el-form-item>
            
            <el-form-item label="物流接口AppKey" v-if="logisticsForm.enableLogisticsQuery">
              <el-input v-model="logisticsForm.logisticsAppKey" placeholder="请输入物流接口AppKey" />
            </el-form-item>
            
            <el-form-item label="物流接口AppSecret" v-if="logisticsForm.enableLogisticsQuery">
              <el-input v-model="logisticsForm.logisticsAppSecret" placeholder="请输入物流接口AppSecret" show-password />
            </el-form-item>
            
            <el-divider content-position="left">物流公司</el-divider>
            
            <div class="logistics-companies">
              <div v-for="(company, index) in logisticsForm.companies" :key="index" class="company-item">
                <el-input v-model="company.code" placeholder="物流公司代码" style="width: 150px" />
                <el-input v-model="company.name" placeholder="物流公司名称" style="width: 200px; margin: 0 10px" />
                <el-button type="danger" icon="Delete" circle @click="removeLogisticsCompany(index)" />
              </div>
              <el-button type="primary" icon="Plus" @click="addLogisticsCompany">添加物流公司</el-button>
            </div>
            
            <el-form-item style="margin-top: 20px">
              <el-button type="primary" :loading="submitting" @click="saveLogisticsSettings">保存设置</el-button>
            </el-form-item>
          </el-form>
        </el-tab-pane>
        
        <!-- 管理员设置 -->
        <el-tab-pane label="管理员设置" name="admin">
          <el-form
            ref="adminFormRef"
            :model="adminForm"
            :rules="adminRules"
            label-width="120px"
            label-position="right"
          >
            <el-form-item label="管理员账号" prop="username">
              <el-input v-model="adminForm.username" disabled />
            </el-form-item>
            
            <el-form-item label="管理员姓名" prop="name">
              <el-input v-model="adminForm.name" placeholder="请输入管理员姓名" />
            </el-form-item>
            
            <el-form-item label="联系电话" prop="phone">
              <el-input v-model="adminForm.phone" placeholder="请输入联系电话" />
            </el-form-item>
            
            <el-form-item label="联系邮箱" prop="email">
              <el-input v-model="adminForm.email" placeholder="请输入联系邮箱" />
            </el-form-item>
            
            <el-divider content-position="left">修改密码</el-divider>
            
            <el-form-item label="原密码" prop="oldPassword">
              <el-input v-model="adminForm.oldPassword" type="password" placeholder="请输入原密码" show-password />
            </el-form-item>
            
            <el-form-item label="新密码" prop="newPassword">
              <el-input v-model="adminForm.newPassword" type="password" placeholder="请输入新密码" show-password />
            </el-form-item>
            
            <el-form-item label="确认新密码" prop="confirmPassword">
              <el-input v-model="adminForm.confirmPassword" type="password" placeholder="请再次输入新密码" show-password />
            </el-form-item>
            
            <el-form-item>
              <el-button type="primary" :loading="submitting" @click="saveAdminSettings">保存设置</el-button>
            </el-form-item>
          </el-form>
        </el-tab-pane>
      </el-tabs>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Plus, Delete } from '@element-plus/icons-vue'

const activeTab = ref('basic')
const submitting = ref(false)
const basicFormRef = ref(null)
const paymentFormRef = ref(null)
const logisticsFormRef = ref(null)
const adminFormRef = ref(null)

// 上传图片的请求头
const uploadHeaders = computed(() => {
  return {
    Authorization: `Bearer ${localStorage.getItem('token')}`
  }
})

// 基本设置表单
const basicForm = reactive({
  systemName: '宠物服装商城管理系统',
  logo: '',
  description: '专业的宠物服装商城管理系统，提供全面的宠物服装销售解决方案。',
  contactPhone: '************',
  contactEmail: '<EMAIL>',
  copyright: '© 2023 宠物服装商城 All Rights Reserved.'
})

// 基本设置验证规则
const basicRules = {
  systemName: [
    { required: true, message: '请输入系统名称', trigger: 'blur' }
  ],
  contactPhone: [
    { pattern: /^1[3-9]\d{9}$|^0\d{2,3}-\d{7,8}$/, message: '请输入正确的联系电话', trigger: 'blur' }
  ],
  contactEmail: [
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ]
}

// 支付设置表单
const paymentForm = reactive({
  enableWechat: true,
  wechatAppId: 'wx1234567890abcdef',
  wechatMchId: '1234567890',
  wechatApiKey: 'abcdefghijklmnopqrstuvwxyz123456',
  enableAlipay: true,
  alipayAppId: '2021000000000000',
  alipayPrivateKey: '-----BEGIN RSA PRIVATE KEY-----\n...\n-----END RSA PRIVATE KEY-----',
  alipayPublicKey: '-----BEGIN PUBLIC KEY-----\n...\n-----END PUBLIC KEY-----'
})

// 物流设置表单
const logisticsForm = reactive({
  enableLogisticsQuery: true,
  logisticsAppKey: 'abcdefghijklmn',
  logisticsAppSecret: '1234567890abcdefghijklmn',
  companies: [
    { code: 'SF', name: '顺丰速运' },
    { code: 'ZTO', name: '中通快递' },
    { code: 'YTO', name: '圆通速递' },
    { code: 'YD', name: '韵达快递' },
    { code: 'STO', name: '申通快递' },
    { code: 'JD', name: '京东物流' }
  ]
})

// 管理员设置表单
const adminForm = reactive({
  username: 'admin',
  name: '系统管理员',
  phone: '13800138000',
  email: '<EMAIL>',
  oldPassword: '',
  newPassword: '',
  confirmPassword: ''
})

// 管理员设置验证规则
const adminRules = {
  name: [
    { required: true, message: '请输入管理员姓名', trigger: 'blur' }
  ],
  phone: [
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
  ],
  email: [
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ],
  newPassword: [
    { min: 6, message: '密码长度不能小于6个字符', trigger: 'blur' }
  ],
  confirmPassword: [
    { validator: (rule, value, callback) => {
      if (adminForm.newPassword && value !== adminForm.newPassword) {
        callback(new Error('两次输入的密码不一致'))
      } else {
        callback()
      }
    }, trigger: 'blur' }
  ]
}

// Logo上传前的验证
const beforeLogoUpload = (file) => {
  const isJPG = file.type === 'image/jpeg'
  const isPNG = file.type === 'image/png'
  const isLt2M = file.size / 1024 / 1024 < 2

  if (!isJPG && !isPNG) {
    ElMessage.error('上传Logo只能是 JPG 或 PNG 格式!')
    return false
  }
  if (!isLt2M) {
    ElMessage.error('上传Logo大小不能超过 2MB!')
    return false
  }
  return true
}

// Logo上传成功
const handleLogoSuccess = (response) => {
  basicForm.logo = response.data.url
}

// 添加物流公司
const addLogisticsCompany = () => {
  logisticsForm.companies.push({ code: '', name: '' })
}

// 移除物流公司
const removeLogisticsCompany = (index) => {
  logisticsForm.companies.splice(index, 1)
}

// 保存基本设置
const saveBasicSettings = async () => {
  await basicFormRef.value.validate(async (valid) => {
    if (valid) {
      submitting.value = true
      try {
        // 这里应该调用API保存基本设置
        setTimeout(() => {
          ElMessage.success('基本设置保存成功')
          submitting.value = false
        }, 500)
      } catch (error) {
        console.error('保存基本设置失败:', error)
        ElMessage.error('保存基本设置失败')
        submitting.value = false
      }
    } else {
      return false
    }
  })
}

// 保存支付设置
const savePaymentSettings = async () => {
  submitting.value = true
  try {
    // 这里应该调用API保存支付设置
    setTimeout(() => {
      ElMessage.success('支付设置保存成功')
      submitting.value = false
    }, 500)
  } catch (error) {
    console.error('保存支付设置失败:', error)
    ElMessage.error('保存支付设置失败')
    submitting.value = false
  }
}

// 保存物流设置
const saveLogisticsSettings = async () => {
  submitting.value = true
  try {
    // 这里应该调用API保存物流设置
    setTimeout(() => {
      ElMessage.success('物流设置保存成功')
      submitting.value = false
    }, 500)
  } catch (error) {
    console.error('保存物流设置失败:', error)
    ElMessage.error('保存物流设置失败')
    submitting.value = false
  }
}

// 保存管理员设置
const saveAdminSettings = async () => {
  await adminFormRef.value.validate(async (valid) => {
    if (valid) {
      submitting.value = true
      try {
        // 这里应该调用API保存管理员设置
        setTimeout(() => {
          ElMessage.success('管理员设置保存成功')
          adminForm.oldPassword = ''
          adminForm.newPassword = ''
          adminForm.confirmPassword = ''
          submitting.value = false
        }, 500)
      } catch (error) {
        console.error('保存管理员设置失败:', error)
        ElMessage.error('保存管理员设置失败')
        submitting.value = false
      }
    } else {
      return false
    }
  })
}

// 获取设置数据
const fetchSettings = async () => {
  try {
    // 这里应该调用API获取设置数据
    // 暂时使用模拟数据
  } catch (error) {
    console.error('获取设置数据失败:', error)
    ElMessage.error('获取设置数据失败')
  }
}

onMounted(() => {
  fetchSettings()
})
</script>

<style scoped>
.settings-container {
  padding: 20px;
}

.settings-card {
  margin-bottom: 20px;
}

.card-header {
  font-weight: bold;
}

.logo-uploader {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  width: 178px;
  height: 178px;
}

.logo-uploader:hover {
  border-color: #409EFF;
}

.logo-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  line-height: 178px;
  text-align: center;
}

.logo-image {
  width: 178px;
  height: 178px;
  display: block;
}

.logistics-companies {
  margin-top: 20px;
}

.company-item {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}
</style>
