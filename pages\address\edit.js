// pages/address/edit.js
const addressApi = require('../../utils/addressApi.js');

Page({
  data: {
    // 地址信息
    addressInfo: {
      id: 0,
      consignee: '',
      phone: '',
      address: '',
      isDefault: false
    },
    // 是否是编辑模式
    isEdit: false,
    // 表单验证错误信息
    errors: {
      consignee: '',
      phone: '',
      address: ''
    },
    // 地区选择器
    region: ['', '', ''],
    // 详细地址
    detailAddress: '',
    // 是否正在提交
    submitting: false
  },

  onLoad: function (options) {
    // 如果传入了地址ID，则是编辑模式
    if (options.id) {
      const addressId = parseInt(options.id);
      this.setData({
        isEdit: true,
        'addressInfo.id': addressId
      });
      
      // 设置导航栏标题
      wx.setNavigationBarTitle({
        title: '编辑收货地址'
      });
      
      // 加载地址详情
      this.loadAddressDetail(addressId);
    } else {
      // 新增模式
      wx.setNavigationBarTitle({
        title: '新增收货地址'
      });
    }
  },

  // 加载地址详情
  loadAddressDetail: function (addressId) {
    wx.showLoading({ title: '加载中...' });
    
    // 从地址列表中查找对应的地址
    addressApi.getAddressList()
      .then(addressList => {
        const address = addressList.find(item => item.id === addressId);
        
        if (address) {
          // 解析地址，分离省市区和详细地址
          const { region, detailAddress } = this.parseAddress(address.address);
          
          this.setData({
            addressInfo: {
              id: address.id,
              consignee: address.consignee,
              phone: address.phone,
              address: address.address,
              isDefault: address.isDefault
            },
            region: region,
            detailAddress: detailAddress
          });
        } else {
          wx.showToast({
            title: '地址不存在',
            icon: 'none'
          });
          setTimeout(() => {
            wx.navigateBack();
          }, 1500);
        }
      })
      .catch(err => {
        console.error('获取地址详情失败:', err);
        wx.showToast({
          title: '获取地址失败',
          icon: 'none'
        });
      })
      .finally(() => {
        wx.hideLoading();
      });
  },

  // 解析地址，分离省市区和详细地址
  parseAddress: function (fullAddress) {
    // 简单实现，实际可能需要更复杂的解析逻辑
    const parts = fullAddress.split(' ');
    let region = ['', '', ''];
    let detailAddress = '';
    
    if (parts.length >= 4) {
      // 假设前三部分是省市区
      region = parts.slice(0, 3);
      detailAddress = parts.slice(3).join(' ');
    } else if (parts.length === 3) {
      region = parts;
      detailAddress = '';
    } else {
      detailAddress = fullAddress;
    }
    
    return { region, detailAddress };
  },

  // 输入框内容变化
  onInputChange: function (e) {
    const field = e.currentTarget.dataset.field;
    const value = e.detail.value;
    
    this.setData({
      [`addressInfo.${field}`]: value,
      [`errors.${field}`]: '' // 清除对应的错误信息
    });
  },

  // 地区选择器变化
  onRegionChange: function (e) {
    this.setData({
      region: e.detail.value
    });
  },

  // 详细地址输入变化
  onDetailAddressChange: function (e) {
    this.setData({
      detailAddress: e.detail.value,
      'errors.address': '' // 清除地址错误信息
    });
  },

  // 默认地址开关变化
  onDefaultChange: function (e) {
    this.setData({
      'addressInfo.isDefault': e.detail.value
    });
  },

  // 表单验证
  validateForm: function () {
    let isValid = true;
    const errors = {
      consignee: '',
      phone: '',
      address: ''
    };
    
    // 验证收货人
    if (!this.data.addressInfo.consignee.trim()) {
      errors.consignee = '请输入收货人姓名';
      isValid = false;
    }
    
    // 验证手机号
    const phoneReg = /^1[3-9]\d{9}$/;
    if (!this.data.addressInfo.phone.trim()) {
      errors.phone = '请输入手机号码';
      isValid = false;
    } else if (!phoneReg.test(this.data.addressInfo.phone.trim())) {
      errors.phone = '手机号码格式不正确';
      isValid = false;
    }
    
    // 验证地址
    if (this.data.region[0] === '' || this.data.region[1] === '' || this.data.region[2] === '') {
      errors.address = '请选择所在地区';
      isValid = false;
    } else if (!this.data.detailAddress.trim()) {
      errors.address = '请输入详细地址';
      isValid = false;
    }
    
    this.setData({ errors });
    return isValid;
  },

  // 保存地址
  saveAddress: function () {
    // 表单验证
    if (!this.validateForm()) {
      return;
    }
    
    // 防止重复提交
    if (this.data.submitting) {
      return;
    }
    
    this.setData({ submitting: true });
    wx.showLoading({ title: '保存中...' });
    
    // 构建完整地址
    const fullAddress = this.data.region.join(' ') + ' ' + this.data.detailAddress;
    
    // 构建请求数据
    const addressData = {
      ...this.data.addressInfo,
      address: fullAddress
    };
    
    // 根据是否是编辑模式调用不同的API
    const apiPromise = this.data.isEdit
      ? addressApi.updateAddress(addressData)
      : addressApi.addAddress(addressData);
    
    apiPromise
      .then(() => {
        wx.showToast({
          title: this.data.isEdit ? '更新成功' : '添加成功',
          icon: 'success'
        });
        
        // 返回上一页
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
      })
      .catch(err => {
        console.error(this.data.isEdit ? '更新地址失败:' : '添加地址失败:', err);
        wx.showToast({
          title: this.data.isEdit ? '更新失败' : '添加失败',
          icon: 'none'
        });
      })
      .finally(() => {
        this.setData({ submitting: false });
        wx.hideLoading();
      });
  }
});
