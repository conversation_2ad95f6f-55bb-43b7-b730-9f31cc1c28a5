<template>
  <div v-if="!item.hidden">
    <!-- 单级菜单项 -->
    <el-menu-item
      v-if="item.meta"
      :index="resolvePath(item.path)"
    >
      <el-icon v-if="item.meta.icon">
        <component :is="item.meta.icon" />
      </el-icon>
      <template #title>
        <span>{{ item.meta.title }}</span>
      </template>
    </el-menu-item>
  </div>
</template>

<script setup>
import { isExternal } from '@/utils/validate'

const props = defineProps({
  item: {
    type: Object,
    required: true
  },
  basePath: {
    type: String,
    default: ''
  }
})

// 解析路径
const resolvePath = (routePath) => {
  if (isExternal(routePath)) {
    return routePath
  }

  if (isExternal(props.basePath)) {
    return props.basePath
  }

  // 对于主菜单项，正确拼接路径
  if (props.basePath === '/') {
    return `/${routePath}`
  }

  return `${props.basePath}/${routePath}`.replace(/\/\/+/g, '/')
}


</script>
