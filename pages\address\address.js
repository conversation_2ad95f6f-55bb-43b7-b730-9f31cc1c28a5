// pages/address/address.js
const addressApi = require('../../utils/addressApi.js');

Page({
  data: {
    addressList: [],
    loading: false,
    isFromOrder: false, // 是否从订单页面跳转而来
    selectedId: -1 // 当前选中的地址ID
  },

  onLoad: function (options) {
    // 检查是否从订单页面跳转而来
    if (options.from === 'order') {
      this.setData({
        isFromOrder: true
      });
    }

    // 如果传入了已选择的地址ID
    if (options.selectedId) {
      this.setData({
        selectedId: parseInt(options.selectedId)
      });
    }

    // 加载地址列表
    this.loadAddressList();
  },

  onShow: function () {
    // 页面显示时重新加载地址列表，以便在添加/编辑地址后刷新
    this.loadAddressList();
  },

  // 加载地址列表
  loadAddressList: function () {
    this.setData({ loading: true });

    addressApi.getAddressList()
      .then(addressList => {
        this.setData({
          addressList: addressList || [],
          loading: false
        });
      })
      .catch(err => {
        console.error('获取地址列表失败:', err);
        wx.showToast({
          title: '获取地址失败',
          icon: 'none'
        });
        this.setData({ loading: false });
      });
  },

  // 添加新地址
  addAddress: function () {
    wx.navigateTo({
      url: '/pages/address/edit'
    });
  },

  // 编辑地址
  editAddress: function (e) {
    const addressId = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/address/edit?id=${addressId}`
    });
  },

  // 删除地址
  deleteAddress: function (e) {
    const addressId = e.currentTarget.dataset.id;
    const addressIndex = e.currentTarget.dataset.index;
    const addressList = this.data.addressList;
    const address = addressList[addressIndex];

    wx.showModal({
      title: '提示',
      content: '确定要删除这个地址吗？',
      success: (res) => {
        if (res.confirm) {
          wx.showLoading({ title: '删除中...' });

          addressApi.deleteAddress(addressId)
            .then(() => {
              // 从列表中移除
              addressList.splice(addressIndex, 1);
              this.setData({ addressList });
              
              wx.showToast({
                title: '删除成功',
                icon: 'success'
              });
            })
            .catch(err => {
              console.error('删除地址失败:', err);
              wx.showToast({
                title: '删除失败',
                icon: 'none'
              });
            })
            .finally(() => {
              wx.hideLoading();
            });
        }
      }
    });
  },

  // 设置默认地址
  setDefaultAddress: function (e) {
    const addressId = e.currentTarget.dataset.id;
    const addressIndex = e.currentTarget.dataset.index;
    
    // 如果已经是默认地址，则不做操作
    if (this.data.addressList[addressIndex].isDefault) {
      return;
    }

    wx.showLoading({ title: '设置中...' });

    addressApi.setDefaultAddress(addressId)
      .then(() => {
        // 更新列表中的默认状态
        const addressList = this.data.addressList.map((item, index) => {
          item.isDefault = index === addressIndex;
          return item;
        });

        this.setData({ addressList });
        
        wx.showToast({
          title: '设置成功',
          icon: 'success'
        });
      })
      .catch(err => {
        console.error('设置默认地址失败:', err);
        wx.showToast({
          title: '设置失败',
          icon: 'none'
        });
      })
      .finally(() => {
        wx.hideLoading();
      });
  },

  // 选择地址并返回
  selectAddress: function (e) {
    if (!this.data.isFromOrder) {
      return;
    }

    const addressIndex = e.currentTarget.dataset.index;
    const address = this.data.addressList[addressIndex];

    // 将选中的地址保存到缓存
    wx.setStorageSync('selectedAddress', address);

    // 返回上一页
    wx.navigateBack();
  }
});
