<template>
  <div class="banner-list-container">
    <!-- 搜索和操作区域 -->
    <el-card class="search-card" shadow="never">
      <div class="search-operations">
        <el-form :model="searchForm" class="search-form" inline>
          <el-form-item label="标题">
            <el-input
              v-model="searchForm.title"
              placeholder="请输入轮播图标题"
              clearable
              style="width: 200px"
            />
          </el-form-item>
          <el-form-item label="状态">
            <el-select
              v-model="searchForm.status"
              placeholder="请选择状态"
              clearable
              style="width: 120px"
            >
              <el-option label="启用" :value="1" />
              <el-option label="禁用" :value="0" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">搜索</el-button>
            <el-button @click="resetSearch">重置</el-button>
          </el-form-item>
        </el-form>
        <div class="operation-buttons">
          <el-button type="primary" @click="handleAdd">添加轮播图</el-button>
          <el-button type="danger" @click="handleBatchDelete">批量删除</el-button>
        </div>
      </div>
    </el-card>

    <!-- 轮播图列表 -->
    <el-card class="banner-table-card" shadow="never">
      <el-table
        v-loading="loading"
        :data="bannerList"
        border
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column label="轮播图" width="150">
          <template #default="scope">
            <el-image
              :src="scope.row.imageUrl"
              :preview-src-list="[scope.row.imageUrl]"
              fit="cover"
              style="width: 120px; height: 60px; border-radius: 4px"
            />
          </template>
        </el-table-column>
        <el-table-column prop="title" label="标题" min-width="200" show-overflow-tooltip />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-switch
              v-model="scope.row.status"
              :active-value="1"
              :inactive-value="0"
              @change="handleStatusChange(scope.row)"
            />
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="180" />
        <el-table-column prop="updateTime" label="更新时间" width="180" />
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="scope">
            <el-button type="primary" link @click="handleEdit(scope.row)">编辑</el-button>

            <el-button type="danger" link @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
// 启用真实API调用
import { getBannerList, deleteBanner, updateBannerStatus, batchDeleteBanners } from '@/api/banner'

const router = useRouter()
const loading = ref(false)
const bannerList = ref([])
const selectedBanners = ref([])
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(10)
const previewDialogVisible = ref(false)
const previewImage = ref('')
const previewBanner = ref({})

// 搜索表单
const searchForm = reactive({
  title: '',
  status: ''
})

// 获取轮播图列表
const fetchBannerList = async () => {
  loading.value = true
  try {
    // 构建查询参数
    const params = {
      page: currentPage.value,
      pageSize: pageSize.value,
      title: searchForm.title || undefined,
      status: searchForm.status !== '' ? searchForm.status : undefined
    }

    // 调用真实API
    const response = await getBannerList(params)

    if (response.code === 1) {
      bannerList.value = response.data.records || []
      total.value = response.data.total || 0
    } else {
      ElMessage.error(response.msg || '获取轮播图列表失败')
    }
  } catch (error) {
    console.error('获取轮播图列表失败:', error)
    ElMessage.error('获取轮播图列表失败')
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  currentPage.value = 1
  fetchBannerList()
}

// 重置搜索
const resetSearch = () => {
  Object.keys(searchForm).forEach(key => {
    searchForm[key] = ''
  })
  currentPage.value = 1
  fetchBannerList()
}

// 添加轮播图
const handleAdd = () => {
  router.push('/banner/add')
}

// 编辑轮播图
const handleEdit = (row) => {
  router.push(`/banner/edit/${row.id}`)
}

// 预览轮播图
const handleView = (row) => {
  previewBanner.value = row
  previewImage.value = row.imageUrl
  previewDialogVisible.value = true
}

// 删除轮播图
const handleDelete = (row) => {
  ElMessageBox.confirm('确定要删除该轮播图吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      const response = await deleteBanner(row.id)
      if (response.code === 1) {
        ElMessage.success('删除成功')
        fetchBannerList()
      } else {
        ElMessage.error(response.msg || '删除失败')
      }
    } catch (error) {
      console.error('删除轮播图失败:', error)
      ElMessage.error('删除轮播图失败')
    }
  }).catch(() => {})
}

// 批量删除
const handleBatchDelete = () => {
  if (selectedBanners.value.length === 0) {
    ElMessage.warning('请选择要删除的轮播图')
    return
  }

  ElMessageBox.confirm(`确定要删除选中的 ${selectedBanners.value.length} 个轮播图吗？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      const ids = selectedBanners.value.map(item => item.id)
      const response = await batchDeleteBanners(ids)
      if (response.code === 1) {
        ElMessage.success('批量删除成功')
        fetchBannerList()
      } else {
        ElMessage.error(response.msg || '批量删除失败')
      }
    } catch (error) {
      console.error('批量删除轮播图失败:', error)
      ElMessage.error('批量删除轮播图失败')
    }
  }).catch(() => {})
}

// 更改轮播图状态
const handleStatusChange = async (row) => {
  try {
    const response = await updateBannerStatus(row.id, row.status)
    if (response.code === 1) {
      ElMessage.success(`轮播图已${row.status === 1 ? '启用' : '禁用'}`)
    } else {
      ElMessage.error(response.msg || '状态更新失败')
      // 恢复原状态
      row.status = row.status === 1 ? 0 : 1
    }
  } catch (error) {
    console.error('更新轮播图状态失败:', error)
    ElMessage.error('更新轮播图状态失败')
    // 恢复原状态
    row.status = row.status === 1 ? 0 : 1
  }
}

// 表格选择变化
const handleSelectionChange = (selection) => {
  selectedBanners.value = selection
}

// 页码变化
const handleCurrentChange = (page) => {
  currentPage.value = page
  fetchBannerList()
}

// 每页条数变化
const handleSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1
  fetchBannerList()
}

onMounted(() => {
  fetchBannerList()
})
</script>

<style scoped>
.banner-list-container {
  padding: 20px;
}

.search-card {
  margin-bottom: 20px;
}

.search-operations {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
}

.search-form {
  flex: 1;
  min-width: 600px;
}

.operation-buttons {
  display: flex;
  align-items: center;
  gap: 10px;
}

.banner-table-card {
  margin-bottom: 20px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.preview-container {
  text-align: center;
}

.preview-info {
  margin-top: 20px;
  text-align: left;
}

.preview-info p {
  margin: 10px 0;
  font-size: 14px;
}
</style>