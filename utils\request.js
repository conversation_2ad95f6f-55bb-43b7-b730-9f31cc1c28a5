// utils/request.js

// 是否正在刷新Token
let isRefreshing = false;
// 等待刷新Token的请求队列
let requestsQueue = [];

/**
 * 处理Token过期
 * @param {Object} originalOptions - 原始请求选项
 * @returns {Promise} - 返回Promise
 */
const handleTokenExpired = (originalOptions) => {
  return new Promise((resolve, reject) => {
    // 将请求加入队列
    requestsQueue.push({
      options: originalOptions,
      resolve,
      reject
    });

    // 如果已经在刷新Token，则不重复执行
    if (isRefreshing) {
      return;
    }

    isRefreshing = true;

    // 调用微信登录获取code
    wx.login({
      success: (loginRes) => {
        if (loginRes.code) {
          // 使用code获取新Token
          wx.request({
            url: getApp().globalData.baseUrl + '/user/login',
            method: 'POST',
            data: {
              code: loginRes.code
            },
            success: (tokenRes) => {
              if (tokenRes.data.code === 0 || tokenRes.data.code === 1) {
                // 获取新Token成功
                const newToken = tokenRes.data.data.token;

                // 保存新Token
                const app = getApp();
                app.globalData.token = newToken;
                app.globalData.userId = tokenRes.data.data.userId;
                app.globalData.userInfo = tokenRes.data.data;
                app.globalData.isLoggedIn = true;

                wx.setStorageSync('token', newToken);
                wx.setStorageSync('userId', tokenRes.data.data.userId);
                wx.setStorageSync('userInfo', tokenRes.data.data);

                console.log('Token刷新成功');

                // 重新发起队列中的请求
                requestsQueue.forEach((item) => {
                  request({
                    ...item.options,
                    success: (res) => item.resolve(res),
                    fail: (err) => item.reject(err)
                  });
                });
              } else {
                // 获取新Token失败，清空队列并拒绝所有请求
                requestsQueue.forEach((item) => {
                  item.reject(new Error('刷新Token失败'));
                });

                // 跳转到登录页
                wx.navigateTo({
                  url: '/pages/login/login'
                });
              }
            },
            fail: () => {
              // 请求失败，清空队列并拒绝所有请求
              requestsQueue.forEach((item) => {
                item.reject(new Error('刷新Token请求失败'));
              });

              // 跳转到登录页
              wx.navigateTo({
                url: '/pages/login/login'
              });
            },
            complete: () => {
              // 重置状态和队列
              isRefreshing = false;
              requestsQueue = [];
            }
          });
        } else {
          // 微信登录失败，清空队列并拒绝所有请求
          requestsQueue.forEach((item) => {
            item.reject(new Error('微信登录失败'));
          });

          // 重置状态和队列
          isRefreshing = false;
          requestsQueue = [];

          // 跳转到登录页
          wx.navigateTo({
            url: '/pages/login/login'
          });
        }
      },
      fail: () => {
        // 微信登录失败，清空队列并拒绝所有请求
        requestsQueue.forEach((item) => {
          item.reject(new Error('微信登录失败'));
        });

        // 重置状态和队列
        isRefreshing = false;
        requestsQueue = [];

        // 跳转到登录页
        wx.navigateTo({
          url: '/pages/login/login'
        });
      }
    });
  });
};

/**
 * 封装wx.request，自动添加token到请求头
 * @param {Object} options - 请求选项
 * @param {string} options.url - 请求地址
 * @param {string} options.method - 请求方法
 * @param {Object} options.data - 请求数据
 * @param {Object} options.header - 请求头
 * @param {Function} options.success - 成功回调
 * @param {Function} options.fail - 失败回调
 * @param {Function} options.complete - 完成回调
 * @param {boolean} options.needToken - 是否需要token
 */
const request = (options) => {
  const app = getApp();
  const baseUrl = app.globalData.baseUrl;

  // 默认需要token
  const needToken = options.needToken !== false;

  // 构建请求头
  let header = options.header || {};

  // 如果需要token，添加到请求头
  if (needToken) {
    const token = app.globalData.token || wx.getStorageSync('token');
    if (token) {
      header['Authorization'] = token;
    }
  }

  // 处理URL和参数
  let url = options.url.startsWith('http') ? options.url : baseUrl + options.url;
  let data = options.data || {};

  // 如果是GET请求且包含keyword参数，确保正确处理
  if ((options.method || 'GET').toUpperCase() === 'GET' && data.keyword !== undefined) {
    // 确保keyword参数直接作为文本传递，不进行URL编码
    console.log('处理搜索请求，关键词:', data.keyword);
  }

  // 记录请求详情
  console.log('Request - 发起请求:', {
    url: url,
    method: options.method || 'GET',
    data: data,
    header: header
  });

  // 发起请求
  wx.request({
    url: url,
    method: options.method || 'GET',
    data: data,
    header: header,
    success: (res) => {
      // 检查是否Token过期
      if (res.statusCode === 401 ||
          (res.data && (res.data.code === 401 || res.data.msg === '未授权，请重新登录'))) {

        // 处理Token过期
        handleTokenExpired(options)
          .then((newRes) => {
            // 新请求成功，调用原始成功回调
            if (options.success) {
              options.success(newRes);
            }
          })
          .catch((err) => {
            // 新请求失败，调用原始失败回调
            if (options.fail) {
              options.fail(err);
            } else {
              wx.showToast({
                title: '登录已过期，请重新登录',
                icon: 'none'
              });
            }
          });
      } else {
        // 正常响应，调用成功回调
        if (options.success) {
          options.success(res);
        }
      }
    },
    fail: (err) => {
      // 调用失败回调
      if (options.fail) {
        options.fail(err);
      } else {
        // 默认错误提示
        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        });
      }
    },
    complete: options.complete
  });
};

/**
 * GET请求
 * @param {string} url - 请求地址
 * @param {Object} data - 请求数据
 * @param {Object} options - 其他选项
 */
const get = (url, data = {}, options = {}) => {
  // 检查是否包含keyword参数，如果有，确保直接传递文本
  if (data.keyword !== undefined) {
    console.log('发送搜索请求，关键词:', data.keyword);

    // 如果options中没有header，则创建
    if (!options.header) {
      options.header = {};
    }

    // 添加Content-Type头，确保正确处理文本
    options.header['Content-Type'] = 'application/x-www-form-urlencoded';
  }

  return request({
    url,
    method: 'GET',
    data,
    ...options
  });
};

/**
 * POST请求
 * @param {string} url - 请求地址
 * @param {Object} data - 请求数据
 * @param {Object} options - 其他选项
 */
const post = (url, data = {}, options = {}) => {
  return request({
    url,
    method: 'POST',
    data,
    ...options
  });
};

/**
 * PUT请求
 * @param {string} url - 请求地址
 * @param {Object} data - 请求数据
 * @param {Object} options - 其他选项
 */
const put = (url, data = {}, options = {}) => {
  return request({
    url,
    method: 'PUT',
    data,
    ...options
  });
};

/**
 * DELETE请求
 * @param {string} url - 请求地址
 * @param {Object} data - 请求数据
 * @param {Object} options - 其他选项
 */
const del = (url, data = {}, options = {}) => {
  return request({
    url,
    method: 'DELETE',
    data,
    ...options
  });
};

module.exports = {
  request,
  get,
  post,
  put,
  delete: del
};
