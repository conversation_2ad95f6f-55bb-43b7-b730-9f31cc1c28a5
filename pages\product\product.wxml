<!--pages/product/product.wxml-->
<view class="container">
  <!-- 加载中 -->
  <view class="loading-container" wx:if="{{!product}}">
    <view class="loading-spinner"></view>
    <text class="loading-text">商品信息加载中...</text>
  </view>

  <block wx:if="{{product}}">
    <!-- 商品轮播图 -->
    <swiper class="product-swiper" indicator-dots="{{indicatorDots}}" autoplay="{{autoplay}}" interval="{{interval}}" duration="{{duration}}" bindchange="swiperChange">
      <block wx:for="{{product.images}}" wx:key="*this">
        <swiper-item>
          <image src="{{item}}" class="slide-image" mode="aspectFill" bindtap="previewImage" data-src="{{item}}" />
        </swiper-item>
      </block>
    </swiper>

    <!-- 轮播图指示器 -->
    <view class="swiper-indicator">
      <text>{{current}}/{{product.images.length}}</text>
    </view>

    <!-- 商品价格信息 -->
    <view class="product-price-container">
      <view class="price-row">
        <view class="price-info">
          <text class="price-symbol">¥</text>
          <text class="price-value">{{product.price}}</text>
          <block wx:if="{{product.originalPrice && product.originalPrice !== product.price}}">
            <text class="original-price">¥{{product.originalPrice}}</text>
          </block>
          <text class="sales-info">剩余 {{product.stock}}件</text>
        </view>
      </view>

      <!-- 商品名称 -->
      <view class="product-name-row">
        <text class="product-name">{{product.name}}</text>
      </view>
    </view>

    <!-- 配送信息 -->
    <view class="delivery-section">
      <view class="section-item" bindtap="showAddressSelector">
        <view class="section-label">配送</view>
        <view class="section-content">
          <text>至 {{currentAddress.address}}</text>
        </view>
        <view class="section-arrow">></view>
      </view>
      <view class="delivery-info">
        <text class="delivery-label">快递: </text>
        <text class="delivery-value">免运费</text>
      </view>
    </view>

    <!-- 服务信息 -->
    <view class="service-section">
      <view class="section-item">
        <view class="section-label">服务</view>
        <view class="section-content">
          <text>线下门店 · 到店自提 · 收货后结算</text>
        </view>
      </view>
    </view>

    <!-- 选项卡 -->
    <view class="tab-container">
      <view class="tab-header">
        <view class="tab-item {{currentTab == 0 ? 'active' : ''}}" bindtap="switchTab" data-index="0">
          <text>商品</text>
        </view>
        <view class="tab-item {{currentTab == 1 ? 'active' : ''}}" bindtap="switchTab" data-index="1">
          <text>评价</text>
        </view>
        <view class="tab-item {{currentTab == 2 ? 'active' : ''}}" bindtap="switchTab" data-index="2">
          <text>详情</text>
        </view>
        <view class="tab-item {{currentTab == 3 ? 'active' : ''}}" bindtap="switchTab" data-index="3">
          <text>推荐</text>
        </view>
      </view>

      <view class="tab-content">
        <!-- 商品介绍 -->
        <view class="tab-panel" hidden="{{currentTab != 0}}">
          <!-- 详情图片无缝展示 -->
          <view class="detail-images" wx:if="{{product.detailImages && product.detailImages.length > 0}}">
            <block wx:for="{{product.detailImages}}" wx:key="*this">
              <image src="{{item}}" class="detail-image" mode="widthFix" lazy-load="{{true}}" binderror="onDetailImageError" bindload="onDetailImageLoad" data-src="{{item}}" bindtap="previewDetailImage" />
            </block>
          </view>

          <!-- 当没有详情图片时显示描述 -->
          <view class="no-detail-images" wx:if="{{!product.detailImages || product.detailImages.length === 0}}">
            <text class="detail-text">{{product.detail || product.description || '暂无详细信息'}}</text>
          </view>
        </view>

        <!-- 评价 -->
        <view class="tab-panel" hidden="{{currentTab != 1}}">
          <!-- 评价统计 -->
          <view class="review-summary" wx:if="{{product.reviewCount > 0 || product.averageRating > 0}}">
            <view class="review-stats">
              <view class="average-rating">
                <text class="rating-number">{{product.averageRating || 0}}</text>
                <view class="rating-stars">
                  <block wx:for="{{5}}" wx:key="*this">
                    <image src="/images/icon-star{{index < (product.averageRating || 0) ? '' : '-empty'}}.png" class="star-icon" />
                  </block>
                </view>
              </view>
              <text class="review-count">共{{product.reviewCount || 0}}条评价</text>
            </view>
          </view>

          <!-- 评价列表 -->
          <view class="comment-list">
            <!-- 有评价时显示评价列表 -->
            <view wx:if="{{product.comments && product.comments.length > 0}}">
              <block wx:for="{{product.comments}}" wx:key="id">
                <view class="comment-item">
                  <view class="comment-header">
                    <image src="{{item.avatar}}" class="comment-avatar" binderror="onAvatarError" />
                    <text class="comment-user">{{item.user}}</text>
                    <view class="comment-star">
                      <block wx:for="{{item.starArray}}" wx:key="*this" wx:for-item="star">
                        <image src="/images/icon-star.png" class="star-icon" />
                      </block>
                    </view>
                  </view>
                  <view class="comment-content">
                    <text>{{item.content}}</text>
                  </view>
                  <view class="comment-images" wx:if="{{item.images && item.images.length > 0}}">
                    <block wx:for="{{item.images}}" wx:key="*this" wx:for-item="img">
                      <image src="{{img}}" class="comment-image" mode="aspectFill" bindtap="previewCommentImage" data-src="{{img}}" data-images="{{item.images}}" />
                    </block>
                  </view>
                  <view class="comment-footer">
                    <text class="comment-date">{{item.date}}</text>
                  </view>
                </view>
              </block>
            </view>

            <!-- 无评价时显示占位内容 -->
            <view class="no-comments" wx:else>
              <image src="/images/empty-comment.png" class="empty-comment-image" mode="aspectFit" />
              <text class="empty-comment-text">暂无评价</text>
              <text class="empty-comment-desc">快来成为第一个评价的用户吧~</text>
            </view>
          </view>
        </view>

        <!-- 详情 -->
        <view class="tab-panel" hidden="{{currentTab != 2}}">
          <view class="product-detail">
            <!-- 商品描述文字 -->
            <view class="detail-description" wx:if="{{product.description}}">
              <text class="detail-text">{{product.description}}</text>
            </view>
          </view>
        </view>

        <!-- 推荐 -->
        <view class="tab-panel" hidden="{{currentTab != 3}}">
          <view class="recommend-list">
            <block wx:for="{{product.recommends}}" wx:key="id">
              <navigator url="/pages/product/product?id={{item.id}}" class="recommend-item">
                <image src="{{item.image}}" class="recommend-image" mode="aspectFill" />
                <view class="recommend-info">
                  <text class="recommend-name">{{item.name}}</text>
                  <text class="recommend-price">¥{{item.price}}</text>
                </view>
              </navigator>
            </block>
          </view>
        </view>
      </view>
    </view>

    <!-- 底部操作栏 -->
    <view class="footer">
      <view class="footer-left">
        <view class="footer-btn" bindtap="onShareTap">
          <image src="/images/icon-share.png" class="footer-icon" />
          <text class="footer-btn-text">分享</text>
        </view>
        <view class="footer-btn" bindtap="contactService">
          <image src="/images/icon-service.png" class="footer-icon" />
          <text class="footer-btn-text">客服</text>
        </view>
        <view class="footer-btn" bindtap="goToCart">
          <image src="/images/icon-cart.png" class="footer-icon" />
          <text class="footer-btn-text">购物车</text>
        </view>
      </view>
      <view class="footer-right">
        <view class="btn-container">
          <button class="add-cart-btn" bindtap="showSkuPanel" data-type="cart">加入购物车</button>
          <button class="buy-now-btn" bindtap="showSkuPanel" data-type="buy">立即购买</button>
        </view>
      </view>
    </view>

    <!-- SKU选择面板 -->
    <view class="sku-panel-mask" wx:if="{{showSku}}" bindtap="hideSkuPanel"></view>
    <view class="sku-panel {{showSku ? 'show' : ''}}">
      <view class="sku-header">
        <view class="sku-close" bindtap="hideSkuPanel">×</view>
        <view class="sku-product-info">
          <image src="{{product.images[0]}}" class="sku-image" />
          <view class="sku-info">
            <view class="sku-price-row">
              <text class="sku-price-symbol">¥</text>
              <text class="sku-price-value">{{sizes[selectedSizeIndex].price}}</text>
            </view>
            <view class="sku-stock">
              <text>剩余 {{sizes[selectedSizeIndex].stock || 999}}件</text>
            </view>
            <view class="sku-selected">
              <text>已选：{{selectedSize}} {{selectedColor}}</text>
            </view>
          </view>
        </view>
      </view>

      <view class="sku-body">
        <!-- 尺码选择 -->
        <view class="sku-row">
          <text class="sku-title">尺码</text>
          <view class="sku-options">
            <block wx:for="{{sizes}}" wx:key="id" wx:for-index="index">
              <view class="sku-option {{selectedSizeIndex === index ? 'selected' : ''}} {{item.stock <= 0 ? 'disabled' : ''}}" bindtap="selectSize" data-index="{{index}}">
                <view class="sku-option-name">{{item.name}}</view>
                <view class="sku-option-price">¥{{item.price}}</view>
                <view class="sku-option-stock" wx:if="{{item.stock <= 0}}">无货</view>
              </view>
            </block>
          </view>
        </view>

        <!-- 颜色选择 -->
        <view class="sku-row" wx:if="{{colors && colors.length > 0}}">
          <text class="sku-title">颜色</text>
          <view class="sku-options">
            <block wx:for="{{colors}}" wx:key="*this">
              <view class="sku-option {{selectedColor === item ? 'selected' : ''}}" bindtap="selectColor" data-color="{{item}}">
                <view class="sku-option-name">{{item}}</view>
              </view>
            </block>
          </view>
        </view>

        <!-- 数量选择 -->
        <view class="sku-row">
          <text class="sku-title">数量</text>
          <view class="quantity-selector">
            <view class="quantity-btn {{quantity <= 1 ? 'disabled' : ''}}" bindtap="minusCount">-</view>
            <input type="number" class="quantity-input" value="{{quantity}}" />
            <view class="quantity-btn" bindtap="addCount">+</view>
          </view>
        </view>
      </view>

      <view class="sku-footer">
        <button class="sku-confirm-btn" bindtap="{{skuType == 'cart' ? 'addToCart' : 'buyNow'}}">
          {{skuType == 'cart' ? '加入购物车' : '立即购买'}}
        </button>
      </view>
    </view>

    <!-- 地址选择弹窗 -->
    <view class="address-popup-mask" wx:if="{{showAddressPopup}}" bindtap="hideAddressSelector"></view>
    <view class="address-popup {{showAddressPopup ? 'show' : ''}}">
      <view class="address-popup-header">
        <text class="address-popup-title">请选择收货地址</text>
        <view class="address-popup-close" bindtap="hideAddressSelector">×</view>
      </view>

      <view class="address-list">
        <view class="address-item {{selectedAddressIndex === index ? 'selected' : ''}}" wx:for="{{addresses}}" wx:key="id" bindtap="selectAddress" data-index="{{index}}">
          <view class="address-select-icon">
            <icon type="{{selectedAddressIndex === index ? 'success' : 'circle'}}" size="18" color="{{selectedAddressIndex === index ? '#07c160' : '#999'}}"></icon>
          </view>
          <view class="address-info">
            <view class="address-contact">
              <text class="address-name">{{item.name}}</text>
              <text class="address-phone">{{item.phone}}</text>
              <text class="address-default" wx:if="{{item.isDefault}}">默认</text>
            </view>
            <view class="address-detail">{{item.address}}</view>
          </view>
        </view>
      </view>

      <view class="address-popup-footer">
        <button class="add-address-btn" bindtap="goToAddressManage">添加/管理收货地址</button>
      </view>
    </view>

    <!-- 订单确认面板 -->
    <view class="order-confirm-mask" wx:if="{{showOrderConfirm}}" bindtap="hideOrderConfirm"></view>
    <view class="order-confirm {{showOrderConfirm ? 'show' : ''}}">
      <view class="order-confirm-header">
        <view class="order-confirm-close" bindtap="hideOrderConfirm">×</view>
        <view class="order-price-info">
          <text class="price-value">¥{{sizes[selectedSizeIndex].price}}</text>
          <text class="stock-info">剩余 {{sizes[selectedSizeIndex].stock || 999}}件</text>
          <text class="selected-info">已选：{{selectedSize}}</text>
        </view>
      </view>



      <!-- 配送方式选择 -->
      <view class="delivery-type-section">
        <view class="delivery-type-title">配送方式</view>
        <view class="delivery-type-options">
          <view class="delivery-type-option {{deliveryType === 'express' ? 'selected' : ''}}" bindtap="selectDeliveryType" data-type="express">
            <view class="delivery-type-radio">
              <view class="radio-inner {{deliveryType === 'express' ? 'active' : ''}}"></view>
            </view>
            <view class="delivery-type-info">
              <view class="delivery-type-name">邮寄</view>
              <view class="delivery-type-desc">快递配送上门</view>
            </view>
          </view>
          <!-- <view class="delivery-type-option {{deliveryType === 'self' ? 'selected' : ''}}" bindtap="selectDeliveryType" data-type="self">
            <view class="delivery-type-radio">
              <view class="radio-inner {{deliveryType === 'self' ? 'active' : ''}}"></view>
            </view>
            <view class="delivery-type-info">
              <view class="delivery-type-name">自提</view>
              <view class="delivery-type-desc">到店自取更便捷</view>
            </view>
          </view> -->
        </view>
      </view>

      <!-- 邮寄信息 - 当选择邮寄时显示 -->
      <block wx:if="{{deliveryType === 'express'}}">
        <!-- 收货地址 -->
        <view class="order-section" bindtap="handleAddressClickInPopup">
          <view class="section-label">收货地址</view>
          <view class="section-content">
            <text wx:if="{{currentAddress}}">{{currentAddress.address}}</text>
            <text wx:else>请选择收货地址</text>
          </view>
          <view class="section-arrow">></view>
        </view>
      </block>

      <!-- 自提信息 - 当选择自提时显示 -->
      <block wx:if="{{deliveryType === 'self'}}">
        <!-- 店铺信息 -->
        <view class="shop-info">
          <view class="shop-tag">自提</view>
          <view class="shop-name">{{deliveryInfo.shop}}</view>
        </view>
        <view class="shop-address">{{deliveryInfo.location}}</view>

        <!-- 提货人信息 -->
        <view class="order-section" bindtap="showReceiverInput">
          <view class="section-label">提货人</view>
          <view class="section-content">
            <text>{{receiver.name || '请添加联系人'}}</text>
          </view>
          <view class="section-arrow">></view>
        </view>

        <!-- 提货时间 -->
        <view class="order-section" bindtap="showTimeSelector">
          <view class="section-label">提货时间</view>
          <view class="section-content">
            <text>{{deliveryTime || '请选择提货时间'}}</text>
          </view>
          <view class="section-arrow">></view>
        </view>
      </block>

      <!-- 尺码选择 -->
      <view class="order-section">
        <view class="section-label">尺码</view>
        <view class="section-content">
          <text>{{selectedSize}}</text>
        </view>
        <view class="section-arrow">></view>
      </view>

      <!-- 颜色选择 -->
      <view class="order-section" wx:if="{{selectedColor}}">
        <view class="section-label">颜色</view>
        <view class="section-content">
          <text>{{selectedColor}}</text>
        </view>
        <view class="section-arrow">></view>
      </view>

      <!-- 数量选择 -->
      <view class="order-section">
        <view class="section-label">数量</view>
        <view class="section-content">
          <view class="quantity-selector">
            <view class="quantity-btn {{quantity <= 1 ? 'disabled' : ''}}" bindtap="minusCount">-</view>
            <input type="number" class="quantity-input" value="{{quantity}}" />
            <view class="quantity-btn" bindtap="addCount">+</view>
          </view>
        </view>
      </view>

      <!-- 支付方式 -->
      <view class="payment-method">
        <text class="payment-label">支付方式</text>
        <view class="payment-option">
          <icon type="success" size="18" color="#07c160"></icon>
          <text>{{paymentMethod}}</text>
        </view>
      </view>

      <!-- 订单备注 -->
      <view class="order-remark" bindtap="showRemarkInput">
        <text class="remark-label">订单备注</text>
        <view class="remark-content">
          <text>{{orderRemark || '备注建议提前协商（250字以内）'}}</text>
        </view>
        <view class="remark-arrow">></view>
      </view>

      <!-- 订单总价 -->
      <view class="order-total">
        <text class="total-label">总价¥{{sizes[selectedSizeIndex].price}}</text>
      </view>

      <!-- 提交订单按钮 -->
      <view class="order-submit">
        <button class="submit-btn" bindtap="submitOrder">微信支付¥{{sizes[selectedSizeIndex].price * quantity}}</button>
      </view>
    </view>

    <!-- 提货人选择弹窗 -->
    <view class="receiver-popup-mask" wx:if="{{showReceiverSelector}}" bindtap="hideReceiverInput"></view>
    <view class="receiver-popup {{showReceiverSelector ? 'show' : ''}}">
      <view class="receiver-popup-header">
        <text class="receiver-popup-title">添加提货人</text>
        <view class="receiver-popup-close" bindtap="hideReceiverInput">×</view>
      </view>
      <form bindsubmit="setReceiver">
        <view class="receiver-form">
          <view class="form-item">
            <text class="form-label">姓名</text>
            <input name="name" class="form-input" placeholder="请输入姓名" value="{{receiver.name}}" />
          </view>
          <view class="form-item">
            <text class="form-label">手机号</text>
            <input name="phone" class="form-input" placeholder="请输入手机号" type="number" value="{{receiver.phone}}" />
          </view>
        </view>
        <button form-type="submit" class="receiver-confirm-btn">确定</button>
      </form>
    </view>

    <!-- 提货时间选择弹窗 -->
    <view class="time-popup-mask" wx:if="{{showTimeSelector}}" bindtap="hideTimeSelector"></view>
    <view class="time-popup {{showTimeSelector ? 'show' : ''}}">
      <view class="time-popup-header">
        <text class="time-popup-title">选择提货时间</text>
        <view class="time-popup-close" bindtap="hideTimeSelector">×</view>
      </view>
      <view class="time-list">
        <view class="time-item" bindtap="selectDeliveryTime" data-time="今天 12:00-14:00">
          <text>今天 12:00-14:00</text>
        </view>
        <view class="time-item" bindtap="selectDeliveryTime" data-time="今天 14:00-16:00">
          <text>今天 14:00-16:00</text>
        </view>
        <view class="time-item" bindtap="selectDeliveryTime" data-time="今天 16:00-18:00">
          <text>今天 16:00-18:00</text>
        </view>
        <view class="time-item" bindtap="selectDeliveryTime" data-time="明天 10:00-12:00">
          <text>明天 10:00-12:00</text>
        </view>
        <view class="time-item" bindtap="selectDeliveryTime" data-time="明天 12:00-14:00">
          <text>明天 12:00-14:00</text>
        </view>
      </view>
    </view>

    <!-- 备注输入弹窗 -->
    <view class="remark-popup-mask" wx:if="{{showRemarkInput}}" bindtap="hideRemarkInput"></view>
    <view class="remark-popup {{showRemarkInput ? 'show' : ''}}">
      <view class="remark-popup-header">
        <text class="remark-popup-title">订单备注</text>
        <view class="remark-popup-close" bindtap="hideRemarkInput">×</view>
      </view>
      <view class="remark-form">
        <textarea class="remark-textarea" placeholder="备注建议提前协商（250字以内）" maxlength="250" bindinput="setOrderRemark" value="{{orderRemark}}"></textarea>
      </view>
      <button class="remark-confirm-btn" bindtap="hideRemarkInput">确定</button>
    </view>
  </block>
</view>