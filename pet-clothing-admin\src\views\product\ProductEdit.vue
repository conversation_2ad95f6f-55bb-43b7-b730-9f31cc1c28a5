<template>
  <div class="product-edit-container">
    <el-card class="form-card" shadow="never" v-loading="loading || submitting">
      <template #header>
        <div class="card-header">
          <span>编辑商品</span>
        </div>
      </template>

      <el-form
        ref="productFormRef"
        :model="productForm"
        :rules="productRules"
        label-width="120px"
        label-position="right"
      >
        <!-- 基本信息 -->
        <el-divider content-position="left">基本信息</el-divider>

        <el-form-item label="商品ID">
          <el-input v-model="productForm.id" disabled />
        </el-form-item>

        <el-form-item label="商品名称" prop="name">
          <el-input v-model="productForm.name" placeholder="请输入商品名称" />
        </el-form-item>

        <el-form-item label="商品子分类" prop="subcategoryId">
          <el-select v-model="productForm.subcategoryId" placeholder="请选择商品子分类" style="width: 100%">
             <!-- 分类选项从API获取 -->
            <el-option
              v-for="item in categoryOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="商品原价" prop="originalPrice">
              <el-input-number
                v-model="productForm.originalPrice"
                :min="0"
                :precision="2"
                :step="0.1"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
             <el-form-item label="折扣率" prop="discountRate">
              <el-input-number
                v-model="productForm.discountRate"
                :min="0"
                :max="1"
                :precision="2"
                :step="0.01"
                style="width: 100%"
              />
             </el-form-item>
          </el-col>
        </el-row>

        <!-- SKU信息 -->
        <el-divider content-position="left">SKU信息</el-divider>
        <el-form-item label="SKU列表">
          <div class="sku-container">
            <!-- SKU操作按钮 -->
            <div class="sku-actions">
              <el-button type="primary" size="small" @click="addSku">添加SKU</el-button>
              <span class="sku-count">共 {{ productForm.skus.length }} 个SKU</span>
            </div>

            <!-- SKU表格 -->
            <el-table
              :data="productForm.skus"
              border
              class="sku-table"
              :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
            >
              <!-- 显示名称 -->
              <el-table-column prop="displayName" label="SKU名称" width="150" show-overflow-tooltip>
                <template #default="{ row }">
                  <el-tag v-if="row.displayName" type="info" size="small">{{ row.displayName }}</el-tag>
                  <span v-else class="text-placeholder">未设置</span>
                </template>
              </el-table-column>

              <!-- 动态属性列 -->
              <el-table-column label="属性" min-width="200">
                <template #default="{ row, $index }">
                  <div class="attributes-container">
                    <div
                      v-for="(value, key) in row.attributes"
                      :key="key"
                      class="attribute-item"
                    >
                      <el-tag size="small" class="attribute-key">{{ key }}</el-tag>
                      <el-input
                        v-model="row.attributes[key]"
                        size="small"
                        class="attribute-value"
                        @input="updateDisplayName($index)"
                      />
                    </div>
                  </div>
                </template>
              </el-table-column>

              <!-- 价格 -->
              <el-table-column prop="price" label="原价" width="180">
                <template #default="{ row, $index }">
                  <el-input-number
                    v-model="row.price"
                    :min="0"
                    :precision="2"
                    :step="0.1"
                    size="small"
                    style="width: 100%"
                    @change="updateDiscountedPrice($index)"
                  />
                </template>
              </el-table-column>

              <!-- 折后价 -->
              <el-table-column prop="discountedPrice" label="折后价" width="180">
                <template #default="{ row }">
                  <el-input-number
                    v-model="row.discountedPrice"
                    :precision="2"
                    size="small"
                    style="width: 100%"
                    disabled
                  />
                </template>
              </el-table-column>

              <!-- 库存 -->
              <el-table-column prop="stock" label="库存" width="140">
                <template #default="{ row }">
                  <el-input-number
                    v-model="row.stock"
                    :min="0"
                    :precision="0"
                    :step="1"
                    size="small"
                    style="width: 100%"
                  />
                </template>
              </el-table-column>

              <!-- SKU编码 -->
              <el-table-column prop="skuCode" label="SKU编码" width="150" show-overflow-tooltip>
                <template #default="{ row }">
                  <el-input
                    v-model="row.skuCode"
                    size="small"
                    placeholder="自动生成"
                    disabled
                  />
                </template>
              </el-table-column>

              <!-- 操作 -->
              <el-table-column label="操作" width="80" fixed="right">
                <template #default="{ $index }">
                  <el-button
                    type="danger"
                    size="small"
                    link
                    @click="removeSku($index)"
                    :disabled="productForm.skus.length <= 1"
                  >
                    删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-form-item>

        <!-- 商品图片 -->
        <el-divider content-position="left">商品图片</el-divider>

        <el-form-item label="主图" prop="imageUrl">
          <el-upload
            class="product-image-uploader"
            action="/api/admin/upload/image"
            :headers="uploadHeaders"
            :show-file-list="false"
            :on-success="handleMainImageSuccess"
            :before-upload="beforeImageUpload"
          >
            <img v-if="productForm.imageUrl" :src="productForm.imageUrl" class="product-image" />
            <el-icon v-else class="product-image-uploader-icon"><Plus /></el-icon>
          </el-upload>
        </el-form-item>

        <el-form-item label="详情图集" prop="detailImages">
          <el-upload
            class="product-images-uploader"
            action="/api/admin/upload/image"
            :headers="uploadHeaders"
            list-type="picture-card"
            :file-list="detailImagesFileList"
            :on-success="handleImagesSuccess"
            :on-remove="handleImagesRemove"
            :before-upload="beforeImageUpload"
          >
            <el-icon><Plus /></el-icon>
          </el-upload>
        </el-form-item>

        <!-- 商品详情 -->
        <el-divider content-position="left">商品详情</el-divider>

        <el-form-item label="商品描述" prop="description">
          <el-input
            v-model="productForm.description"
            type="textarea"
            :rows="6"
            placeholder="请输入商品描述"
          />
        </el-form-item>

        <!-- 其他设置 -->
        <el-divider content-position="left">其他设置</el-divider>

        <el-form-item label="商品状态">
          <el-switch
            v-model="productForm.status"
            active-value="上架"
            inactive-value="下架"
            active-text="上架"
            inactive-text="下架"
          />
        </el-form-item>

         <el-form-item label="是否热门">
           <el-switch
            v-model="productForm.isHot"
            :active-value="true"
            :inactive-value="false"
            active-text="是"
            inactive-text="否"
          />
        </el-form-item>

         <el-form-item label="是否推荐">
           <el-switch
            v-model="productForm.isRecommended"
            :active-value="true"
            :inactive-value="false"
            active-text="是"
            inactive-text="否"
          />
        </el-form-item>


        <el-form-item label="排序值" prop="sort">
          <el-input-number
            v-model="productForm.sort"
            :min="0"
            :precision="0"
            :step="1"
            style="width: 200px"
          />
          <div class="form-item-tip">数值越小排序越靠前</div>
        </el-form-item>

        <!-- 提交按钮 -->
        <el-form-item>
          <el-button type="primary" :loading="submitting" @click="submitForm">保存</el-button>
          <el-button @click="goBack">取消</el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import { getProductDetail, updateProduct, updateProductSku } from '@/api/product'
import { getCategoryList } from '@/api/category'

const router = useRouter()
const route = useRoute()
const productFormRef = ref(null)
const loading = ref(false)
const submitting = ref(false)
const categoryOptions = ref([])

// 上传图片的请求头
const uploadHeaders = computed(() => {
  const token = localStorage.getItem('token')
  // 根据您最新的请求头格式要求，这里使用token作为key
  return {
    'token': token
  }
})

// 商品表单
const productForm = reactive({
  id: '',
  name: '',
  subcategoryId: '',
  originalPrice: 0,
  discountRate: 1,
  discountedPrice: 0,
  imageUrl: '',
  detailImages: [],
  description: '',
  status: '上架',
  isHot: false,
  isRecommended: false,
  sort: 0,
  totalStock: 0,
  skus: [{
    id: '',
    attributes: {
      '尺码': '',
      '颜色': ''
    },
    displayName: '',
    stock: 0,
    skuCode: '',
    price: 0,
    discountedPrice: 0
  }]
})

// 详情图的fileList格式，用于el-upload
const detailImagesFileList = computed(() => {
    return productForm.detailImages.map(url => ({url: url, name: url.substring(url.lastIndexOf('/') + 1) }))
})

// 表单验证规则
const productRules = {
   name: [
    { required: true, message: '请输入商品名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  subcategoryId: [
    { required: true, message: '请选择商品子分类', trigger: 'change' }
  ],
  originalPrice: [
    { required: true, message: '请输入商品原价', trigger: 'blur' },
    { type: 'number', min: 0.01, message: '商品原价必须大于0', trigger: 'blur' }
  ],
   discountRate: [
    { required: true, message: '请输入折扣率', trigger: 'blur' },
    { type: 'number', min: 0, max: 1, message: '折扣率必须在 0 到 1 之间', trigger: 'blur' }
  ],
  imageUrl: [
    { required: true, message: '请上传商品主图', trigger: 'change' }
  ],
  description: [
    { required: true, message: '请输入商品描述', trigger: 'blur' }
  ],
   stock: [
     { required: true, message: '请输入SKU库存', trigger: 'blur' },
     { type: 'number', min: 0, message: '库存不能小于0', trigger: 'blur' }
  ],
  skuPrice: [
     { required: true, message: '请输入SKU价格', trigger: 'blur' },
     { type: 'number', min: 0.01, message: 'SKU价格必须大于0', trigger: 'blur' }
  ]
}

// SKU管理方法
// 添加SKU
const addSku = () => {
  productForm.skus.push({
    id: '',
    attributes: {
      '尺码': '',
      '颜色': ''
    },
    displayName: '',
    stock: 0,
    skuCode: '',
    price: 0,
    discountedPrice: 0
  })
}

// 删除SKU
const removeSku = (index) => {
  if (productForm.skus.length > 1) {
    productForm.skus.splice(index, 1)
  }
}

// 更新显示名称
const updateDisplayName = (skuIndex) => {
  const sku = productForm.skus[skuIndex]
  const attributeValues = Object.values(sku.attributes).filter(v => v.trim())
  sku.displayName = attributeValues.join('-') || '未设置'
}

// 更新折后价
const updateDiscountedPrice = (skuIndex) => {
  const sku = productForm.skus[skuIndex]
  sku.discountedPrice = Number((sku.price * productForm.discountRate).toFixed(2))
}

// 监听折扣率变化，更新所有SKU的折后价
watch(() => productForm.discountRate, (newRate) => {
  productForm.skus.forEach((sku, index) => {
    updateDiscountedPrice(index)
  })
})

// 获取商品详情
const loadProductDetail = async (id) => {
  loading.value = true
  try {
    const response = await getProductDetail(id)
    if (response.code === 1 && response.data) {
      const productData = response.data
      // 更新表单数据
      Object.keys(productForm).forEach(key => {
        if (productData[key] !== undefined) {
          if (key === 'skus') {
            // 确保SKU数据的属性结构正确，并处理动态属性
            productForm.skus = productData.skus.map(sku => ({
              ...sku,
              attributes: sku.attributes || { '尺码': '', '颜色': '' },
              displayName: sku.displayName || generateDisplayName(sku.attributes)
            }))
          } else {
            productForm[key] = productData[key]
          }
        }
      })
    } else {
      ElMessage.error(response.msg || '获取商品详情失败')
    }
  } catch (error) {
    console.error('获取商品详情失败:', error)
    ElMessage.error('获取商品详情失败')
  } finally {
    loading.value = false
  }
}

// 生成显示名称的辅助函数
const generateDisplayName = (attributes) => {
  const values = Object.values(attributes).filter(v => v && v.trim())
  return values.join('-') || '未设置'
}

// 获取分类列表
const fetchCategories = async () => {
  try {
    const response = await getCategoryList()
    if (response.code === 1) {
      categoryOptions.value = response.data.map(item => ({
        value: item.id,
        label: item.name
      }))
    } else {
      ElMessage.error(response.msg || '获取分类列表失败')
    }
  } catch (error) {
    console.error('获取分类列表失败:', error)
    ElMessage.error('获取分类列表失败')
  }
}

// 监听路由参数变化
watch(
  () => route.params.id,
  (newId) => {
    if (newId) {
      loadProductDetail(newId)
    }
  }
)

// 提交表单
const submitForm = async () => {
  if (!productFormRef.value) return

  await productFormRef.value.validate(async (valid) => {
    if (valid) {
      submitting.value = true
      try {
        // 准备提交数据
        const submitData = {
          ...productForm,
          skus: productForm.skus.map(sku => ({
            ...sku,
            displayName: `${sku.attributes.尺码}-${sku.attributes.颜色}`,
            discountedPrice: sku.price * productForm.discountRate
          }))
        }

        const response = await updateProduct(submitData)
        if (response.code === 1) {
          // 商品信息更新成功后，批量更新SKU
          const skuUpdateResults = await Promise.all(productForm.skus.map(sku =>
            updateProductSku({
              id: sku.id,
              attributes: sku.attributes,
              price: sku.price,
              skuCode: sku.skuCode,
              stock: sku.stock
            })
          ))
          const allSuccess = skuUpdateResults.every(res => res.code === 1)
          if (allSuccess) {
            ElMessage.success('商品及所有SKU更新成功')
            router.push('/product')
          } else {
            ElMessage.error('部分SKU更新失败')
          }
        } else {
          ElMessage.error(response.msg || '商品更新失败')
        }
      } catch (error) {
        console.error('商品更新失败:', error)
        ElMessage.error('商品更新失败')
      } finally {
        submitting.value = false
      }
    }
  })
}

// 返回列表页
const goBack = () => {
  router.back()
}

// 组件挂载时初始化
onMounted(async () => {
  await fetchCategories()
  if (route.params.id) {
    await loadProductDetail(route.params.id)
  }
})

// 处理主图上传成功
const handleMainImageSuccess = (response) => {
  if (response.code === 1 && response.data) {
    productForm.imageUrl = `${BASE_URL}${response.data}`
    productFormRef.value.validateField('imageUrl')
  } else {
    ElMessage.error('主图上传失败')
  }
}

// 处理详情图上传成功
const handleImagesSuccess = (response) => {
  if (response.code === 1 && response.data) {
    productForm.detailImages.push(`${BASE_URL}${response.data}`)
  } else {
    ElMessage.error('详情图上传失败')
  }
}

// 处理详情图移除
const handleImagesRemove = (uploadFile) => {
  const index = productForm.detailImages.findIndex(url => url === uploadFile.url)
  if (index !== -1) {
    productForm.detailImages.splice(index, 1)
  }
}

// 上传图片前的检查
const beforeImageUpload = (rawFile) => {
  const isJPG = rawFile.type === 'image/jpeg' || rawFile.type === 'image/png' || rawFile.type === 'image/gif'
  const isLt2M = rawFile.size / 1024 / 1024 < 2

  if (!isJPG) {
    ElMessage.error('图片只能是 JPG/PNG/GIF 格式!')
    return false
  }
  if (!isLt2M) {
    ElMessage.error('图片大小不能超过 2MB!')
    return false
  }
  return true
}

</script>

<style scoped>
.product-edit-container {
  padding: 20px;
}

.form-card {
  max-width: 800px;
  margin: 0 auto;
}

.product-image-uploader .el-upload {
  border: 1px dashed var(--el-border-color-darker);
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: var(--el-transition-duration-fast);
}

.product-image-uploader .el-upload:hover {
  border-color: var(--el-color-primary);
}

.el-icon.product-image-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  text-align: center;
}

.product-image {
  width: 178px;
  height: 178px;
  display: block;
  object-fit: cover;
}

.form-item-tip {
  color: #909399;
  font-size: 12px;
  line-height: 1.5;
  margin-top: 4px;
}

/* SKU相关样式 */
.sku-container {
  width: 100%;
}

.sku-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 12px 16px;
  background: #f8f9fa;
  border-radius: 6px;
}

.sku-count {
  color: #606266;
  font-size: 14px;
}

.sku-table {
  width: 100%;
  margin-bottom: 16px;
}

.sku-table .el-table__cell {
  padding: 8px 0;
}

.attributes-container {
  display: flex;
  flex-direction: column;
  gap: 8px;
  min-width: 180px;
}

.attribute-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.attribute-key {
  min-width: 50px;
  text-align: center;
  background: #e1f3d8;
  color: #67c23a;
  border: 1px solid #b3d8a4;
}

.attribute-value {
  flex: 1;
  min-width: 80px;
}

.add-attribute-btn {
  color: #409eff;
  font-size: 12px;
  padding: 4px 8px;
  align-self: flex-start;
}

.add-attribute-btn:hover {
  background: #ecf5ff;
}

.text-placeholder {
  color: #c0c4cc;
  font-style: italic;
  font-size: 12px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .sku-table {
    font-size: 12px;
  }

  .attribute-item {
    flex-direction: column;
    align-items: stretch;
    gap: 4px;
  }

  .attribute-key {
    min-width: auto;
    text-align: left;
  }
}

@media (max-width: 768px) {
  .form-card {
    max-width: 100%;
    margin: 0;
  }

  .sku-actions {
    flex-direction: column;
    gap: 8px;
    align-items: stretch;
  }

  .sku-table .el-table__cell {
    padding: 4px 0;
  }

  .attributes-container {
    min-width: 120px;
  }
}
</style>
