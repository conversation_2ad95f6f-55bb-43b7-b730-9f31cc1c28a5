import { defineStore } from 'pinia'
import { login, getInfo, logout } from '@/api/user'

export const useUserStore = defineStore('user', {
  state: () => ({
    token: localStorage.getItem('token') || '',
    userInfo: JSON.parse(localStorage.getItem('userInfo') || '{}'),
    roles: []
  }),
  
  getters: {
    isLoggedIn: (state) => !!state.token,
    username: (state) => state.userInfo.username || ''
  },
  
  actions: {
    // 登录
    async login(userInfo) {
      try {
        const { username, password } = userInfo
        const response = await login({ username: username.trim(), password })
        
        if (response.code === 1) {
          const { token, ...userData } = response.data
          this.token = token
          this.userInfo = userData
          localStorage.setItem('token', token)
          localStorage.setItem('userInfo', JSON.stringify(userData))
          
          return Promise.resolve(response)
        } else {
          return Promise.reject(response.msg || '登录失败')
        }
      } catch (error) {
        return Promise.reject(error)
      }
    },
    
    // 获取用户信息
    async getUserInfo() {
      try {
        const response = await getInfo()
        if (response.code === 1) {
          const { data } = response
          this.userInfo = data
          this.roles = data.role ? [data.role] : []
          localStorage.setItem('userInfo', JSON.stringify(data))
          return Promise.resolve(data)
        } else {
          return Promise.reject(response.msg || '获取用户信息失败')
        }
      } catch (error) {
        return Promise.reject(error)
      }
    },
    
    // 退出登录
    async logout() {
      try {
        await logout()
        this.resetState()
        return Promise.resolve()
      } catch (error) {
        return Promise.reject(error)
      }
    },
    
    // 重置状态
    resetState() {
      this.token = ''
      this.userInfo = {}
      this.roles = []
      
      localStorage.removeItem('token')
      localStorage.removeItem('userInfo')
    }
  }
})
