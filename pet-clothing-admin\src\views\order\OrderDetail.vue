<template>
  <div class="order-detail-container" v-loading="loading">
    <!-- 操作区域 -->
    <el-card class="operation-card" shadow="never">
      <div class="operation-header">
        <div class="order-title">
          <span>订单详情</span>
          <el-tag :type="getOrderStatusType(orderInfo.status)" class="status-tag">
            {{ getOrderStatusText(orderInfo.status) }}
          </el-tag>
        </div>
        <div class="operation-buttons">
          <el-button @click="goBack">返回</el-button>
          <el-button
            v-if="orderInfo.status === '已支付'"
            type="primary"
            @click="handleShip"
          >
            发货
          </el-button>
          <el-button
            v-if="orderInfo.status === '待付款'"
            type="danger"
            @click="handleCancel"
          >
            取消订单
          </el-button>
          <el-button
            v-if="orderInfo.status === '退款中'"
            type="warning"
            @click="handleViewRefund"
          >
            查看退款申请
          </el-button>
        </div>
      </div>
    </el-card>

    <!-- 订单基本信息 -->
    <el-card class="info-card" shadow="never">
      <template #header>
        <div class="card-header">
          <span>订单信息</span>
        </div>
      </template>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="订单编号">{{ orderInfo.orderNumber }}</el-descriptions-item>
        <el-descriptions-item label="下单时间">{{ orderInfo.createdAt }}</el-descriptions-item>
        <el-descriptions-item label="用户名">{{ orderInfo.userName }}</el-descriptions-item>
        <el-descriptions-item label="支付状态">{{ orderInfo.paymentStatus || '-' }}</el-descriptions-item>
        <el-descriptions-item label="更新时间">{{ orderInfo.updatedAt || '-' }}</el-descriptions-item>
        <el-descriptions-item label="订单备注">{{ orderInfo.remark || '-' }}</el-descriptions-item>
        <el-descriptions-item v-if="orderInfo.transactionId" label="交易单号">{{ orderInfo.transactionId }}</el-descriptions-item>
      </el-descriptions>
    </el-card>

    <!-- 收货信息 -->
    <el-card class="info-card" shadow="never">
      <template #header>
        <div class="card-header">
          <span>收货信息</span>
        </div>
      </template>
      <el-descriptions :column="1" border>
        <el-descriptions-item label="收货人">{{ orderInfo.consignee }}</el-descriptions-item>
        <el-descriptions-item label="联系电话">{{ orderInfo.phone }}</el-descriptions-item>
        <el-descriptions-item label="用户电话">{{ orderInfo.userPhone }}</el-descriptions-item>
        <el-descriptions-item label="收货地址">{{ orderInfo.address }}</el-descriptions-item>
      </el-descriptions>
    </el-card>

    <!-- 物流信息 -->
    <el-card class="info-card" shadow="never" v-if="orderInfo.shipment && (orderInfo.shipment.shippingCompany || orderInfo.shipment.trackingNumber)">
      <template #header>
        <div class="card-header">
          <span>物流信息</span>
        </div>
      </template>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="物流公司">{{ getLogisticsCompanyText(orderInfo.shipment.shippingCompany) || '-' }}</el-descriptions-item>
        <el-descriptions-item label="物流单号">{{ orderInfo.shipment.trackingNumber || '-' }}</el-descriptions-item>
        <el-descriptions-item label="发货时间">{{ orderInfo.shipment.shippingDate || '-' }}</el-descriptions-item>
        <el-descriptions-item label="物流状态">{{ orderInfo.shipment.shippingStatus || '-' }}</el-descriptions-item>
      </el-descriptions>
    </el-card>

    <!-- 商品信息 -->
    <el-card class="info-card" shadow="never">
      <template #header>
        <div class="card-header">
          <span>商品信息</span>
        </div>
      </template>
      <el-table :data="orderInfo.orderItems" border style="width: 100%">
        <el-table-column label="商品图片" width="100">
          <template #default="scope">
            <el-image
              :src="scope.row.imageUrl"
              :preview-src-list="[scope.row.imageUrl]"
              fit="cover"
              style="width: 60px; height: 60px"
            />
          </template>
        </el-table-column>
        <el-table-column prop="productName" label="商品名称" min-width="200" show-overflow-tooltip />
        <el-table-column label="规格" width="150">
          <template #default="scope">
            <div>
              <div v-if="scope.row.color">颜色：{{ scope.row.color }}</div>
              <div v-if="scope.row.size">尺寸：{{ scope.row.size }}</div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="price" label="单价" width="120">
          <template #default="scope">
            ¥{{ (scope.row.price ?? 0).toFixed(2) }}
          </template>
        </el-table-column>
        <el-table-column prop="quantity" label="数量" width="100" />
        <el-table-column prop="subtotal" label="小计" width="120">
          <template #default="scope">
            ¥{{ (scope.row.subtotal ?? 0).toFixed(2) }}
          </template>
        </el-table-column>
      </el-table>
      <div class="order-amount">
        <div class="amount-item total">
          <span>订单总价：</span>
          <span class="total-price">¥{{ (orderInfo.totalAmount ?? 0).toFixed(2) }}</span>
        </div>
      </div>
    </el-card>



    <!-- 发货对话框 -->
    <el-dialog
      v-model="shipDialogVisible"
      title="订单发货"
      width="500px"
    >
      <el-form :model="shipForm" label-width="100px">
        <el-form-item label="订单号">
          <el-input v-model="shipForm.orderNo" disabled />
        </el-form-item>
        <el-form-item label="物流公司">
          <el-select v-model="shipForm.logisticsCompany" placeholder="请选择物流公司" style="width: 100%">
            <el-option label="顺丰速运" value="SF" />
            <el-option label="中通快递" value="ZTO" />
            <el-option label="圆通速递" value="YTO" />
            <el-option label="韵达快递" value="YD" />
            <el-option label="申通快递" value="STO" />
            <el-option label="京东物流" value="JD" />
          </el-select>
        </el-form-item>
        <el-form-item label="物流单号">
          <el-input v-model="shipForm.trackingNumber" placeholder="请输入物流单号" />
        </el-form-item>
        <el-form-item label="备注">
          <el-input
            v-model="shipForm.remark"
            type="textarea"
            :rows="3"
            placeholder="请输入备注信息"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="shipDialogVisible = false">取消</el-button>
          <el-button type="primary" :loading="submitting" @click="confirmShip">确认发货</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 退款申请详情对话框 -->
    <el-dialog
      v-model="refundDialogVisible"
      title="退款申请详情"
      width="800px"
    >
      <div v-loading="refundLoading" class="refund-detail">
        <div v-if="refundDetail" class="refund-info">
          <!-- 基本信息 -->
          <el-card class="info-card" shadow="never">
            <template #header>
              <span class="card-title">退款申请信息</span>
            </template>
            <el-descriptions :column="2" border>
              <el-descriptions-item label="退款单号">{{ refundDetail.refundNumber }}</el-descriptions-item>
              <el-descriptions-item label="订单号">{{ refundDetail.orderNumber }}</el-descriptions-item>
              <el-descriptions-item label="申请时间">{{ refundDetail.createdAt }}</el-descriptions-item>
              <el-descriptions-item label="退款状态">
                <el-tag :type="getRefundStatusType(refundDetail.status)">{{ refundDetail.status }}</el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="用户名">{{ refundDetail.userName }}</el-descriptions-item>
              <el-descriptions-item label="用户电话">{{ refundDetail.userPhone }}</el-descriptions-item>
              <el-descriptions-item label="订单金额">¥{{ refundDetail.orderAmount.toFixed(2) }}</el-descriptions-item>
              <el-descriptions-item label="退款金额">¥{{ refundDetail.refundAmount.toFixed(2) }}</el-descriptions-item>
              <el-descriptions-item label="退款原因" :span="2">{{ refundDetail.reason }}</el-descriptions-item>
              <el-descriptions-item v-if="refundDetail.adminRemark" label="管理员备注" :span="2">{{ refundDetail.adminRemark }}</el-descriptions-item>
            </el-descriptions>
          </el-card>

          <!-- 商品信息 -->
          <el-card class="info-card" shadow="never">
            <template #header>
              <span class="card-title">商品信息</span>
            </template>
            <div class="product-info">
              <el-image
                :src="refundDetail.firstOrderItem.imageUrl"
                fit="cover"
                class="product-image"
              />
              <div class="product-details">
                <div class="product-name">{{ refundDetail.firstOrderItem.productName }}</div>
                <div class="product-specs">
                  <span v-if="refundDetail.firstOrderItem.color">颜色：{{ refundDetail.firstOrderItem.color }}</span>
                  <span v-if="refundDetail.firstOrderItem.size">尺寸：{{ refundDetail.firstOrderItem.size }}</span>
                </div>
                <div class="product-price">
                  单价：¥{{ refundDetail.firstOrderItem.price.toFixed(2) }} × {{ refundDetail.firstOrderItem.quantity }}
                </div>
                <div class="product-total">
                  小计：¥{{ refundDetail.firstOrderItem.subtotal.toFixed(2) }}
                </div>
                <div v-if="refundDetail.totalItems > 1" class="more-items">
                  还有 {{ refundDetail.totalItems - 1 }} 件其他商品
                </div>
              </div>
            </div>
          </el-card>

          <!-- 审核操作 -->
          <el-card v-if="refundDetail.status === '待审核'" class="info-card" shadow="never">
            <template #header>
              <span class="card-title">审核退款申请</span>
            </template>
            <el-form :model="refundHandleForm" label-width="120px">
              <el-form-item label="审核结果">
                <el-radio-group v-model="refundHandleForm.approved">
                  <el-radio :value="true">同意退款</el-radio>
                  <el-radio :value="false">拒绝退款</el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item v-if="refundHandleForm.approved" label="实际退款金额" required>
                <el-input-number
                  v-model="refundHandleForm.actualRefundAmount"
                  :min="0.01"
                  :max="refundDetail.orderAmount"
                  :precision="2"
                  :step="0.01"
                  style="width: 200px"
                  placeholder="请输入实际退款金额"
                />
                <span class="amount-hint">（最大可退款：¥{{ refundDetail.orderAmount.toFixed(2) }}）</span>
              </el-form-item>
              <el-form-item label="管理员备注" required>
                <el-input
                  v-model="refundHandleForm.adminRemark"
                  type="textarea"
                  :rows="3"
                  placeholder="请输入审核备注"
                />
              </el-form-item>
            </el-form>
          </el-card>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="refundDialogVisible = false">关闭</el-button>
          <el-button
            v-if="refundDetail && refundDetail.status === '待审核'"
            type="primary"
            :loading="refundSubmitting"
            @click="handleRefund"
          >
            提交审核
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 订单评论卡片 -->
    <el-card class="info-card" shadow="never" style="margin-top: 20px;">
      <template #header>
        <div class="card-header">
          <span>订单评论</span>
          <span v-if="reviewsTotal > 0" class="review-count">共 {{ reviewsTotal }} 条评论</span>
        </div>
      </template>

      <div v-loading="reviewsLoading">
        <div v-if="reviewsList.length > 0" class="reviews-list">
          <div
            v-for="review in reviewsList"
            :key="review.id"
            class="review-item"
          >
            <div class="review-header">
              <div class="review-user">
                <span class="user-name">{{ review.userName || '匿名用户' }}</span>
                <div class="review-rating">
                  <el-rate
                    v-model="review.rating"
                    disabled
                    show-score
                    text-color="#ff9900"
                    score-template="{value} 分"
                  />
                </div>
              </div>
              <div class="review-actions">
                <span class="review-time">{{ review.createdAt }}</span>
                <el-button
                  type="danger"
                  size="small"
                  plain
                  @click="handleDeleteReview(review.id, review.content)"
                >
                  删除
                </el-button>
              </div>
            </div>
            <div class="review-content">
              {{ review.content }}
            </div>
            <div v-if="review.images && review.images.length > 0" class="review-images">
              <el-image
                v-for="(image, index) in review.images"
                :key="index"
                :src="image"
                fit="cover"
                class="review-image"
                :preview-src-list="review.images"
                :initial-index="index"
              />
            </div>
          </div>
        </div>

        <el-empty v-else description="该订单暂无评论" />

        <!-- 分页 -->
        <div v-if="reviewsTotal > reviewsPageSize" class="review-pagination">
          <el-pagination
            v-model:current-page="reviewsPage"
            :page-size="reviewsPageSize"
            :total="reviewsTotal"
            layout="prev, pager, next, total"
            @current-change="handleReviewPageChange"
          />
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getOrderDetail, getRefundRequests, reviewRefundRequest } from '@/api/order'
import { getOrderReviews, deleteReview } from '@/api/review'

const router = useRouter()
const route = useRoute()
const orderId = ref(route.params.id)
const loading = ref(false)
const submitting = ref(false)
const shipDialogVisible = ref(false)

// 退款相关
const refundDialogVisible = ref(false)
const refundLoading = ref(false)
const refundSubmitting = ref(false)
const refundDetail = ref(null)

// 评论相关
const reviewsLoading = ref(false)
const reviewsList = ref([])
const reviewsTotal = ref(0)
const reviewsPage = ref(1)
const reviewsPageSize = ref(10)

// 发货表单
const shipForm = reactive({
  orderNo: '',
  logisticsCompany: '',
  trackingNumber: '',
  remark: ''
})

// 退款处理表单
const refundHandleForm = reactive({
  approved: true,
  actualRefundAmount: 0,
  adminRemark: ''
})

// 订单信息
const orderInfo = ref({
  id: '',
  orderNumber: '',
  status: '',
  totalAmount: 0,
  paymentStatus: '',
  createdAt: '',
  updatedAt: '',
  remark: '',
  transactionId: '',
  userId: '',
  userName: '',
  userPhone: '',
  phone: '',
  address: '',
  consignee: '',
  orderItems: [],
  shipment: {
    shippingCompany: null,
    trackingNumber: null,
    shippingStatus: '',
    shippingDate: null
  }
})



// 获取订单状态类型
const getOrderStatusType = (status) => {
  switch (status) {
    case '待付款':
      return 'info'
    case '已支付':
      return 'warning'
    case '已发货':
      return 'primary'
    case '已完成':
      return 'success'
    case '已取消':
      return 'danger'
    case '已退款':
      return 'info'
    case '退款中':
      return 'warning'
    default:
      return 'info'
  }
}

// 获取订单状态文本
const getOrderStatusText = (status) => {
  return status || '未知'
}

// 获取退款状态类型
const getRefundStatusType = (status) => {
  switch (status) {
    case '待审核':
      return 'warning'
    case '已同意':
      return 'success'
    case '已拒绝':
      return 'danger'
    default:
      return 'info'
  }
}

// 格式化评分显示
const formatRating = (rating) => {
  return Number(rating || 0).toFixed(1)
}



// 获取物流公司文本
const getLogisticsCompanyText = (company) => {
  switch (company) {
    case 'SF':
      return '顺丰速运'
    case 'ZTO':
      return '中通快递'
    case 'YTO':
      return '圆通速递'
    case 'YD':
      return '韵达快递'
    case 'STO':
      return '申通快递'
    case 'JD':
      return '京东物流'
    default:
      return company
  }
}

// 返回上一页
const goBack = () => {
  router.back()
}

// 发货
const handleShip = () => {
  shipForm.orderNo = orderInfo.value.orderNumber
  shipForm.logisticsCompany = ''
  shipForm.trackingNumber = ''
  shipForm.remark = ''
  shipDialogVisible.value = true
}

// 确认发货
const confirmShip = async () => {
  if (!shipForm.logisticsCompany) {
    ElMessage.warning('请选择物流公司')
    return
  }
  if (!shipForm.trackingNumber) {
    ElMessage.warning('请输入物流单号')
    return
  }

  submitting.value = true
  try {
    // 这里应该调用API进行发货操作
    setTimeout(() => {
      ElMessage.success('发货成功')
      shipDialogVisible.value = false
      fetchOrderDetail()
      submitting.value = false
    }, 500)
  } catch (error) {
    console.error('发货失败:', error)
    ElMessage.error('发货失败')
    submitting.value = false
  }
}

// 取消订单
const handleCancel = () => {
  ElMessageBox.confirm('确定要取消该订单吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      // 这里应该调用API取消订单
      ElMessage.success('订单已取消')
      fetchOrderDetail()
    } catch (error) {
      console.error('取消订单失败:', error)
      ElMessage.error('取消订单失败')
    }
  }).catch(() => {})
}

// 查看退款申请
const handleViewRefund = async () => {
  refundLoading.value = true
  refundDialogVisible.value = true
  refundDetail.value = null

  try {
    const response = await getRefundRequests({ orderNumber: orderInfo.value.orderNumber })
    if (response.code === 1 && response.data.records.length > 0) {
      refundDetail.value = response.data.records[0]
      // 重置处理表单
      refundHandleForm.approved = true
      refundHandleForm.actualRefundAmount = refundDetail.value.refundAmount
      refundHandleForm.adminRemark = ''
    } else {
      ElMessage.error('未找到退款申请信息')
      refundDialogVisible.value = false
    }
  } catch (error) {
    console.error('获取退款申请失败:', error)
    ElMessage.error('获取退款申请失败')
    refundDialogVisible.value = false
  } finally {
    refundLoading.value = false
  }
}

// 处理退款申请
const handleRefund = async () => {
  if (!refundHandleForm.adminRemark.trim()) {
    ElMessage.warning('请输入管理员备注')
    return
  }

  // 如果同意退款，检查实际退款金额
  if (refundHandleForm.approved) {
    if (!refundHandleForm.actualRefundAmount || refundHandleForm.actualRefundAmount <= 0) {
      ElMessage.warning('请输入有效的实际退款金额')
      return
    }
    if (refundHandleForm.actualRefundAmount > refundDetail.value.orderAmount) {
      ElMessage.warning('实际退款金额不能大于订单金额')
      return
    }
  }

  refundSubmitting.value = true
  try {
    const requestData = {
      refundRequestId: refundDetail.value.id,
      approved: refundHandleForm.approved,
      adminRemark: refundHandleForm.adminRemark
    }

    // 只有在同意退款时才传递实际退款金额
    if (refundHandleForm.approved) {
      requestData.actualRefundAmount = refundHandleForm.actualRefundAmount
    }

    const response = await reviewRefundRequest(requestData)

    if (response.code === 1) {
      ElMessage.success('退款申请审核成功')
      refundDialogVisible.value = false
      fetchOrderDetail() // 刷新订单详情
    } else {
      ElMessage.error(response.msg || '审核退款申请失败')
    }
  } catch (error) {
    console.error('处理退款申请失败:', error)
    ElMessage.error('处理退款申请失败')
  } finally {
    refundSubmitting.value = false
  }
}

// 获取订单评论
const fetchOrderReviews = async () => {
  reviewsLoading.value = true
  try {
    const response = await getOrderReviews(orderId.value, reviewsPage.value, reviewsPageSize.value)
    if (response.code === 1) {
      reviewsList.value = response.data.records || []
      reviewsTotal.value = response.data.total || 0
    } else {
      console.warn('获取订单评论失败:', response.msg)
      reviewsList.value = []
      reviewsTotal.value = 0
    }
  } catch (error) {
    console.error('获取订单评论失败:', error)
    reviewsList.value = []
    reviewsTotal.value = 0
  } finally {
    reviewsLoading.value = false
  }
}

// 删除评论
const handleDeleteReview = async (reviewId, reviewContent) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除这条评论吗？\n"${reviewContent.length > 50 ? reviewContent.substring(0, 50) + '...' : reviewContent}"`,
      '删除评论',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning',
        confirmButtonClass: 'el-button--danger'
      }
    )

    const response = await deleteReview(reviewId)
    if (response.code === 1) {
      ElMessage.success('评论删除成功')
      // 如果当前页没有数据了，回到上一页
      if (reviewsList.value.length === 1 && reviewsPage.value > 1) {
        reviewsPage.value--
      }
      fetchOrderReviews() // 刷新评论列表
    } else {
      ElMessage.error(response.msg || '删除评论失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除评论失败:', error)
      ElMessage.error('删除评论失败')
    }
  }
}

// 评论分页变化
const handleReviewPageChange = (page) => {
  reviewsPage.value = page
  fetchOrderReviews()
}

// 获取订单详情
const fetchOrderDetail = async () => {
  loading.value = true
  try {
    const res = await getOrderDetail(orderId.value)
    if (res.code === 1 && res.data) {
      orderInfo.value = res.data
      // 设置发货表单的订单号
      shipForm.orderNo = res.data.orderNumber
      // 获取订单评论
      fetchOrderReviews()
    } else {
      ElMessage.error(res.msg || '获取订单详情失败')
      orderInfo.value = null
    }
  } catch (error) {
    console.error('获取订单详情失败:', error)
    ElMessage.error('获取订单详情失败')
    orderInfo.value = null
  } finally {
    loading.value = false
  }
}

watch(
  () => route.params.id,
  (newId) => {
    if (newId) {
      orderId.value = newId
      fetchOrderDetail()
    }
  }
)

onMounted(() => {
  fetchOrderDetail()
})
</script>

<style scoped>
.order-detail-container {
  padding: 20px;
}

.operation-card {
  margin-bottom: 20px;
}

.operation-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.order-title {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: bold;
}

.status-tag {
  margin-left: 10px;
}

.info-card {
  margin-bottom: 20px;
}

.card-header {
  font-weight: bold;
}

.order-amount {
  margin-top: 20px;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.amount-item {
  margin-top: 10px;
  font-size: 14px;
}

.total {
  font-size: 16px;
  font-weight: bold;
}

.total-price {
  color: #f56c6c;
  font-size: 20px;
}

/* 退款申请详情样式 */
.refund-detail {
  max-height: 600px;
  overflow-y: auto;
}

.card-title {
  font-weight: 600;
  color: #303133;
}

.product-info {
  display: flex;
  align-items: flex-start;
  gap: 16px;
}

.product-image {
  width: 80px;
  height: 80px;
  border-radius: 8px;
  flex-shrink: 0;
}

.product-details {
  flex: 1;
}

.product-name {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 8px;
}

.product-specs {
  font-size: 14px;
  color: #909399;
  margin-bottom: 4px;
}

.product-specs span {
  margin-right: 16px;
}

.product-price,
.product-total {
  font-size: 14px;
  color: #606266;
  margin-bottom: 4px;
}

.more-items {
  font-size: 12px;
  color: #909399;
  margin-top: 8px;
}

.amount-hint {
  margin-left: 10px;
  font-size: 12px;
  color: #909399;
}

/* 评论相关样式 */
.review-count {
  font-size: 14px;
  color: #909399;
  font-weight: normal;
}

.reviews-list {
  max-height: 600px;
  overflow-y: auto;
}

.review-item {
  padding: 16px 0;
  border-bottom: 1px solid #f0f0f0;
}

.review-item:last-child {
  border-bottom: none;
}

.review-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.review-user {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.user-name {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.review-rating {
  display: flex;
  align-items: center;
}

.review-actions {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 8px;
}

.review-time {
  font-size: 12px;
  color: #909399;
}

.review-content {
  font-size: 14px;
  color: #606266;
  line-height: 1.6;
  margin-bottom: 12px;
  word-wrap: break-word;
  white-space: pre-wrap;
}

.review-images {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.review-image {
  width: 80px;
  height: 80px;
  border-radius: 8px;
  cursor: pointer;
}

.review-pagination {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}
</style>
