const app = getApp();
const request = require('../../../utils/request.js'); // 假设request.js在utils目录下

Page({
  data: {
    avatarUrl: '', // 用于显示头像的临时URL或后端返回的URL
    tempAvatarPath: '', // 用户选择的头像临时文件路径
    userInfo: {
      avatar: '',       
      name: '',
      gender: 'other', // male, female, other
      birthday: '',     // YYYY-MM-DD
      phone: '',
      region: '',
      enableRecommendation: true,
    },
    endDate: new Date().toISOString().split('T')[0], // 生日选择器的结束日期为今天
  },

  onLoad: function (options) {
    this.loadUserProfile();
  },

  onShow: function() {
    // 页面显示时检查登录状态
    const token = wx.getStorageSync('token');
    if (!token) {
      wx.redirectTo({
        url: '/pages/login/login'
      });
      return;
    }
  },

  loadUserProfile: function() {
    console.log('开始加载用户信息');
    wx.showLoading({
      title: '加载中...',
      mask: true
    });

    request.get('/user/info', {}, {
      success: (res) => {
        console.log('获取用户信息响应:', res);
        wx.hideLoading();
        if (res.data && res.data.code === 1) {
          const profile = res.data.data;
          console.log('用户信息数据:', profile);
          if (profile) {
            // 性别映射
            let mappedGender = 'other';
            if (profile.gender === '男') {
              mappedGender = 'male';
            } else if (profile.gender === '女') {
              mappedGender = 'female';
            }
            console.log('映射后的性别:', mappedGender);

            const userInfo = {
              name: profile.name || '',
              gender: mappedGender,
              birthday: profile.birthday ? profile.birthday.split(' ')[0] : '',
              phone: profile.phone || '',
              region: profile.region || '',
              enableRecommendation: profile.enableRecommendation === true,
              avatar: profile.avatar || ''
            };
            console.log('准备设置的用户信息:', userInfo);

            this.setData({
              userInfo: userInfo,
              avatarUrl: profile.avatar || '/images/default-avatar.png'
            }, () => {
              console.log('用户信息设置完成');
            });
          } else {
            console.log('profile为空');
            wx.showToast({
              title: '获取用户信息失败',
              icon: 'none'
            });
          }
        } else {
          console.log('响应code不为1:', res.data);
          wx.showToast({
            title: res.data.msg || '加载失败',
            icon: 'none'
          });
        }
      },
      fail: (err) => {
        console.error('请求失败:', err);
        wx.hideLoading();
        wx.showToast({
          title: '加载失败',
          icon: 'none'
        });
      }
    });
  },

  chooseAvatar: function() {
    wx.chooseMedia({
      count: 1,
      mediaType: ['image'],
      sourceType: ['album', 'camera'],
      sizeType: ['compressed'],
      success: (res) => {
        if (res.tempFiles && res.tempFiles.length > 0) {
          const tempFilePath = res.tempFiles[0].tempFilePath;
          this.setData({
            avatarUrl: tempFilePath,
            tempAvatarPath: tempFilePath
          });
        }
      }
    });
  },

  bindGenderChange: function(e) {
    this.setData({
      'userInfo.gender': e.detail.value
    });
  },

  bindBirthdayChange: function(e) {
    this.setData({
      'userInfo.birthday': e.detail.value
    });
  },

  bindRecommendationChange: function(e) {
    this.setData({
      'userInfo.enableRecommendation': e.detail.value
    });
  },

  onChooseAvatar: function(e) {
    const { avatarUrl } = e.detail;
    
    // 显示加载提示
    wx.showLoading({
      title: '上传中...',
      mask: true
    });

    // 先将文件上传到服务器
    wx.uploadFile({
      url: getApp().globalData.baseUrl + '/user/upload/image',
      filePath: avatarUrl,
      name: 'file',
      header: {
        'Authorization': wx.getStorageSync('token')
      },
      success: (res) => {
        const result = JSON.parse(res.data);
        if (result.code === 1) {
          // 上传成功，拼接完整的URL
          const baseUrl = getApp().globalData.baseUrl;
          const imageUrl = baseUrl + result.data;
          console.log('完整的头像URL:', imageUrl);
          
          this.setData({
            avatarUrl: imageUrl,
            'userInfo.avatar': imageUrl
          });

          // 立即更新用户信息，发送完整路径
          this.updateAvatar(imageUrl);
        } else {
          wx.showToast({
            title: result.msg || '上传失败',
            icon: 'none'
          });
        }
      },
      fail: (err) => {
        console.error('上传失败:', err);
        wx.showToast({
          title: '上传失败',
          icon: 'none'
        });
      },
      complete: () => {
        wx.hideLoading();
      }
    });
  },

  // 更新头像信息
  updateAvatar: function(avatarUrl) {
    const data = {
      avatar: avatarUrl
    };

    request.put('/user/profile', data, {
      success: (res) => {
        if (res.data && res.data.code === 1) {
          wx.showToast({
            title: '更新成功',
            icon: 'success'
          });
          
          // 更新全局用户信息
          if (app.globalData.userInfo) {
            app.globalData.userInfo.avatar = avatarUrl;
          }

          // 设置需要刷新的标志
          getApp().globalData.needRefreshUserInfo = true;
        } else {
          wx.showToast({
            title: res.data.msg || '更新失败',
            icon: 'none'
          });
        }
      },
      fail: (err) => {
        console.error('更新失败:', err);
        wx.showToast({
          title: '更新失败',
          icon: 'none'
        });
      }
    });
  },

  onChooseNickname: function(e) {
    const { nickname } = e.detail;
    if (nickname) {
      this.setData({
        'userInfo.name': nickname
      });
    }
  },

  onNameInput: function(e) {
    this.setData({
      'userInfo.name': e.detail.value
    });
  },

  saveProfile: function(e) {
    const formData = e.detail.value;
    
    // 构建请求数据
    let apiPayload = {
      name: this.data.userInfo.name || '',
      gender: this.data.userInfo.gender,
      birthday: this.data.userInfo.birthday || '',
      phone: formData.phone || '',
      region: formData.region || '',
      enableRecommendation: this.data.userInfo.enableRecommendation
    };

    wx.showLoading({
      title: '保存中...',
      mask: true
    });

    this.submitProfileData(apiPayload);
  },

  submitProfileData: function(apiData) {
    // 性别转换回后端期望的格式
    let genderToSend = '未知';
    if (apiData.gender === 'male') {
      genderToSend = '男';
    } else if (apiData.gender === 'female') {
      genderToSend = '女';
    }

    // 构建符合后端期望的数据格式
    const dataToSend = {
      ...apiData,
      gender: genderToSend,
      birthday: apiData.birthday || null
    };

    request.put('/user/profile', dataToSend, {
      success: (res) => {
        if (res.data && res.data.code === 1) {
          wx.showToast({
            title: '保存成功',
            icon: 'success'
          });
          
          // 更新全局用户信息
          if (app.globalData.userInfo) {
            app.globalData.userInfo = {
              ...app.globalData.userInfo,
              ...dataToSend
            };
          }
          
          // 设置需要刷新的标志
          getApp().globalData.needRefreshUserInfo = true;
          
          // 延迟返回上一页
          setTimeout(() => {
            wx.navigateBack();
          }, 1500);
        } else {
          wx.showToast({
            title: res.data.msg || '保存失败',
            icon: 'none'
          });
        }
      },
      fail: (err) => {
        wx.hideLoading();
        wx.showToast({
          title: '保存失败',
          icon: 'none'
        });
        console.error('保存用户信息失败:', err);
      }
    });
  }
}); 