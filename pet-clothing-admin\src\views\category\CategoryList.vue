<template>
  <div class="category-list-container">
    <!-- 操作区域 -->
    <el-card class="operation-card" shadow="never">
      <div class="operation-buttons">
        <el-button type="primary" @click="handleAdd">添加分类</el-button>
        <el-button type="danger" :disabled="!selectedCategories.length" @click="handleBatchDelete">批量删除</el-button>
      </div>
    </el-card>

    <!-- 分类列表 -->
    <el-card class="category-table-card" shadow="never">
      <el-table
        v-loading="loading"
        :data="categoryList"
        border
        style="width: 100%"
        @selection-change="handleSelectionChange"
        @expand-change="onExpandChange"
      >
        <el-table-column type="expand">
          <template #default="props">
            <el-table
              :data="subcategoryMap[props.row.id] || []"
              v-loading="subcategoryLoadingMap[props.row.id]"
              size="small"
              style="width: 90%; margin: 0 auto;"
              empty-text="暂无小类"
            >
              <el-table-column prop="id" label="小类ID" width="80" />
              <el-table-column prop="name" label="小类名称" min-width="120" />
              <el-table-column prop="status" label="状态" width="80" />
              <el-table-column prop="createdAt" label="创建时间" width="160" />
              <el-table-column label="操作" width="160">
                <template #default="scope">
                  <el-button
                    size="small"
                    type="success"
                    @click="handleSubcategoryStatus(scope.row)"
                  >
                    {{ scope.row.status === '启用' ? '禁用' : '启用' }}
                  </el-button>
                  <el-button
                    size="small"
                    type="danger"
                    @click="handleDeleteSubcategory(scope.row, props.row.id)"
                  >
                    删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </template>
        </el-table-column>
        <el-table-column type="selection" width="55" />
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="name" label="分类名称" min-width="180" />
        <el-table-column prop="createdAt" label="创建时间" width="180" />
        <el-table-column prop="status" label="状态" width="120">
          <template #default="scope">
            <el-tag :type="scope.row.status === '启用' ? 'success' : 'info'" effect="plain">
              {{ scope.row.status }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="250" fixed="right">
          <template #default="scope">
            <el-button type="primary" link @click="handleEdit(scope.row)">编辑</el-button>
            <el-button type="success" link @click="handleStatusChange(scope.row)">
              {{ scope.row.status === '启用' ? '禁用' : '启用' }}
            </el-button>
            <el-button type="danger" link @click="handleDelete(scope.row)">删除</el-button>
            <el-button type="info" link @click="openAddSubcategoryDialog(scope.row)">新增小类</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
    <el-dialog v-model="subcategoryDialogVisible" title="新增商品小类" width="400px">
    <el-form :model="subcategoryForm" :rules="subcategoryRules" ref="subcategoryFormRef" label-width="80px">
      <el-form-item label="小类名称" prop="name">
        <el-input v-model="subcategoryForm.name" placeholder="请输入小类名称" />
      </el-form-item>
      <el-form-item label="状态">
        <el-radio-group v-model="subcategoryForm.status">
          <el-radio label="启用">启用</el-radio>
          <el-radio label="禁用">禁用</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="subcategoryDialogVisible = false">取消</el-button>
      <el-button type="primary" @click="submitSubcategoryForm">保存</el-button>
    </template>
  </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, reactive, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getCategoryList, deleteCategory, updateCategoryStatus, addSubcategory, getSubcategoriesByCategoryId, updateSubcategoryStatus, deleteSubcategory } from '@/api/category'

const router = useRouter()
const route = useRoute()
const loading = ref(false)
const categoryList = ref([])
const selectedCategories = ref([])
const subcategoryDialogVisible = ref(false)
const subcategoryForm = reactive({
  categoryId: '',
  name: '',
  status: '启用'
})
const subcategoryFormRef = ref(null)
const subcategoryRules = {
  name: [
    { required: true, message: '请输入小类名称', trigger: 'blur' },
    { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' }
  ]
}
const subcategoryMap = ref({}) // { [categoryId]: 小类数组 }
const subcategoryLoadingMap = ref({}) // { [categoryId]: boolean }

// 监听路由参数变化
watch(
  () => route.query.t,
  () => {
    fetchCategoryList()
  }
)

// 获取分类列表
const fetchCategoryList = async () => {
  loading.value = true
  try {
    const response = await getCategoryList()
    if (response.code === 1) {
      categoryList.value = response.data || []
    } else {
      ElMessage.error(response.msg || '获取分类列表失败')
    }
  } catch (error) {
    console.error('获取分类列表失败:', error)
    ElMessage.error('获取分类列表失败')
  } finally {
    loading.value = false
  }
}

// 添加分类
const handleAdd = () => {
  router.push('/category/add')
}

// 编辑分类
const handleEdit = (row) => {
  router.push({
    path: '/category/edit',
    query: { id: row.id }
  })
}

// 删除分类
const handleDelete = (row) => {
  ElMessageBox.confirm('确定要删除该分类吗？删除后将无法恢复！', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      const response = await deleteCategory(row.id)
      if (response.code === 1) {
        ElMessage.success('删除成功')
        fetchCategoryList()
      } else {
        ElMessage.error(response.msg || '删除失败')
      }
    } catch (error) {
      console.error('删除分类失败:', error)
      ElMessage.error('删除分类失败')
    }
  }).catch(() => {})
}

// 批量删除
const handleBatchDelete = () => {
  if (selectedCategories.value.length === 0) {
    ElMessage.warning('请选择要删除的分类')
    return
  }
  
  ElMessageBox.confirm(`确定要删除选中的 ${selectedCategories.value.length} 个分类吗？删除后将无法恢复！`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      const deletePromises = selectedCategories.value.map(category => deleteCategory(category.id))
      const results = await Promise.all(deletePromises)
      
      // 检查是否所有删除操作都成功
      const allSuccess = results.every(res => res.code === 1)
      if (allSuccess) {
        ElMessage.success('批量删除成功')
        fetchCategoryList()
      } else {
        ElMessage.error('部分分类删除失败')
      }
    } catch (error) {
      console.error('批量删除分类失败:', error)
      ElMessage.error('批量删除分类失败')
    }
  }).catch(() => {})
}

// 更改分类状态
const handleStatusChange = async (row) => {
  const newStatus = row.status === '启用' ? '禁用' : '启用'
  
  ElMessageBox.confirm(`确定要${newStatus}该分类吗？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      const response = await updateCategoryStatus(row.id, newStatus)
      if (response.code === 1) {
        ElMessage.success(`分类已${newStatus}`)
        fetchCategoryList() // 刷新列表
      } else {
        ElMessage.error(response.msg || `${newStatus}失败`)
      }
    } catch (error) {
      console.error('更新分类状态失败:', error)
      ElMessage.error(`${newStatus}失败`)
    }
  }).catch(() => {})
}

// 表格选择变化
const handleSelectionChange = (selection) => {
  selectedCategories.value = selection
}

const openAddSubcategoryDialog = (category) => {
  subcategoryForm.categoryId = category.id
  subcategoryForm.name = ''
  subcategoryForm.status = '启用'
  subcategoryDialogVisible.value = true
}

const submitSubcategoryForm = () => {
  subcategoryFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        const res = await addSubcategory({
          categoryId: subcategoryForm.categoryId,
          name: subcategoryForm.name,
          status: subcategoryForm.status
        })
        if (res.code === 1) {
          ElMessage.success('新增小类成功')
          subcategoryDialogVisible.value = false
          // 新增成功后刷新该大类下的小类列表
          subcategoryMap.value[subcategoryForm.categoryId] = undefined
          loadSubcategories(subcategoryForm.categoryId)
        } else {
          ElMessage.error(res.msg || '新增小类失败')
        }
      } catch (e) {
        ElMessage.error('新增小类失败')
      }
    }
  })
}

const loadSubcategories = async (categoryId) => {
  if (subcategoryMap.value[categoryId]) return // 已加载过
  subcategoryLoadingMap.value[categoryId] = true
  try {
    const res = await getSubcategoriesByCategoryId(categoryId)
    if (res.code === 1) {
      subcategoryMap.value[categoryId] = res.data || []
    } else {
      ElMessage.error(res.msg || '获取小类失败')
    }
  } catch (e) {
    ElMessage.error('获取小类失败')
  } finally {
    subcategoryLoadingMap.value[categoryId] = false
  }
}

const onExpandChange = (row, expandedRows) => {
  if (row && !subcategoryMap.value[row.id]) {
    loadSubcategories(row.id)
  }
}

// 启用/禁用小类
const handleSubcategoryStatus = (subcategory) => {
  const newStatus = subcategory.status === '启用' ? '禁用' : '启用'
  ElMessageBox.confirm(
    `确定要${newStatus}该小类吗？`,
    '提示',
    { confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning' }
  ).then(async () => {
    try {
      const res = await updateSubcategoryStatus(subcategory.id, newStatus)
      if (res.code === 1) {
        ElMessage.success(`小类已${newStatus}`)
        // 刷新该大类下的小类列表
        subcategoryMap.value[subcategory.categoryId] = undefined
        loadSubcategories(subcategory.categoryId)
      } else {
        ElMessage.error(res.msg || `${newStatus}失败`)
      }
    } catch (e) {
      ElMessage.error(`${newStatus}失败`)
    }
  }).catch(() => {})
}

// 删除小类
const handleDeleteSubcategory = (subcategory, categoryId) => {
  ElMessageBox.confirm(
    '确定要删除该小类吗？删除后将无法恢复！',
    '提示',
    { confirmButtonText: '确定', cancelButtonText: '取消', type: 'warning' }
  ).then(async () => {
    try {
      const res = await deleteSubcategory(subcategory.id)
      if (res.code === 1) {
        ElMessage.success('删除成功')
        // 刷新该大类下的小类列表
        subcategoryMap.value[categoryId] = undefined
        loadSubcategories(categoryId)
      } else {
        ElMessage.error(res.msg || '删除失败')
      }
    } catch (e) {
      ElMessage.error('删除失败')
    }
  }).catch(() => {})
}

onMounted(() => {
  fetchCategoryList()
})
</script>

<style scoped>
.category-list-container {
  padding: 20px;
}

.operation-card {
  margin-bottom: 20px;
}

.operation-buttons {
  display: flex;
  gap: 10px;
}

.category-table-card {
  margin-bottom: 20px;
}
</style>
