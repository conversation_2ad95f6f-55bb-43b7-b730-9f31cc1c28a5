<!--pages/category/category.wxml-->
<view class="container">
  <!-- 搜索栏 -->
  <view class="search-container">
    <view class="search-box">
      <icon type="search" size="14" color="#999"></icon>
      <input
        type="text"
        placeholder="搜索宠物服饰"
        confirm-type="search"
        value="{{searchKeyword}}"
        bindinput="onSearchInput"
        bindconfirm="onSearchConfirm"
        focus="{{searchFocus}}"
      />
      <!-- 保留清除按钮功能，但在搜索框中显示 -->
      <view class="search-clear" wx:if="{{searchKeyword}}" bindtap="clearSearch">
        <icon type="clear" size="14" color="#999"></icon>
      </view>
    </view>
  </view>

  <!-- 隐藏的清除搜索按钮，用于代码调用但不显示在界面上 -->
  <view class="hidden-clear-button" bindtap="clearSearch" style="display: none;"></view>

  <!-- 分类内容 -->
  <view class="category-content {{isSearching ? 'searching' : ''}}">
    <!-- 左侧主分类 - 仅在非搜索状态显示 -->
    <scroll-view scroll-y class="main-category" wx:if="{{!isSearching}}">
      <!-- 加载中提示 -->
      <view class="loading-container" wx:if="{{loading}}">
        <view class="loading-icon"></view>
        <text class="loading-text">加载中...</text>
      </view>

      <!-- 分类列表 -->
      <view
        class="category-item {{activeCategory === item.id ? 'active' : ''}}"
        wx:for="{{categories}}"
        wx:key="id"
        bindtap="switchCategory"
        data-id="{{item.id}}"
      >
        <text>{{item.name}}</text>
      </view>

      <!-- 空状态提示 -->
      <view class="empty-container" wx:if="{{!loading && categories.length === 0}}">
        <text class="empty-text">暂无分类数据</text>
      </view>
    </scroll-view>

    <!-- 右侧内容 -->
    <scroll-view scroll-y class="category-detail {{isSearching ? 'full-width' : ''}}">
      <!-- 搜索结果提示 - 只显示信息，不显示清除按钮 -->
      <view class="search-result-tip" wx:if="{{isSearching && products.length > 0}}">
        <text class="search-result-text">找到"{{searchKeyword}}"相关商品{{products.length}}个</text>
      </view>

      <!-- 子分类 - 仅在非搜索状态显示 -->
      <view class="sub-category" wx:if="{{subCategories.length > 0 && !isSearching}}">
        <view
          class="sub-category-item {{activeSubCategory === item.id ? 'active' : ''}}"
          wx:for="{{subCategories}}"
          wx:key="id"
          bindtap="switchSubCategory"
          data-id="{{item.id}}"
        >
          <text>{{item.name}}</text>
        </view>
      </view>

      <!-- 子分类空状态 - 仅在非搜索状态显示 -->
      <view class="empty-sub-category" wx:if="{{!loading && !isSearching && activeCategory && subCategories.length === 0}}">
        <text class="empty-text">暂无子分类数据</text>
      </view>

      <!-- 商品列表 -->
      <view class="product-list" wx:if="{{products.length > 0}}">
        <view
          class="product-item"
          wx:for="{{products}}"
          wx:key="id"
          bindtap="navigateToDetail"
          data-id="{{item.id}}"
        >
          <image src="{{item.imageUrl}}" mode="aspectFill" class="product-image" />
          <view class="product-info">
            <text class="product-name">{{item.name}}</text>
            <view class="product-price-container">
              <text class="product-price">¥{{item.price}}</text>
              <text class="product-original-price">¥{{item.originalPrice}}</text>
            </view>
            <text class="product-sales">已售{{item.sales}}件</text>
            <text class="product-category" wx:if="{{item.subcategoryName}}">{{item.subcategoryName}}</text>
          </view>
        </view>
      </view>

      <!-- 商品列表空状态 -->
      <view class="empty-products" wx:if="{{!loading && activeCategory && subCategories.length > 0 && products.length === 0 && !isSearching}}">
        <text class="empty-text">暂无商品数据</text>
      </view>

      <!-- 搜索结果为空状态 -->
      <view class="empty-products" wx:if="{{!loading && isSearching && products.length === 0}}">
        <text class="empty-text">未找到"{{searchKeyword}}"相关商品</text>
      </view>

      <!-- 底部搜索结果提示已移除 -->
    </scroll-view>
  </view>
</view>
