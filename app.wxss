/**app.wxss**/
page {
  height: 100%;
  font-size: 28rpx;
  color: #333;
  background-color: #f8f8f8;
  font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica,
    Segoe UI, Arial, Roboto, 'PingFang SC', 'miui', 'Hiragino Sans GB', 'Microsoft Yahei',
    sans-serif;
}

.container {
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
}

/* 清除浮动 */
.clearfix:after {
  content: '';
  display: block;
  clear: both;
}

/* 主题色 */
.theme-color {
  color: #ff6b81;
}

.theme-bg-color {
  background-color: #ff6b81;
}

/* 按钮样式 */
.btn {
  display: inline-block;
  padding: 20rpx 40rpx;
  border-radius: 10rpx;
  font-size: 28rpx;
  text-align: center;
}

.btn-primary {
  background-color: #ff6b81;
  color: #fff;
}

.btn-outline {
  border: 1rpx solid #ff6b81;
  color: #ff6b81;
  background-color: transparent;
}

/* 常用边距 */
.m-10 { margin: 10rpx; }
.m-20 { margin: 20rpx; }
.mt-10 { margin-top: 10rpx; }
.mt-20 { margin-top: 20rpx; }
.mb-10 { margin-bottom: 10rpx; }
.mb-20 { margin-bottom: 20rpx; }
.ml-10 { margin-left: 10rpx; }
.ml-20 { margin-left: 20rpx; }
.mr-10 { margin-right: 10rpx; }
.mr-20 { margin-right: 20rpx; }

/* 常用内边距 */
.p-10 { padding: 10rpx; }
.p-20 { padding: 20rpx; }
.pt-10 { padding-top: 10rpx; }
.pt-20 { padding-top: 20rpx; }
.pb-10 { padding-bottom: 10rpx; }
.pb-20 { padding-bottom: 20rpx; }
.pl-10 { padding-left: 10rpx; }
.pl-20 { padding-left: 20rpx; }
.pr-10 { padding-right: 10rpx; }
.pr-20 { padding-right: 20rpx; }

/* 文本对齐 */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }
