<template>
  <div class="product-add-container">
    <el-card class="form-card" shadow="never" v-loading="submitting || loading">
      <template #header>
        <div class="card-header">
          <span>添加商品</span>
        </div>
      </template>
      
      <el-form
        ref="productFormRef"
        :model="productForm"
        :rules="productRules"
        label-width="120px"
        label-position="right"
      >
        <!-- 基本信息 -->
        <el-divider content-position="left">基本信息</el-divider>
        
        <el-form-item label="商品名称" prop="name">
          <el-input v-model="productForm.name" placeholder="请输入商品名称" />
        </el-form-item>
        
        <el-form-item label="商品子分类" prop="subcategoryId">
          <el-cascader
            v-model="productForm.subcategoryId"
            :options="categoryOptions"
            :props="{
              value: 'value',
              label: 'label',
              children: 'children',
              checkStrictly: true,
              emitPath: false
            }"
            @change="handleCategoryChange"
          />
        </el-form-item>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="商品原价" prop="originalPrice">
              <el-input-number
                v-model="productForm.originalPrice"
                :min="0"
                :precision="2"
                :step="0.1"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="折扣率" prop="discountRate">
              <el-input-number
                v-model="productForm.discountRate"
                :min="0"
                :max="1"
                :precision="2"
                :step="0.01"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
        
        <!-- 商品图片 -->
        <el-divider content-position="left">商品图片</el-divider>
        
        <el-form-item label="主图" prop="imageUrl">
          <el-upload
            class="product-image-uploader"
            :action="UPLOAD_URL"
            :headers="uploadHeaders"
            :show-file-list="false"
            :on-success="handleMainImageSuccess"
            :before-upload="beforeImageUpload"
          >
            <img v-if="productForm.imageUrl" :src="productForm.imageUrl" class="product-image" />
            <el-icon v-else class="product-image-uploader-icon"><Plus /></el-icon>
          </el-upload>
        </el-form-item>
        
        <el-form-item label="详情图集" prop="detailImages">
          <el-upload
            class="product-images-uploader"
            :action="UPLOAD_URL"
            :headers="uploadHeaders"
            list-type="picture-card"
            :file-list="productForm.detailImages.map(url => ({url}))"
            :on-success="handleImagesSuccess"
            :on-remove="handleImagesRemove"
            :before-upload="beforeImageUpload"
          >
            <el-icon><Plus /></el-icon>
          </el-upload>
        </el-form-item>
        
        <!-- 商品详情 -->
        <el-divider content-position="left">商品详情</el-divider>
        
        <el-form-item label="商品描述" prop="description">
          <el-input
            v-model="productForm.description"
            type="textarea"
            :rows="6"
            placeholder="请输入商品描述"
          />
        </el-form-item>
        
        <!-- 其他设置 -->
        <el-divider content-position="left">其他设置</el-divider>
        
        <el-form-item label="商品状态">
          <el-radio-group v-model="productForm.status">
            <el-radio label="上架">上架</el-radio>
            <el-radio label="下架">下架</el-radio>
          </el-radio-group>
        </el-form-item>
        
        <el-form-item label="是否热门">
          <el-switch
            v-model="productForm.isHot"
            :active-value="true"
            :inactive-value="false"
            active-text="是"
            inactive-text="否"
          />
        </el-form-item>

        <el-form-item label="是否推荐">
          <el-switch
            v-model="productForm.isRecommended"
            :active-value="true"
            :inactive-value="false"
            active-text="是"
            inactive-text="否"
          />
        </el-form-item>

        <!-- SKU设置 -->
        <el-divider content-position="left">
          <div class="divider-content">
            <span class="divider-title">SKU设置</span>
            <el-tooltip
              effect="dark"
              placement="right"
            >
              <template #content>
                <div>
                  <p>SKU是商品的最小可售单元，例如：</p>
                  <p>- 同一件衣服的不同颜色和尺码组合</p>
                  <p>- 同一个商品的不同规格</p>
                </div>
              </template>
              <el-icon class="divider-icon"><QuestionFilled /></el-icon>
            </el-tooltip>
          </div>
        </el-divider>

        <div v-if="productForm.skuList.length === 0" class="empty-sku-tip">
          <el-empty description="暂无SKU信息">
            <el-button type="primary" @click="addSku">
              <el-icon><Plus /></el-icon>添加第一个SKU
            </el-button>
          </el-empty>
        </div>

        <div v-else class="sku-list">
          <div v-for="(sku, index) in productForm.skuList" :key="index" class="sku-item">
            <el-card class="sku-card" shadow="hover">
              <template #header>
                <div class="sku-header">
                  <div class="sku-header-left">
                    <span class="sku-title">SKU {{ index + 1 }}</span>
                    <el-tag size="small" type="info" class="sku-tag">
                      {{ Object.keys(sku.attributes).length }}个属性
                    </el-tag>
                  </div>
                  <el-button type="danger" link @click="removeSku(index)">
                    <el-icon><Delete /></el-icon>删除
                  </el-button>
                </div>
              </template>

              <div class="sku-section">
                <div class="section-header">
                  <span class="section-title">规格属性</span>
                  <el-tooltip content="商品的颜色和尺码规格" placement="top">
                    <el-icon><InfoFilled /></el-icon>
                  </el-tooltip>
                </div>
                
                <div class="sku-attributes">
                  <div class="attribute-item">
                    <el-input 
                      v-model="sku.attributeKeys.color"
                      class="attribute-name-input"
                      placeholder="属性名（如：颜色）"
                      disabled
                    />
                    <el-input 
                      v-model="sku.attributes.color"
                      class="attribute-value-input"
                      placeholder="属性值（如：红色）"
                    />
                  </div>
                  <div class="attribute-item">
                    <el-input 
                      v-model="sku.attributeKeys.size"
                      class="attribute-name-input"
                      placeholder="属性名（如：尺码）"
                      disabled
                    />
                    <el-input 
                      v-model="sku.attributes.size"
                      class="attribute-value-input"
                      placeholder="属性值（如：XL）"
                    />
                    <el-button 
                      type="primary" 
                      link 
                      @click="generateSizeSKUs(index)"
                      class="generate-sizes-btn"
                    >
                      <el-icon><SetUp /></el-icon>
                      一键生成尺码
                    </el-button>
                  </div>
                </div>
              </div>

              <div class="sku-section">
                <div class="section-header">
                  <span class="section-title">价格与库存</span>
                  <el-tooltip content="设置该SKU的销售价格和库存数量" placement="top">
                    <el-icon><InfoFilled /></el-icon>
                  </el-tooltip>
                </div>
                
                <el-row :gutter="20">
                  <el-col :span="12">
                    <el-form-item 
                      :prop="'skuList.' + index + '.price'"
                      label="销售价格"
                    >
                      <el-input-number
                        v-model="sku.price"
                        :min="0"
                        :precision="2"
                        :step="0.1"
                        style="width: 100%"
                      >
                        <template #prefix>￥</template>
                      </el-input-number>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item 
                      :prop="'skuList.' + index + '.stock'"
                      label="库存数量"
                    >
                      <el-input-number
                        v-model="sku.stock"
                        :min="0"
                        :precision="0"
                        :step="1"
                        style="width: 100%"
                      >
                        <template #prefix>
                          <el-icon><Goods /></el-icon>
                        </template>
                      </el-input-number>
                    </el-form-item>
                  </el-col>
                </el-row>
              </div>
            </el-card>
          </div>
        </div>

        <div class="sku-actions" v-if="productForm.skuList.length > 0">
          <el-button type="primary" plain @click="addSku">
            <el-icon><Plus /></el-icon>添加更多SKU
          </el-button>
          <el-text class="sku-count" type="info">
            已添加 {{ productForm.skuList.length }} 个SKU
          </el-text>
        </div>
        
        <!-- 提交按钮 -->
        <el-form-item>
          <el-button type="primary" :loading="submitting" @click="submitForm">保存</el-button>
          <el-button @click="goBack">取消</el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Plus, 
  Delete, 
  Document, 
  InfoFilled, 
  QuestionFilled,
  Goods,
  SetUp 
} from '@element-plus/icons-vue'
import { addProduct, batchAddSkus } from '@/api/product'
import { getCategoryList, getSubcategoriesByCategoryId } from '@/api/category'
import { BASE_URL, UPLOAD_URL } from '@/utils/config'

const router = useRouter()
const productFormRef = ref(null)
const submitting = ref(false)
const loading = ref(false)

// 上传图片的请求头
const uploadHeaders = computed(() => {
  const token = localStorage.getItem('token')
  // 根据您最新的请求头格式要求，这里使用token作为key
  return {
    'token': token
  }
})

// 商品表单
const productForm = reactive({
  name: '',
  subcategoryId: '',
  originalPrice: 0,
  discountRate: 0,
  imageUrl: '',
  detailImages: [],
  description: '',
  status: '上架',
  isHot: false,
  isRecommended: false,
  skuList: []
})

// 表单验证规则
const productRules = {
  name: [
    { required: true, message: '请输入商品名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  subcategoryId: [
    { required: true, message: '请选择商品子分类', trigger: 'change' }
  ],
  originalPrice: [
    { required: true, message: '请输入商品原价', trigger: 'blur' },
    { type: 'number', min: 0.01, message: '商品原价必须大于0', trigger: 'blur' }
  ],
  discountRate: [
    { required: true, message: '请输入折扣率', trigger: 'blur' },
    { type: 'number', min: 0, max: 1, message: '折扣率必须在 0 到 1 之间', trigger: 'blur' }
  ],
  imageUrl: [
    { required: true, message: '请上传商品主图', trigger: 'change' }
  ],
  description: [
    { required: true, message: '请输入商品描述', trigger: 'blur' }
  ],
  'skuList': [
    { required: true, message: '请至少添加一个SKU', trigger: 'change' },
    {
      type: 'array',
      validator: (rule, value, callback) => {
        if (value.length === 0) {
          callback(new Error('请至少添加一个SKU'))
          return
        }
        const errors = []
        value.forEach((sku, index) => {
          if (!sku.skuCode) {
            errors.push(`SKU ${index + 1} 的编码不能为空`)
          }
          if (!sku.price || sku.price <= 0) {
            errors.push(`SKU ${index + 1} 的价格必须大于0`)
          }
          if (typeof sku.stock !== 'number' || sku.stock < 0) {
            errors.push(`SKU ${index + 1} 的库存不能小于0`)
          }
          if (Object.keys(sku.attributes).length === 0) {
            errors.push(`SKU ${index + 1} 至少需要一个属性`)
          }
        })
        if (errors.length) {
          callback(new Error(errors.join('\n')))
        } else {
          callback()
        }
      },
      trigger: 'change'
    }
  ]
}

// 分类选项
const categoryOptions = ref([])

// 获取分类列表 (获取所有大类及小类)
const fetchCategories = async () => {
  try {
    // 1. 获取所有一级分类
    const categoriesResponse = await getCategoryList()
    if (categoriesResponse.code === 1 && categoriesResponse.data) {
      const topLevelCategories = categoriesResponse.data
      const categoryTree = []

      // 2. 遍历一级分类，获取其下的二级分类
      for (const category of topLevelCategories) {
        const subcategoriesResponse = await getSubcategoriesByCategoryId(category.id)
        let children = []
        if (subcategoriesResponse.code === 1 && subcategoriesResponse.data) {
          children = subcategoriesResponse.data.map(sub => ({
            value: sub.id,
            label: sub.name,
            leaf: true // 二级分类是叶子节点
          }))
        }

        categoryTree.push({
          value: category.id,
          label: category.name,
          children: children, // 添加二级分类数组
          leaf: children.length === 0 // 如果没有二级分类，则一级分类也是叶子节点
        })
      }
      categoryOptions.value = categoryTree

    } else {
      ElMessage.error(categoriesResponse.msg || '获取一级分类失败')
    }
  } catch (error) {
    console.error('获取分类列表失败:', error)
    ElMessage.error('获取分类列表失败')
  } finally {
  }
}

// 处理主图上传成功
const handleMainImageSuccess = (response) => {
  if (response.code === 1 && response.data) {
    // 拼接完整的图片地址
    productForm.imageUrl = `${BASE_URL}${response.data}`
    // 触发表单验证
    productFormRef.value.validateField('imageUrl')
  } else {
    ElMessage.error('主图上传失败')
  }
}

// 处理详情图上传成功
const handleImagesSuccess = (response) => {
  if (response.code === 1 && response.data) {
    // 拼接完整的图片地址
    productForm.detailImages.push(`${BASE_URL}${response.data}`)
  } else {
    ElMessage.error('详情图上传失败')
  }
}

// 处理详情图移除
const handleImagesRemove = (uploadFile, uploadFiles) => {
  // 根据uploadFile的url找到对应的detailImages项并移除
  const index = productForm.detailImages.findIndex(url => url === uploadFile.url)
  if (index !== -1) {
    productForm.detailImages.splice(index, 1)
  }
}

// 上传图片前的检查
const beforeImageUpload = (rawFile) => {
  const isJPG = rawFile.type === 'image/jpeg' || rawFile.type === 'image/png' || rawFile.type === 'image/gif'
  const isLt2M = rawFile.size / 1024 / 1024 < 2

  if (!isJPG) {
    ElMessage.error('图片只能是 JPG/PNG/GIF 格式!')
    return false
  }
  if (!isLt2M) {
    ElMessage.error('图片大小不能超过 2MB!')
    return false
  }
  return true
}

// 添加新的SKU
const addSku = () => {
  const defaultAttributes = {
    'color': {
      key: '颜色',
      value: ''
    },
    'size': {
      key: '尺码',
      value: ''
    }
  }

  productForm.skuList.push({
    skuCode: '',
    attributes: {
      color: '',
      size: ''
    },
    attributeKeys: {
      color: '颜色',
      size: '尺码'
    },
    price: 0,
    stock: 0
  })
}

// 移除SKU
const removeSku = (index) => {
  productForm.skuList.splice(index, 1)
}

// 移除指定SKU的属性
const removeAttribute = (skuIndex, key) => {
  // 不允许删除默认属性
  ElMessage.warning('默认规格属性不可删除')
}

// 默认尺码列表
const DEFAULT_SIZES = ['S', 'M', 'L', 'XL']

// 生成多尺码SKU
const generateSizeSKUs = async (currentSkuIndex) => {
  const currentSku = productForm.skuList[currentSkuIndex]
  const color = currentSku.attributes.color

  if (!color) {
    ElMessage.warning('请先填写颜色信息')
    return
  }

  // 确认是否生成
  try {
    await ElMessageBox.confirm(
      `将为颜色"${color}"生成 ${DEFAULT_SIZES.join('、')} 四个尺码的SKU，是否继续？`,
      '生成确认',
      {
        confirmButtonText: '确认生成',
        cancelButtonText: '取消',
        type: 'info'
      }
    )

    // 删除当前SKU
    productForm.skuList.splice(currentSkuIndex, 1)

    // 为每个尺码创建新的SKU
    DEFAULT_SIZES.forEach((size) => {
      productForm.skuList.push({
        skuCode: `${color}-${size}`,
        attributes: {
          color: color,
          size: size
        },
        attributeKeys: {
          color: '颜色',
          size: '尺码'
        },
        price: currentSku.price || 0,
        stock: currentSku.stock || 0
      })
    })

    ElMessage.success(`已成功生成 ${DEFAULT_SIZES.length} 个不同尺码的SKU`)
  } catch (error) {
    // 用户取消操作
    return
  }
}

// 提交表单
const submitForm = async () => {
  await productFormRef.value.validate(async (valid) => {
    if (valid) {
      submitting.value = true
      try {
        const response = await addProduct(productForm)

        if (response.code === 1) {
          ElMessage.success('添加商品成功')

          // 如果是添加商品，则批量添加SKU
          if (productForm.skuList && productForm.skuList.length > 0) {
            const productId = response.data // 商品ID在响应数据data中
            try {
              const skuResponse = await batchAddSkus({
                productId: productId,
                skus: productForm.skuList.map(sku => ({
                  attributes: sku.attributes,
                  price: sku.price,
                  productId: productId, // SKU中的productId也设置为新商品的ID
                  skuCode: sku.skuCode,
                  stock: sku.stock
                }))
              })

              if (skuResponse.code === 1) {
                ElMessage.success('SKU批量添加成功')
              } else {
                ElMessage.error(skuResponse.msg || 'SKU批量添加失败')
              }
            } catch (skuError) {
              console.error('SKU批量添加失败:', skuError)
              ElMessage.error('SKU批量添加失败')
            }
          }

          router.push('/product')
        } else {
          ElMessage.error(response.msg || '添加商品失败')
        }
      } catch (error) {
        console.error('添加商品失败:', error)
        ElMessage.error('添加商品失败')
      } finally {
        submitting.value = false
      }
    }
  })
}

// 返回上一页
const goBack = () => {
  router.back()
}

onMounted(() => {
  fetchCategories() // 在组件挂载时获取分类列表
})

// 处理级联选择器选中值变化
// 如果 emitPath 为 false 且 checkStrictly 为 true，change 事件的参数是选中节点的 value
// 如果你需要获取选中节点的完整路径，可以监听 expand-change 或其他事件，并遍历 nodes
const handleCategoryChange = (value) => {
  // value 现在是选中的分类（子分类）ID
  // productForm.subcategoryId 已经被 el-cascader 自动更新为选中的 value
  // console.log('Selected Category ID:', value);
}

</script>

<style scoped>
.product-add-container {
  padding: 20px;
}

.form-card {
  max-width: 800px;
  margin: 0 auto;
}

.product-image-uploader .el-upload {
  border: 1px dashed var(--el-border-color-darker);
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: var(--el-transition-duration-fast);
}

.product-image-uploader .el-upload:hover {
  border-color: var(--el-color-primary);
}

.el-icon.product-image-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  text-align: center;
}

.product-image {
  width: 178px;
  height: 178px;
  display: block;
  object-fit: cover;
}

.form-item-tip {
  color: #909399;
  font-size: 12px;
  line-height: 1.5;
  margin-top: 4px;
}

.divider-content {
  display: flex;
  align-items: center;
  gap: 8px;
}

.divider-title {
  font-size: 16px;
  font-weight: 500;
}

.divider-icon {
  cursor: pointer;
  color: #909399;
}

.empty-sku-tip {
  padding: 40px 0;
  text-align: center;
}

.sku-card {
  margin-bottom: 16px;
  border: 1px solid #ebeef5;
}

.sku-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.sku-header-left {
  display: flex;
  align-items: center;
  gap: 8px;
}

.sku-title {
  font-size: 16px;
  font-weight: 500;
}

.sku-tag {
  margin-left: 8px;
}

.sku-section {
  margin: 20px 0;
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 4px;
}

.section-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 16px;
}

.section-title {
  font-size: 14px;
  font-weight: 500;
  color: #606266;
}

.sku-attributes {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.attribute-item {
  display: flex;
  align-items: center;
  gap: 10px;
  width: 100%;
}

.attribute-name-input {
  width: 180px !important;
  flex-shrink: 0;
}

.attribute-value-input {
  width: 180px !important;
  flex-shrink: 0;
}

.add-attribute-btn {
  margin-top: 8px;
}

.sku-actions {
  display: flex;
  align-items: center;
  gap: 16px;
  margin: 20px 0;
}

.sku-count {
  color: #909399;
}

.attribute-name-input :deep(.el-input__inner) {
  background-color: #f5f7fa;
  cursor: not-allowed;
}

.generate-sizes-btn {
  margin-left: 8px;
}
</style>
